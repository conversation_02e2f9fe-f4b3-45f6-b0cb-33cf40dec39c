const util = require("../../utils/util")
const api = require("../../config/api")
const userUtils = require("../../utils/user")
const app = getApp()
const systemInfoService = require('../../services/systemInfo.js')

Page({
  data: {
    // 常量配置
    MAX_UPLOAD_COUNT: 5,
    MAX_CONTENT_LENGTH: 500,

    // 表单数据
    formData: {
      content: '',
      contact: ''
    },

    // 图片上传
    fileList: [],

    // 页面状态
    submitting: false,
    formValid: false,
    lastSubmitTime: null
  },

  onLoad(options) {
    console.log('反馈页面加载，参数:', options)
  },

  onShow() {
    this.validateForm()
  },

  // 内容输入
  onContentInput(e) {
    this.setData({
      'formData.content': e.detail.value
    }, () => {
      this.validateForm()
    })
  },

  // 联系方式输入
  onContactInput(e) {
    this.setData({
      'formData.contact': e.detail.value
    })
  },

  // 图片上传后处理
  async afterRead(event) {
    const { file } = event.detail;
    // 转换为数组处理，支持单文件和多文件
    const files = Array.isArray(file) ? file : [file];

    // 检查文件总数量限制
    if (this.data.fileList.length + files.length > this.data.MAX_UPLOAD_COUNT) {
      wx.showToast({
        title: `最多上传${this.data.MAX_UPLOAD_COUNT}张图片`,
        icon: 'none'
      });
      return;
    }

    wx.showLoading({
      title: '上传中...',
      mask: true
    });

    try {
      // 使用 Promise.all 同时上传多个文件
      const uploadTasks = files.map(async (file) => {
        const uploadedFile = await util.uploadFile({
          filePath: file.url,
          type: '4' // 通用文件类型
        });

        if (!uploadedFile || !uploadedFile.filePath) {
          throw new Error('文件上传失败，未获取到文件路径');
        }

        return {
          url: file.url,
          name: '反馈图片',
          isImage: true,
          filePath: uploadedFile.filePath
        };
      });

      const uploadedFiles = await Promise.all(uploadTasks);

      // 更新文件列表，添加所有上传成功的文件
      this.setData({
        fileList: [...this.data.fileList, ...uploadedFiles]
      });

      this.validateForm();

    } catch (error) {
      wx.showToast({
        title: error.message || '上传失败，请重试',
        icon: 'none'
      });
    } finally {
      wx.hideLoading();
    }
  },

  // 删除图片
  onDeleteFile(event) {
    const { index } = event.detail;
    if (index < 0 || index >= this.data.fileList.length) return;

    const fileList = this.data.fileList.filter((_, idx) => idx !== index);

    this.setData({
      fileList
    });
    this.validateForm();
  },

  // 图片大小超限处理
  onOversize() {
    wx.showToast({
      title: '图片大小不能超过5MB',
      icon: 'none'
    })
  },

  // 表单验证
  validateForm() {
    const { content } = this.data.formData
    const isValid = content.trim().length > 0

    this.setData({
      formValid: isValid
    })
  },

  // 处理表单提交
  async handleSubmit(e) {
    // 防重复提交检查
    if (this.data.submitting) {
      return
    }

    // 防抖检查 - 1秒内不允许重复点击
    const now = Date.now()
    if (this.data.lastSubmitTime && (now - this.data.lastSubmitTime) < 1000) {
      wx.showToast({
        title: '请勿频繁点击',
        icon: 'none',
        duration: 1000
      })
      return
    }
    this.data.lastSubmitTime = now

    // 表单验证
    if (!this.data.formData.content.trim()) {
      wx.showToast({
        title: '请输入反馈内容',
        icon: 'none'
      })
      return
    }

    try {
      this.setData({
        submitting: true
      })

      wx.showLoading({
        title: '提交中...'
      })

      const feedbackData = {
        content: this.data.formData.content.trim(),
        contact: this.data.formData.contact.trim(),
        images: this.data.fileList.map(f => f.filePath).join(',')
      }

      const res = await api.submitFeedback(feedbackData)

      wx.hideLoading()

      if (res.code === 200) {
        wx.showToast({
          title: '反馈提交成功',
          success: () => {
            setTimeout(() => {
              wx.navigateBack()
            }, 1500)
          }
        })
      } else {
        throw new Error(res.msg || '提交失败')
      }
    } catch (error) {
      wx.hideLoading()
      console.error('提交反馈失败:', error)

      wx.showToast({
        title: error.message || '提交失败，请重试',
        icon: 'none'
      })
    } finally {
      this.setData({
        submitting: false
      })
    }
  }
})
