package com.fs.swap.system.service.impl;

import java.util.List;
import java.util.Date;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.fs.swap.system.mapper.ActivitySignupMapper;
import com.fs.swap.system.mapper.ActivityMapper;
import com.fs.swap.common.core.domain.entity.Activity;
import com.fs.swap.common.core.domain.entity.ActivitySignup;
import com.fs.swap.common.enums.ActivityStatus;
import com.fs.swap.common.enums.SignupStatus;
import com.fs.swap.common.exception.ServiceException;
import com.fs.swap.system.service.IActivitySignupService;

/**
 * 活动报名Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-20
 */
@Service
public class ActivitySignupServiceImpl implements IActivitySignupService
{
    @Autowired
    private ActivitySignupMapper activitySignupMapper;

    @Autowired
    private ActivityMapper activityMapper;

    /**
     * 查询活动报名
     *
     * @param id 活动报名主键
     * @return 活动报名
     */
    @Override
    public ActivitySignup selectActivitySignupById(Long id)
    {
        return activitySignupMapper.selectActivitySignupById(id);
    }

    /**
     * 查询活动报名列表
     *
     * @param activitySignup 活动报名
     * @return 活动报名集合
     */
    @Override
    public List<ActivitySignup> selectActivitySignupList(ActivitySignup activitySignup)
    {
        return activitySignupMapper.selectActivitySignupList(activitySignup);
    }

    /**
     * 查询活动的报名列表
     *
     * @param activityId 活动ID
     * @return 活动报名集合
     */
    @Override
    public List<ActivitySignup> selectActivitySignupListByActivityId(Long activityId)
    {
        return activitySignupMapper.selectActivitySignupListByActivityId(activityId);
    }

    /**
     * 查询用户的报名列表
     *
     * @param userId 用户ID
     * @return 活动报名集合
     */
    @Override
    public List<ActivitySignup> selectActivitySignupListByUserId(Long userId)
    {
        return activitySignupMapper.selectActivitySignupListByUserId(userId);
    }

    /**
     * 查询用户是否已报名活动
     *
     * @param activityId 活动ID
     * @param userId 用户ID
     * @return 活动报名
     */
    @Override
    public ActivitySignup selectActivitySignupByActivityIdAndUserId(Long activityId, Long userId)
    {
        return activitySignupMapper.selectActivitySignupByActivityIdAndUserId(activityId, userId);
    }

    /**
     * 新增活动报名
     *
     * @param activitySignup 活动报名
     * @return 结果
     */
    @Override
    public int insertActivitySignup(ActivitySignup activitySignup)
    {
        return activitySignupMapper.insertActivitySignup(activitySignup);
    }

    /**
     * 修改活动报名
     *
     * @param activitySignup 活动报名
     * @return 结果
     */
    @Override
    public int updateActivitySignup(ActivitySignup activitySignup)
    {
        return activitySignupMapper.updateActivitySignup(activitySignup);
    }

    /**
     * 批量删除活动报名
     *
     * @param ids 需要删除的活动报名主键
     * @return 结果
     */
    @Override
    public int deleteActivitySignupByIds(Long[] ids)
    {
        return activitySignupMapper.deleteActivitySignupByIds(ids);
    }

    /**
     * 删除活动报名信息
     *
     * @param id 活动报名主键
     * @return 结果
     */
    @Override
    public int deleteActivitySignupById(Long id)
    {
        return activitySignupMapper.deleteActivitySignupById(id);
    }

    /**
     * 取消报名
     *
     * @param id 报名ID
     * @param userId 用户ID
     * @return 结果
     */
    @Override
    public int cancelSignup(Long id, Long userId)
    {
        // 检查报名是否存在
        ActivitySignup signup = selectActivitySignupById(id);
        if (signup == null) {
            throw new ServiceException("报名记录不存在");
        }

        // 检查是否是报名用户
        if (!signup.getUserId().equals(userId)) {
            throw new ServiceException("无权操作");
        }


        // 检查活动状态
        Activity activity = activityMapper.selectActivityById(signup.getActivityId());
        if (activity == null) {
            throw new ServiceException("活动不存在");
        }
        // 只有发布中的活动可以取消报名
        if (!ActivityStatus.PUBLISHED.getCode().equals(activity.getStatus())) {
            throw new ServiceException("活动不在发布中状态，无法取消报名");
        }
        //已经开始的活动不允许退出
        if(activity.getStartTime().before(new Date())) {
            throw new ServiceException("活动已开始，无法取消报名");
        }

        // 更新报名状态为已取消
        signup.setStatus(SignupStatus.CANCELED.getCode());
        signup.setUpdateTime(new Date());
        return updateActivitySignup(signup);
    }

    /**
     * 审核报名
     *
     * @param id 报名ID
     * @param status 状态
     * @param userId 操作用户ID
     * @return 结果
     */
    @Override
    public int auditSignup(Long id, String status, Long userId)
    {
        // 检查报名是否存在
        ActivitySignup signup = selectActivitySignupById(id);
        if (signup == null) {
            throw new ServiceException("报名记录不存在");
        }

        // 检查活动是否存在
        Activity activity = activityMapper.selectActivityById(signup.getActivityId());
        if (activity == null) {
            throw new ServiceException("活动不存在");
        }

        // 检查是否是活动发布者
        if (!activity.getUserId().equals(userId)) {
            throw new ServiceException("无权操作");
        }

        // 只有待审核的报名可以审核
        if (!SignupStatus.PENDING.getCode().equals(signup.getStatus())) {
            throw new ServiceException("只有待审核的报名可以审核");
        }

        // 更新报名状态
        signup.setStatus(status);
        signup.setUpdateTime(new Date());
        return updateActivitySignup(signup);
    }

    /**
     * 获取活动报名人数
     *
     * @param activityId 活动ID
     * @return 报名人数
     */
    @Override
    public int getActivitySignupCount(Long activityId)
    {
        return activitySignupMapper.getActivitySignupCount(activityId);
    }
}
