const util = require("../../utils/util")
const api = require("../../config/api")
const ContactType = require('../../utils/ContactType.js')
const systemInfoService = require('../../services/systemInfo.js')

Component({
  properties: {
    // 是否显示弹窗
    show: {
      type: Boolean,
      value: false
    },
    // 联系方式数据
    contactInfo: {
      type: Object,
      value: {
        mobile: '',
        wechatId: '',
        wechatQr: '',
        mobileVisible: false,
        wechatVisible: false,
        wechatQrVisible: false
      }
    }
  },

  data: {
    // 弹窗状态
    showWechatDialog: false,
    showWechatQrDialog: false,

    // 输入字段
    wechatField: '',
    tempQrCodePath: ''
  },

  methods: {
    // 关闭主弹窗
    onClose() {
      this.triggerEvent('close')
    },

    // 阻止事件冒泡
    preventTap() {
      // 阻止事件冒泡，防止点击开关时同时触发整个item的点击事件
    },



    // 手机号可见性变更
    async onMobileVisibilityChange(e) {
      const isVisible = e.detail
      const { contactInfo } = this.properties

      // 如果要设置为不可见，检查是否至少还有一个联系方式可见
      if (!isVisible) {
        const hasOtherVisible = (contactInfo.wechatId && contactInfo.wechatVisible) ||
          (contactInfo.wechatQr && contactInfo.wechatQrVisible)

        if (!hasOtherVisible) {
          wx.showToast({
            title: '至少需要一种联系方式',
            icon: 'none',
            duration: 2000
          })
          return
        }
      }

      try {
        // 调用API更新可见性
        const res = await api.updateContactVisibility({
          contactType: ContactType.MOBILE.code,
          isVisible: isVisible
        })

        if (res.code === 200) {
          const newContactInfo = {
            ...contactInfo,
            mobileVisible: isVisible
          }

          this.triggerEvent('contactChange', {
            contactInfo: newContactInfo,
            type: 'mobileVisibility'
          })
        } else {
          throw new Error(res.msg || '更新失败')
        }
      } catch (error) {
        console.error('更新手机号可见性失败:', error)
        wx.showToast({
          title: error.message || '更新失败',
          icon: 'none',
          duration: 2000
        })
      }
    },

    // 显示微信号编辑弹窗
    showWechatDialog() {
      this.setData({
        showWechatDialog: true,
        wechatField: this.properties.contactInfo.wechatId || ''
      })
    },

    // 关闭微信号编辑弹窗
    onCloseWechatDialog() {
      this.setData({
        showWechatDialog: false,
        wechatField: ''
      })
    },

    // 验证微信号
    validateWechatId(wechatId) {
      const regex = /^[a-zA-Z0-9_-]{6,20}$/
      if (!wechatId) {
        wx.showToast({
          title: '微信号不能为空',
          icon: 'none',
          duration: 2000
        })
        return false
      }
      if (!regex.test(wechatId)) {
        wx.showToast({
          title: '微信号格式不正确',
          icon: 'none',
          duration: 2000
        })
        return false
      }
      return true
    },

    // 确认修改微信号
    async onConfirmWechat() {
      if (!this.validateWechatId(this.data.wechatField)) {
        return
      }

      wx.showLoading({ title: '保存中...' })

      try {
        // 调用API更新微信号
        const res = await api.updateUserContact({
          contactType: ContactType.WECHAT_ID.code,
          contactValue: this.data.wechatField,
          isVisible: true
        })

        if (res.code === 200) {
          const newContactInfo = {
            ...this.properties.contactInfo,
            wechatId: this.data.wechatField,
            wechatVisible: true
          }

          this.setData({
            showWechatDialog: false
          })

          wx.hideLoading()
          wx.showToast({
            title: '微信号保存成功',
            icon: 'success',
            duration: 1500
          })

          this.triggerEvent('contactChange', {
            contactInfo: newContactInfo,
            type: 'wechat'
          })
        } else {
          throw new Error(res.msg || '保存失败')
        }
      } catch (error) {
        console.error('更新微信号失败:', error)
        wx.hideLoading()
        wx.showToast({
          title: error.message || '保存失败',
          icon: 'none',
          duration: 2000
        })
      }
    },

    // 微信号可见性变更
    async onWechatVisibilityChange(e) {
      const isVisible = e.detail
      const { contactInfo } = this.properties

      // 如果要设置为不可见，检查是否至少还有一个联系方式可见
      if (!isVisible) {
        const hasOtherVisible = (contactInfo.mobile && contactInfo.mobileVisible) ||
          (contactInfo.wechatQr && contactInfo.wechatQrVisible)

        if (!hasOtherVisible) {
          wx.showToast({
            title: '至少需要一种联系方式',
            icon: 'none',
            duration: 2000
          })
          return
        }
      }

      try {
        // 调用API更新可见性
        const res = await api.updateContactVisibility({
          contactType: ContactType.WECHAT_ID.code,
          isVisible: isVisible
        })

        if (res.code === 200) {
          const newContactInfo = {
            ...contactInfo,
            wechatVisible: isVisible
          }

          this.triggerEvent('contactChange', {
            contactInfo: newContactInfo,
            type: 'wechatVisibility'
          })
        } else {
          throw new Error(res.msg || '更新失败')
        }
      } catch (error) {
        console.error('更新微信号可见性失败:', error)
        wx.showToast({
          title: error.message || '更新失败',
          icon: 'none',
          duration: 2000
        })
      }
    },

    // 显示微信二维码编辑弹窗
    async showWechatQrDialog() {
      const qrPath = this.properties.contactInfo.wechatQr ? 
        await systemInfoService.processImageUrl(this.properties.contactInfo.wechatQr) : ''

      this.setData({
        showWechatQrDialog: true,
        tempQrCodePath: qrPath
      })
    },

    // 关闭微信二维码编辑弹窗
    onCloseWechatQrDialog() {
      this.setData({
        showWechatQrDialog: false,
        tempQrCodePath: ''
      })
    },

    // 选择微信二维码图片
    chooseQrCode() {
      wx.chooseMedia({
        count: 1,
        mediaType: ['image'],
        sourceType: ['album', 'camera'],
        camera: 'back',
        success: (res) => {
          const tempFilePath = res.tempFiles[0].tempFilePath
          this.setData({
            tempQrCodePath: tempFilePath
          })
        }
      })
    },

    // 确认上传微信二维码
    async onConfirmWechatQr() {
      if (!this.data.tempQrCodePath) {
        wx.showToast({
          title: '请选择二维码图片',
          icon: 'none'
        })
        return
      }

      wx.showLoading({ title: '上传中...' })

      try {
        // 先上传图片文件
        const uploadResult = await util.uploadFile({
          filePath: this.data.tempQrCodePath,
          type: 6 // 微信二维码类型
        })

        // 调用API更新微信二维码
        const res = await api.updateUserContact({
          contactType: ContactType.WECHAT_QR.code,
          contactValue: uploadResult.filePath,
          isVisible: true
        })

        if (res.code === 200) {
          const newContactInfo = {
            ...this.properties.contactInfo,
            wechatQr: uploadResult.filePath,
            wechatQrVisible: true
          }

          this.setData({
            showWechatQrDialog: false,
            tempQrCodePath: ''
          })

          wx.hideLoading()
          wx.showToast({
            title: '二维码保存成功',
            icon: 'success',
            duration: 1500
          })

          this.triggerEvent('contactChange', {
            contactInfo: newContactInfo,
            type: 'wechatQr'
          })
        } else {
          throw new Error(res.msg || '保存失败')
        }
      } catch (error) {
        console.error('上传微信二维码失败:', error)
        wx.hideLoading()
        wx.showToast({
          title: error.message || '上传失败，请重试',
          icon: 'none',
          duration: 2000
        })
      }
    },

    // 微信二维码可见性变更
    async onWechatQrVisibilityChange(e) {
      const isVisible = e.detail
      const { contactInfo } = this.properties

      // 如果要设置为不可见，检查是否至少还有一个联系方式可见
      if (!isVisible) {
        const hasOtherVisible = (contactInfo.mobile && contactInfo.mobileVisible) ||
          (contactInfo.wechatId && contactInfo.wechatVisible)

        if (!hasOtherVisible) {
          wx.showToast({
            title: '至少需要一种联系方式',
            icon: 'none',
            duration: 2000
          })
          return
        }
      }

      try {
        // 调用API更新可见性
        const res = await api.updateContactVisibility({
          contactType: ContactType.WECHAT_QR.code,
          isVisible: isVisible
        })

        if (res.code === 200) {
          const newContactInfo = {
            ...contactInfo,
            wechatQrVisible: isVisible
          }

          this.triggerEvent('contactChange', {
            contactInfo: newContactInfo,
            type: 'wechatQrVisibility'
          })
        } else {
          throw new Error(res.msg || '更新失败')
        }
      } catch (error) {
        console.error('更新微信二维码可见性失败:', error)
        wx.showToast({
          title: error.message || '更新失败',
          icon: 'none',
          duration: 2000
        })
      }
    }
  }
})
