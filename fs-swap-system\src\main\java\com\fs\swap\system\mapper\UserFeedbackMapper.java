package com.fs.swap.system.mapper;

import com.fs.swap.common.core.domain.entity.UserFeedback;

import java.util.List;

/**
 * 用户反馈Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-27
 */
public interface UserFeedbackMapper
{
    /**
     * 查询用户反馈
     *
     * @param id 用户反馈主键
     * @return 用户反馈
     */
    public UserFeedback selectUserFeedbackById(Long id);

    /**
     * 查询用户反馈列表
     *
     * @param userFeedback 用户反馈
     * @return 用户反馈集合
     */
    public List<UserFeedback> selectUserFeedbackList(UserFeedback userFeedback);

    /**
     * 新增用户反馈
     *
     * @param userFeedback 用户反馈
     * @return 结果
     */
    public int insertUserFeedback(UserFeedback userFeedback);

    /**
     * 修改用户反馈
     *
     * @param userFeedback 用户反馈
     * @return 结果
     */
    public int updateUserFeedback(UserFeedback userFeedback);

    /**
     * 删除用户反馈
     *
     * @param id 用户反馈主键
     * @return 结果
     */
    public int deleteUserFeedbackById(Long id);

    /**
     * 批量删除用户反馈
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteUserFeedbackByIds(Long[] ids);

    /**
     * 根据用户ID查询反馈列表
     *
     * @param userId 用户ID
     * @return 用户反馈集合
     */
    public List<UserFeedback> selectUserFeedbackListByUserId(Long userId);
}
