package com.fs.swap.admin.controller.operation;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.fs.swap.common.annotation.Log;
import com.fs.swap.common.core.controller.BaseController;
import com.fs.swap.common.core.domain.AjaxResult;
import com.fs.swap.common.enums.BusinessType;
import com.fs.swap.common.core.domain.entity.UserTask;
import com.fs.swap.system.service.ITaskService;
import com.fs.swap.common.utils.poi.ExcelUtil;
import com.fs.swap.common.core.page.TableDataInfo;

/**
 * 用户任务Controller
 * 
 * <AUTHOR>
 * @date 2025-01-18
 */
@RestController
@RequestMapping("/operation/user-task")
public class UserTaskController extends BaseController {
    
    @Autowired
    private ITaskService taskService;

    /**
     * 查询用户任务列表
     */
    @PreAuthorize("@ss.hasPermi('operation:user-task:list')")
    @GetMapping("/list")
    public TableDataInfo list(UserTask userTask) {
        startPage();
        List<UserTask> list = taskService.selectUserTaskList(userTask);
        return getDataTable(list);
    }

    /**
     * 导出用户任务列表
     */
    @PreAuthorize("@ss.hasPermi('operation:user-task:export')")
    @Log(title = "用户任务", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, UserTask userTask) {
        List<UserTask> list = taskService.selectUserTaskList(userTask);
        ExcelUtil<UserTask> util = new ExcelUtil<UserTask>(UserTask.class);
        util.exportExcel(response, list, "用户任务数据");
    }

    /**
     * 获取用户任务详细信息
     */
    @PreAuthorize("@ss.hasPermi('operation:user-task:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(taskService.selectUserTaskById(id));
    }

    /**
     * 新增用户任务
     */
    @PreAuthorize("@ss.hasPermi('operation:user-task:add')")
    @Log(title = "用户任务", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody UserTask userTask) {
        return toAjax(taskService.insertUserTask(userTask));
    }

    /**
     * 修改用户任务
     */
    @PreAuthorize("@ss.hasPermi('operation:user-task:edit')")
    @Log(title = "用户任务", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    public AjaxResult edit(@RequestBody UserTask userTask) {
        return toAjax(taskService.updateUserTask(userTask));
    }

    /**
     * 删除用户任务
     */
    @PreAuthorize("@ss.hasPermi('operation:user-task:remove')")
    @Log(title = "用户任务", businessType = BusinessType.DELETE)
    @PostMapping("/remove/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(taskService.deleteUserTaskByIds(ids));
    }
} 