package com.fs.swap.wx.controller;

import com.fasterxml.jackson.databind.node.ObjectNode;
import com.fs.swap.common.constant.Constants;
import com.fs.swap.common.core.controller.WxApiBaseController;
import com.fs.swap.common.core.domain.AjaxResult;
import com.fs.swap.common.core.domain.entity.ResidentialArea;
import com.fs.swap.common.core.domain.entity.SysDictData;
import com.fs.swap.common.core.page.TableDataInfo;
import com.fs.swap.common.enums.ErrorType;
import com.fs.swap.common.enums.FilePathType;
import com.fs.swap.common.enums.GreenImgType;
import com.fs.swap.common.enums.DictType;
import com.fs.swap.common.exception.ServiceException;
import com.fs.swap.common.green.ContentModerationFacade;
import com.fs.swap.common.utils.JacksonUtil;
import com.fs.swap.system.service.IResidentialAreaService;
import com.fs.swap.system.service.ISysConfigService;
import com.fs.swap.system.service.ISysDictTypeService;
import com.fs.swap.wx.pojo.dto.GeocoderDTO;
import com.fs.swap.wx.pojo.vo.GeocoderVO;
import com.fs.swap.wx.service.GeocoderService;
import org.dromara.x.file.storage.core.FileInfo;
import org.dromara.x.file.storage.core.FileStorageService;
import org.dromara.x.file.storage.core.upload.UploadPretreatment;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 系统信息
 */
@RestController
@RequestMapping("/common")
public class CommonController extends WxApiBaseController {
    protected final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Value("${dromara.fileDomain}")
    private String fileUrl;
    @Autowired
    private FileStorageService fileStorageService;
    @Autowired
    private ISysDictTypeService dictTypeService;
    @Autowired
    private ISysConfigService sysConfigService;
    @Autowired
    private GeocoderService geocoderService;
    @Autowired
    private IResidentialAreaService residentialAreaService;
    @Autowired
    private ContentModerationFacade contentModerationFacade;


    @GetMapping("/system_info")
    public AjaxResult systemInfo() {
        ObjectNode jsonNodes = JacksonUtil.creatObjNode();
        jsonNodes.put("fileUrl", fileUrl);
        String productSilver = sysConfigService.selectConfigByKey("swap.product.silver");
        String helpSilver = sysConfigService.selectConfigByKey("swap.help.silver");
        jsonNodes.put("productSilver", productSilver);
        jsonNodes.put("helpSilver", helpSilver);

        // 添加社区服务相关字典数据
        try {
            // 获取社区服务分类字典
            List<SysDictData> categoryList = dictTypeService.selectDictDataByType(DictType.COMMUNITY_SERVICE_CATEGORY.val());
            if (categoryList != null && !categoryList.isEmpty()) {
                String categoryJson = JacksonUtil.toJSONString(categoryList);
                jsonNodes.set("communityServiceCategories", JacksonUtil.readTree(categoryJson));
            }
            // 获取活动分类字典
            List<SysDictData> activityCategoryList = dictTypeService.selectDictDataByType(DictType.ACTIVITY_CATEGORY.val());
            if (categoryList != null && !categoryList.isEmpty()) {
                String categoryJson = JacksonUtil.toJSONString(activityCategoryList);
                jsonNodes.set("activityCategories", JacksonUtil.readTree(categoryJson));
            }

            // 获取社区服务标签字典
            List<SysDictData> tagList = dictTypeService.selectDictDataByType(DictType.COMMUNITY_SERVICE_TAG.val());
            if (tagList != null && !tagList.isEmpty()) {
                String tagJson = JacksonUtil.toJSONString(tagList);
                jsonNodes.set("communityServiceTags", JacksonUtil.readTree(tagJson));
            }

            // 获取审核状态字典
            List<SysDictData> auditStatusList = dictTypeService.selectDictDataByType(DictType.COMMUNITY_SERVICE_AUDIT_STATUS.val());
            if (auditStatusList != null && !auditStatusList.isEmpty()) {
                String auditJson = JacksonUtil.toJSONString(auditStatusList);
                jsonNodes.set("communityServiceAuditStatus", JacksonUtil.readTree(auditJson));
            }

            // 获取社区电话分类字典
            List<SysDictData> phoneCategoryList = dictTypeService.selectDictDataByType(DictType.COMMUNITY_PHONE_CATEGORY.val());
            if (phoneCategoryList != null && !phoneCategoryList.isEmpty()) {
                String phoneCategoryJson = JacksonUtil.toJSONString(phoneCategoryList);
                jsonNodes.set("communityPhoneCategories", JacksonUtil.readTree(phoneCategoryJson));
            }
            // 获取社区互助需求字典
            List<SysDictData> requireCategories = dictTypeService.selectDictDataByType(DictType.COMMUNITY_HELP_REQUIRE_CATEGORIES.val());
            if (requireCategories != null && !requireCategories.isEmpty()) {
                String requireCategoriesJson = JacksonUtil.toJSONString(requireCategories);
                jsonNodes.set("communityHelpRequireCategories", JacksonUtil.readTree(requireCategoriesJson));
            }
            // 获取社区服务需求字典
            List<SysDictData> serviceCategories = dictTypeService.selectDictDataByType(DictType.COMMUNITY_HELP_SERVICE_CATEGORIES.val());
            if (serviceCategories != null && !serviceCategories.isEmpty()) {
                String serviceCategoriesJson = JacksonUtil.toJSONString(serviceCategories);
                jsonNodes.set("communityHelpServiceCategories", JacksonUtil.readTree(serviceCategoriesJson));
            }

            // 获取社区互助发布类型字典
            List<SysDictData> helpPublishTypeList = dictTypeService.selectDictDataByType(DictType.COMMUNITY_HELP_PUBLISH_TYPE.val());
            if (helpPublishTypeList != null && !helpPublishTypeList.isEmpty()) {
                String helpPublishTypeJson = JacksonUtil.toJSONString(helpPublishTypeList);
                jsonNodes.set("communityHelpPublishTypes", JacksonUtil.readTree(helpPublishTypeJson));
            }

            // 获取碳豆明细类型
            List<SysDictData> silverEventType = dictTypeService.selectDictDataByType(DictType.SILVER_EVENT_TYPE.val());
            if (silverEventType != null && !silverEventType.isEmpty()) {
                String silverEventTypeJson = JacksonUtil.toJSONString(silverEventType);
                jsonNodes.set("silverEventType", JacksonUtil.readTree(silverEventTypeJson));
            }
        } catch (Exception e) {
            logger.warn("获取社区服务字典数据失败", e);
            // 不影响主要功能，只记录警告日志
        }

        return AjaxResult.success(jsonNodes);
    }

    @PostMapping("/upload")
    public AjaxResult upload(MultipartFile file, @RequestParam(name = "type", required = false) FilePathType filePathType) {
        UploadPretreatment uploadPretreatment = fileStorageService.of(file);
        if (filePathType != null) {
            uploadPretreatment.setPath(filePathType.getPath());
        } else {
            uploadPretreatment.setPath(FilePathType.DEFAULT.getPath());
        }
        FileInfo fileInfo = uploadPretreatment.upload();
        if (fileInfo == null) {
            throw new ServiceException(ErrorType.E_5004);
        }
        String fullPath = fileInfo.getBasePath() + fileInfo.getPath() + fileInfo.getFilename();
        if (FilePathType.AVATAR == filePathType) {
            try {
                contentModerationFacade.checkImage(fileUrl + fullPath, GreenImgType.PROFILE_PHOTO_CHECK.val());
            } catch (ServiceException e) {
                // 审核失败，删除已上传的文件
                fileStorageService.delete(fileInfo);
                throw e;
            }
        }
        ObjectNode jsonNodes = JacksonUtil.creatObjNode();
        jsonNodes.put(Constants.FILE_PATH, fullPath);
        return AjaxResult.success(jsonNodes);
    }

    @PostMapping("/geocoder/address")
    public AjaxResult getAddress(@RequestBody GeocoderDTO geocoderDTO) {
        GeocoderVO vo = geocoderService.getAddress(geocoderDTO);
        return AjaxResult.success(vo);
    }

    @GetMapping("/residential_area_list")
    public TableDataInfo residentialAreaList(ResidentialArea residentialArea) {
        startPage();
        List<ResidentialArea> residentialAreas = residentialAreaService.selectResidentialAreaList(residentialArea);
        return getDataTable(residentialAreas);
    }

}