(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-33805972"],{"0ccb":function(e,t,a){"use strict";var r=a("e330"),n=a("50c4"),i=a("577e"),s=a("1148"),o=a("1d80"),l=r(s),c=r("".slice),d=Math.ceil,u=function(e){return function(t,a,r){var s,u,m=i(o(t)),p=n(a),h=m.length,g=void 0===r?" ":i(r);return p<=h||""===g?m:(s=p-h,u=l(g,d(s/g.length)),u.length>s&&(u=c(u,0,s)),e?m+u:u+m)}};e.exports={start:u(!1),end:u(!0)}},"4d90":function(e,t,a){"use strict";var r=a("23e7"),n=a("0ccb").start,i=a("9a0c");r({target:"String",proto:!0,forced:i},{padStart:function(e){return n(this,e,arguments.length>1?arguments[1]:void 0)}})},"511d":function(e,t,a){"use strict";a("c03d")},"9a0c":function(e,t,a){"use strict";var r=a("342f");e.exports=/Version\/10(?:\.\d+){1,2}(?: [\w./]+)?(?: Mobile\/\w+)? Safari\//.test(r)},a1bd:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",attrs:{model:e.queryParams,size:"small",inline:!0,"label-width":"68px"}},[a("area-chain-selector",{ref:"queryAreaSelector",attrs:{regionId:e.queryParams.regionId,communityId:e.queryParams.communityId,residentialId:e.queryParams.residentialId,labels:{region:"行政区域",community:"社区",residential:"小区"},showCommunity:!0,showResidential:!0,inlineLayout:!0},on:{"update:regionId":function(t){return e.queryParams.regionId=t},"update:communityId":function(t){return e.queryParams.communityId=t},"update:residentialId":function(t){return e.queryParams.residentialId=t}}}),a("el-form-item",{attrs:{label:"年月",prop:"yearMonth"}},[a("el-date-picker",{attrs:{type:"month","value-format":"yyyy-MM",placeholder:"请选择年月",clearable:""},model:{value:e.queryParams.yearMonth,callback:function(t){e.$set(e.queryParams,"yearMonth",t)},expression:"queryParams.yearMonth"}})],1),a("el-form-item",{attrs:{label:"发放状态",prop:"status"}},[a("el-select",{attrs:{placeholder:"请选择发放状态",clearable:""},model:{value:e.queryParams.status,callback:function(t){e.$set(e.queryParams,"status",t)},expression:"queryParams.status"}},[a("el-option",{attrs:{label:"待发放",value:"PENDING"}}),a("el-option",{attrs:{label:"已发放",value:"SUCCESS"}}),a("el-option",{attrs:{label:"发放失败",value:"FAILED"}})],1)],1),a("el-form-item",{attrs:{label:"奖励类型",prop:"rewardType"}},[a("el-select",{attrs:{placeholder:"请选择奖励类型",clearable:""},model:{value:e.queryParams.rewardType,callback:function(t){e.$set(e.queryParams,"rewardType",t)},expression:"queryParams.rewardType"}},e._l(e.dict.type.ranking_reward_type,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),a("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1),a("el-row",{staticClass:"mb8",attrs:{gutter:10}},[a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["operation:monthly-ranking-reward-record:generate"],expression:"['operation:monthly-ranking-reward-record:generate']"}],attrs:{type:"primary",plain:"",icon:"el-icon-plus",size:"mini"},on:{click:e.handleGenerate}},[e._v("生成奖励")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["operation:monthly-ranking-reward-record:issue"],expression:"['operation:monthly-ranking-reward-record:issue']"}],attrs:{type:"success",plain:"",icon:"el-icon-money",size:"mini"},on:{click:e.handleBatchIssue}},[e._v("批量发放")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["operation:monthly-ranking-reward-record:remove"],expression:"['operation:monthly-ranking-reward-record:remove']"}],attrs:{type:"danger",plain:"",icon:"el-icon-delete",size:"mini"},on:{click:e.handleDeleteByMonth}},[e._v("清理记录")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["operation:monthly-ranking-reward-record:export"],expression:"['operation:monthly-ranking-reward-record:export']"}],attrs:{type:"warning",plain:"",icon:"el-icon-download",size:"mini"},on:{click:e.handleExport}},[e._v("导出")])],1),a("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1),a("el-row",{staticClass:"mb8",attrs:{gutter:20}},[a("el-col",{attrs:{span:6}},[a("el-card",{staticClass:"box-card"},[a("div",{staticClass:"statistic-item"},[a("span",{staticClass:"statistic-label"},[e._v("总记录数")]),a("span",{staticClass:"statistic-value"},[e._v(e._s(e.statistics.total))])])])],1),a("el-col",{attrs:{span:6}},[a("el-card",{staticClass:"box-card"},[a("div",{staticClass:"statistic-item"},[a("span",{staticClass:"statistic-label"},[e._v("待发放")]),a("span",{staticClass:"statistic-value pending"},[e._v(e._s(e.statistics.pending))])])])],1),a("el-col",{attrs:{span:6}},[a("el-card",{staticClass:"box-card"},[a("div",{staticClass:"statistic-item"},[a("span",{staticClass:"statistic-label"},[e._v("已发放")]),a("span",{staticClass:"statistic-value success"},[e._v(e._s(e.statistics.success))])])])],1),a("el-col",{attrs:{span:6}},[a("el-card",{staticClass:"box-card"},[a("div",{staticClass:"statistic-item"},[a("span",{staticClass:"statistic-label"},[e._v("发放失败")]),a("span",{staticClass:"statistic-value failed"},[e._v(e._s(e.statistics.failed))])])])],1)],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.rewardRecordList},on:{"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"55",align:"center"}}),a("el-table-column",{attrs:{label:"用户信息",align:"center",width:"200"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",{staticClass:"user-info"},[a("image-preview",{attrs:{src:t.row.avatar||e.defaultAvatar,width:40,height:40}}),a("div",{staticClass:"user-details"},[a("div",[e._v(e._s(t.row.nickname))]),a("div",{staticClass:"user-id"},[e._v("ID: "+e._s(t.row.userId))])])],1)]}}])}),a("el-table-column",{attrs:{label:"排名",align:"center",prop:"rankPosition",width:"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-tag",{attrs:{type:e.getRankTagType(t.row.rankPosition),size:"small"}},[e._v(" 第"+e._s(t.row.rankPosition)+"名 ")])]}}])}),a("el-table-column",{attrs:{label:"用户昵称",align:"center",prop:"nickname"}}),a("el-table-column",{attrs:{label:"奖励名称",align:"center",prop:"rewardName"}}),a("el-table-column",{attrs:{label:"奖励数量",align:"center",prop:"rewardAmount"}}),a("el-table-column",{attrs:{label:"奖励类型",align:"center",prop:"rewardType",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("dict-tag",{attrs:{options:e.dict.type.ranking_reward_type,value:t.row.rewardType}})]}}])}),a("el-table-column",{attrs:{label:"发放状态",align:"center",prop:"status",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("dict-tag",{attrs:{options:e.dict.type.reward_status,value:t.row.status}})]}}])}),a("el-table-column",{attrs:{label:"发放时间",align:"center",prop:"issuedTime",width:"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(e.parseTime(t.row.issuedTime,"{y}-{m}-{d} {h}:{i}:{s}")))])]}}])}),a("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){return["PENDING"===t.row.status||"FAILED"===t.row.status?a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["operation:monthly-ranking-reward-record:issue"],expression:"['operation:monthly-ranking-reward-record:issue']"}],attrs:{size:"mini",type:"text",icon:"el-icon-money"},on:{click:function(a){return e.handleIssue(t.row)}}},[e._v("发放")]):e._e()]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}}),a("el-dialog",{attrs:{title:"生成奖励记录",visible:e.generateDialogVisible,width:"400px","append-to-body":""},on:{"update:visible":function(t){e.generateDialogVisible=t}}},[a("el-form",{ref:"generateForm",attrs:{model:e.generateForm,rules:e.generateRules,"label-width":"100px"}},[a("el-form-item",{attrs:{label:"年月",prop:"yearMonth"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"month","value-format":"yyyy-MM",placeholder:"请选择年月"},model:{value:e.generateForm.yearMonth,callback:function(t){e.$set(e.generateForm,"yearMonth",t)},expression:"generateForm.yearMonth"}})],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(t){e.generateDialogVisible=!1}}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary",loading:e.generateLoading},on:{click:e.confirmGenerate}},[e._v("确定生成")])],1)],1),a("el-dialog",{attrs:{title:"清理奖励记录",visible:e.deleteDialogVisible,width:"400px","append-to-body":""},on:{"update:visible":function(t){e.deleteDialogVisible=t}}},[a("div",{staticStyle:{"text-align":"center"}},[a("i",{staticClass:"el-icon-warning",staticStyle:{"font-size":"50px",color:"#f56c6c"}}),a("p",{staticStyle:{margin:"20px 0"}},[e._v("确定要清理指定月份的奖励记录吗？")]),a("p",{staticStyle:{color:"#909399","font-size":"14px"}},[e._v("此操作将删除该月份的所有奖励记录，不可恢复！")])]),a("el-form",{ref:"deleteForm",attrs:{model:e.deleteForm,rules:e.deleteRules,"label-width":"100px"}},[a("el-form-item",{attrs:{label:"年月",prop:"yearMonth"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"month","value-format":"yyyy-MM",placeholder:"请选择年月"},model:{value:e.deleteForm.yearMonth,callback:function(t){e.$set(e.deleteForm,"yearMonth",t)},expression:"deleteForm.yearMonth"}})],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(t){e.deleteDialogVisible=!1}}},[e._v("取 消")]),a("el-button",{attrs:{type:"danger",loading:e.deleteLoading},on:{click:e.confirmDelete}},[e._v("确定清理")])],1)],1)],1)},n=[],i=a("5530"),s=(a("99af"),a("4de4"),a("d81d"),a("d3b7"),a("4d90"),a("0643"),a("2382"),a("a573"),a("b775"));function o(e){return Object(s["a"])({url:"/operation/monthly-ranking-reward-record/list",method:"get",params:e})}function l(e){return Object(s["a"])({url:"/operation/monthly-ranking-reward-record/generate",method:"post",params:{yearMonth:e}})}function c(){return Object(s["a"])({url:"/operation/monthly-ranking-reward-record/batchIssue",method:"post"})}function d(e){return Object(s["a"])({url:"/operation/monthly-ranking-reward-record/issue/"+e,method:"post"})}function u(e){return Object(s["a"])({url:"/operation/monthly-ranking-reward-record/deleteByMonth",method:"delete",params:{yearMonth:e}})}var m=a("939b"),p={name:"MonthlyRankingRewardRecord",components:{AreaChainSelector:m["a"]},dicts:["reward_status","ranking_reward_type"],data:function(){return{loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,rewardRecordList:[],defaultAvatar:a("4b94"),statistics:{total:0,pending:0,success:0,failed:0},queryParams:{pageNum:1,pageSize:10,yearMonth:null,regionId:null,communityId:null,residentialId:null,status:null,rewardType:null},generateDialogVisible:!1,generateLoading:!1,generateForm:{yearMonth:null},generateRules:{yearMonth:[{required:!0,message:"年月不能为空",trigger:"change"}]},deleteDialogVisible:!1,deleteLoading:!1,deleteForm:{yearMonth:null},deleteRules:{yearMonth:[{required:!0,message:"年月不能为空",trigger:"change"}]}}},created:function(){var e=new Date;this.queryParams.yearMonth="".concat(e.getFullYear(),"-").concat(String(e.getMonth()+1).padStart(2,"0")),this.getList()},methods:{getList:function(){var e=this;this.loading=!0,o(this.queryParams).then((function(t){e.rewardRecordList=t.rows,e.total=t.total,e.updateStatistics(),e.loading=!1}))},updateStatistics:function(){this.statistics.total=this.rewardRecordList.length,this.statistics.pending=this.rewardRecordList.filter((function(e){return"PENDING"===e.status})).length,this.statistics.success=this.rewardRecordList.filter((function(e){return"SUCCESS"===e.status})).length,this.statistics.failed=this.rewardRecordList.filter((function(e){return"FAILED"===e.status})).length},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.$refs.queryAreaSelector.reset(),this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.id})),this.single=1!==e.length,this.multiple=!e.length},handleGenerate:function(){this.generateForm.yearMonth=null,this.generateDialogVisible=!0},confirmGenerate:function(){var e=this;this.$refs["generateForm"].validate((function(t){t&&(e.generateLoading=!0,l(e.generateForm.yearMonth).then((function(t){e.$modal.msgSuccess(t.msg),e.generateDialogVisible=!1,e.getList()})).finally((function(){e.generateLoading=!1})))}))},handleBatchIssue:function(){var e=this;this.$modal.confirm("确认要批量发放所有待发放的奖励吗？").then((function(){c().then((function(t){e.$modal.msgSuccess(t.msg),e.getList()}))}))},handleIssue:function(e){var t=this;this.$modal.confirm("确认要发放该奖励吗？").then((function(){d(e.id).then((function(e){t.$modal.msgSuccess(e.msg),t.getList()}))}))},handleDeleteByMonth:function(){this.deleteForm.yearMonth=null,this.deleteDialogVisible=!0},confirmDelete:function(){var e=this;this.$refs["deleteForm"].validate((function(t){t&&(e.deleteLoading=!0,u(e.deleteForm.yearMonth).then((function(t){e.$modal.msgSuccess(t.msg),e.deleteDialogVisible=!1,e.getList()})).finally((function(){e.deleteLoading=!1})))}))},handleExport:function(){this.download("operation/monthly-ranking-reward-record/export",Object(i["a"])({},this.queryParams),"reward_record_".concat((new Date).getTime(),".xlsx"))},getRankTagType:function(e){return 1===e?"danger":2===e?"warning":3===e?"success":"info"},getStatusTagType:function(e){var t={PENDING:"warning",SUCCESS:"success",FAILED:"danger"};return t[e]||"info"},getStatusText:function(e){var t={PENDING:"待发放",SUCCESS:"已发放",FAILED:"发放失败"};return t[e]||"未知"}}},h=p,g=(a("511d"),a("2877")),y=Object(g["a"])(h,r,n,!1,null,"a8869916",null);t["default"]=y.exports},c03d:function(e,t,a){}}]);