(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-b193f0ee"],{4006:function(t,e,a){"use strict";a("a208")},a208:function(t,e,a){},abc9:function(t,e,a){"use strict";a.r(e);var s=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"app-container"},[a("el-row",{staticClass:"mb20",attrs:{gutter:20}},[a("el-col",{attrs:{span:6}},[a("el-card",{attrs:{shadow:"hover"}},[a("div",{staticClass:"statistic-card"},[a("div",{staticClass:"statistic-icon"},[a("i",{staticClass:"el-icon-s-check",staticStyle:{color:"#67C23A"}})]),a("div",{staticClass:"statistic-content"},[a("div",{staticClass:"statistic-title"},[t._v("今日完成任务数")]),a("div",{staticClass:"statistic-value"},[t._v(t._s(t.statistics.todayCompleted||0))])])])])],1),a("el-col",{attrs:{span:6}},[a("el-card",{attrs:{shadow:"hover"}},[a("div",{staticClass:"statistic-card"},[a("div",{staticClass:"statistic-icon"},[a("i",{staticClass:"el-icon-user",staticStyle:{color:"#409EFF"}})]),a("div",{staticClass:"statistic-content"},[a("div",{staticClass:"statistic-title"},[t._v("活跃用户数")]),a("div",{staticClass:"statistic-value"},[t._v(t._s(t.statistics.activeUsers||0))])])])])],1),a("el-col",{attrs:{span:6}},[a("el-card",{attrs:{shadow:"hover"}},[a("div",{staticClass:"statistic-card"},[a("div",{staticClass:"statistic-icon"},[a("i",{staticClass:"el-icon-coin",staticStyle:{color:"#FF9800"}})]),a("div",{staticClass:"statistic-content"},[a("div",{staticClass:"statistic-title"},[t._v("今日发放碳豆")]),a("div",{staticClass:"statistic-value"},[t._v(t._s(t.statistics.todayReward||0))])])])])],1),a("el-col",{attrs:{span:6}},[a("el-card",{attrs:{shadow:"hover"}},[a("div",{staticClass:"statistic-card"},[a("div",{staticClass:"statistic-icon"},[a("i",{staticClass:"el-icon-trophy",staticStyle:{color:"#E6A23C"}})]),a("div",{staticClass:"statistic-content"},[a("div",{staticClass:"statistic-title"},[t._v("任务完成率")]),a("div",{staticClass:"statistic-value"},[t._v(t._s((t.statistics.completionRate||0).toFixed(1))+"%")])])])])],1)],1),a("el-card",{staticClass:"mb20",attrs:{shadow:"never"}},[a("el-form",{ref:"queryForm",attrs:{model:t.queryParams,size:"small",inline:!0,"label-width":"80px"}},[a("el-form-item",{attrs:{label:"统计类型",prop:"statisticsType"}},[a("el-select",{attrs:{placeholder:"请选择统计类型"},on:{change:t.handleTypeChange},model:{value:t.queryParams.statisticsType,callback:function(e){t.$set(t.queryParams,"statisticsType",e)},expression:"queryParams.statisticsType"}},[a("el-option",{attrs:{label:"完成统计",value:"completion"}}),a("el-option",{attrs:{label:"活跃统计",value:"activity"}}),a("el-option",{attrs:{label:"奖励统计",value:"reward"}}),a("el-option",{attrs:{label:"排行榜",value:"ranking"}})],1)],1),a("el-form-item",{attrs:{label:"时间范围",prop:"dateRange"}},[a("el-date-picker",{staticStyle:{width:"240px"},attrs:{"value-format":"yyyy-MM-dd",type:"daterange","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期"},model:{value:t.dateRange,callback:function(e){t.dateRange=e},expression:"dateRange"}})],1),a("el-form-item",{attrs:{label:"任务类型",prop:"taskType"}},[a("el-select",{attrs:{placeholder:"请选择任务类型",clearable:""},model:{value:t.queryParams.taskType,callback:function(e){t.$set(t.queryParams,"taskType",e)},expression:"queryParams.taskType"}},t._l(t.dict.type.task_type,(function(t){return a("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})})),1)],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:t.handleQuery}},[t._v("查询")]),a("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:t.resetQuery}},[t._v("重置")]),a("el-button",{attrs:{type:"success",icon:"el-icon-download",size:"mini"},on:{click:t.handleExport}},[t._v("导出")])],1)],1)],1),a("el-row",{staticClass:"mb20",attrs:{gutter:20}},[a("el-col",{attrs:{span:12}},[a("el-card",{attrs:{shadow:"never"}},[a("div",{attrs:{slot:"header"},slot:"header"},[a("span",[t._v("任务完成趋势")])]),a("div",{staticStyle:{height:"300px"},attrs:{id:"trendChart"}})])],1),a("el-col",{attrs:{span:12}},[a("el-card",{attrs:{shadow:"never"}},[a("div",{attrs:{slot:"header"},slot:"header"},[a("span",[t._v("任务类型分布")])]),a("div",{staticStyle:{height:"300px"},attrs:{id:"pieChart"}})])],1)],1),a("el-card",{attrs:{shadow:"never"}},[a("div",{attrs:{slot:"header"},slot:"header"},[a("span",[t._v(t._s(t.getTableTitle()))])]),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticStyle:{width:"100%"},attrs:{data:t.tableData}},["completion"===t.queryParams.statisticsType?[a("el-table-column",{attrs:{label:"日期",align:"center",prop:"date",width:"120"}}),a("el-table-column",{attrs:{label:"任务类型",align:"center",prop:"taskType",width:"120"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("dict-tag",{attrs:{options:t.dict.type.task_type,value:e.row.taskType}})]}}],null,!1,304411868)}),a("el-table-column",{attrs:{label:"完成数量",align:"center",prop:"completedCount"}}),a("el-table-column",{attrs:{label:"目标数量",align:"center",prop:"targetCount"}}),a("el-table-column",{attrs:{label:"完成率",align:"center",prop:"completionRate"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-progress",{attrs:{percentage:e.row.completionRate,"show-text":!1}}),a("span",{staticClass:"ml10"},[t._v(t._s(e.row.completionRate)+"%")])]}}],null,!1,3338782538)})]:t._e(),"activity"===t.queryParams.statisticsType?[a("el-table-column",{attrs:{label:"日期",align:"center",prop:"date",width:"120"}}),a("el-table-column",{attrs:{label:"活跃用户数",align:"center",prop:"activeUsers"}}),a("el-table-column",{attrs:{label:"新增用户数",align:"center",prop:"newUsers"}}),a("el-table-column",{attrs:{label:"任务参与率",align:"center",prop:"participationRate"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.participationRate)+"%")])]}}],null,!1,314394099)})]:t._e(),"reward"===t.queryParams.statisticsType?[a("el-table-column",{attrs:{label:"日期",align:"center",prop:"date",width:"120"}}),a("el-table-column",{attrs:{label:"发放碳豆数",align:"center",prop:"rewardAmount"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",{staticClass:"reward-silver"},[t._v(t._s(e.row.rewardAmount))])]}}],null,!1,2136040150)}),a("el-table-column",{attrs:{label:"领取用户数",align:"center",prop:"claimUsers"}}),a("el-table-column",{attrs:{label:"平均奖励",align:"center",prop:"avgReward"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",{staticClass:"reward-silver"},[t._v(t._s(e.row.avgReward))])]}}],null,!1,789043786)})]:t._e(),"ranking"===t.queryParams.statisticsType?[a("el-table-column",{attrs:{label:"排名",align:"center",prop:"rank",width:"80"},scopedSlots:t._u([{key:"default",fn:function(e){return[e.row.rank<=3?a("el-tag",{attrs:{type:t.getRankType(e.row.rank)}},[t._v(" "+t._s(e.row.rank)+" ")]):a("span",[t._v(t._s(e.row.rank))])]}}],null,!1,607392793)}),a("el-table-column",{attrs:{label:"用户ID",align:"center",prop:"userId",width:"100"}}),a("el-table-column",{attrs:{label:"用户名",align:"center",prop:"userName",width:"150"}}),a("el-table-column",{attrs:{label:"完成任务数",align:"center",prop:"completedTasks"}}),a("el-table-column",{attrs:{label:"获得碳豆",align:"center",prop:"totalReward"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",{staticClass:"reward-silver"},[t._v(t._s(e.row.totalReward))])]}}],null,!1,273487768)}),a("el-table-column",{attrs:{label:"完成率",align:"center",prop:"completionRate"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.completionRate)+"%")])]}}],null,!1,228142126)})]:t._e()],2),a("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total>0"}],attrs:{total:t.total,page:t.queryParams.pageNum,limit:t.queryParams.pageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"pageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"pageSize",e)},pagination:t.getList}})],1)],1)},i=[],l=a("5530"),r=a("b775");function n(){return Object(r["a"])({url:"/operation/task-statistics/overview",method:"get"})}function o(t){return Object(r["a"])({url:"/operation/task-statistics/list",method:"get",params:t})}var c=a("313e"),d={name:"TaskStatistics",dicts:["task_type"],data:function(){return{loading:!0,statistics:{},total:0,tableData:[],dateRange:[],queryParams:{pageNum:1,pageSize:10,statisticsType:"completion",taskType:null,beginTime:null,endTime:null},trendChart:null,pieChart:null}},watch:{dateRange:function(t){null===t?(this.queryParams.beginTime=null,this.queryParams.endTime=null):(this.queryParams.beginTime=t[0],this.queryParams.endTime=t[1])}},mounted:function(){this.initCharts(),this.getStatistics(),this.getList()},beforeDestroy:function(){this.trendChart&&this.trendChart.dispose(),this.pieChart&&this.pieChart.dispose()},methods:{initCharts:function(){this.trendChart=c["init"](document.getElementById("trendChart")),this.pieChart=c["init"](document.getElementById("pieChart"));var t={title:{text:"任务完成趋势",left:"center"},tooltip:{trigger:"axis"},legend:{data:["完成数量","目标数量"]},xAxis:{type:"category",data:[]},yAxis:{type:"value"},series:[{name:"完成数量",type:"line",data:[]},{name:"目标数量",type:"line",data:[]}]},e={title:{text:"任务类型分布",left:"center"},tooltip:{trigger:"item"},legend:{orient:"vertical",left:"left"},series:[{name:"任务类型",type:"pie",radius:"50%",data:[]}]};this.trendChart.setOption(t),this.pieChart.setOption(e)},getStatistics:function(){var t=this;n().then((function(e){t.statistics=e.data}))},getList:function(){var t=this;this.loading=!0,o(this.queryParams).then((function(e){t.tableData=e.rows,t.total=e.total,t.loading=!1,t.updateCharts(e.chartData||{})}))},updateCharts:function(t){t.trend&&this.trendChart.setOption({xAxis:{data:t.trend.dates||[]},series:[{data:t.trend.completed||[]},{data:t.trend.target||[]}]}),t.distribution&&this.pieChart.setOption({series:[{data:t.distribution||[]}]})},handleTypeChange:function(){this.queryParams.pageNum=1,this.getList()},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.dateRange=[],this.resetForm("queryForm"),this.queryParams.statisticsType="completion",this.handleQuery()},getTableTitle:function(){var t={completion:"任务完成统计",activity:"用户活跃统计",reward:"奖励发放统计",ranking:"用户排行榜"};return t[this.queryParams.statisticsType]||"统计数据"},getRankType:function(t){return 1===t?"danger":2===t?"warning":3===t?"success":"info"},handleExport:function(){this.download("operation/task-statistics/export",Object(l["a"])({},this.queryParams),"task_statistics_".concat((new Date).getTime(),".xlsx"))}}},p=d,u=(a("4006"),a("2877")),m=Object(u["a"])(p,s,i,!1,null,"3d82baf7",null);e["default"]=m.exports}}]);