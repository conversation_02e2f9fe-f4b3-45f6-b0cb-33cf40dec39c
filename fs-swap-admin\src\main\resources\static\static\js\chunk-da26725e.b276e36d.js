(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-da26725e"],{7245:function(e,t,r){},"8ec0":function(e,t,r){"use strict";r.r(t);var a=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticClass:"app-container"},[r("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",attrs:{model:e.queryParams,size:"small",inline:!0,"label-width":"68px"}},[r("el-form-item",{attrs:{label:"奖励类型",prop:"rewardType"}},[r("el-select",{attrs:{placeholder:"请选择奖励类型",clearable:""},model:{value:e.queryParams.rewardType,callback:function(t){e.$set(e.queryParams,"rewardType",t)},expression:"queryParams.rewardType"}},e._l(e.dict.type.ranking_reward_type,(function(e){return r("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),r("el-form-item",{attrs:{label:"状态",prop:"isActive"}},[r("el-select",{attrs:{placeholder:"请选择状态",clearable:""},model:{value:e.queryParams.isActive,callback:function(t){e.$set(e.queryParams,"isActive",t)},expression:"queryParams.isActive"}},[r("el-option",{attrs:{label:"启用",value:!0}}),r("el-option",{attrs:{label:"禁用",value:!1}})],1)],1),r("el-form-item",[r("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),r("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1),r("el-row",{staticClass:"mb8",attrs:{gutter:10}},[r("el-col",{attrs:{span:1.5}},[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["operation:monthly-ranking-reward-config:add"],expression:"['operation:monthly-ranking-reward-config:add']"}],attrs:{type:"primary",plain:"",icon:"el-icon-plus",size:"mini"},on:{click:e.handleAdd}},[e._v("新增")])],1),r("el-col",{attrs:{span:1.5}},[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["operation:monthly-ranking-reward-config:edit"],expression:"['operation:monthly-ranking-reward-config:edit']"}],attrs:{type:"success",plain:"",icon:"el-icon-edit",size:"mini",disabled:e.single},on:{click:e.handleUpdate}},[e._v("修改")])],1),r("el-col",{attrs:{span:1.5}},[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["operation:monthly-ranking-reward-config:remove"],expression:"['operation:monthly-ranking-reward-config:remove']"}],attrs:{type:"danger",plain:"",icon:"el-icon-delete",size:"mini",disabled:e.multiple},on:{click:e.handleDelete}},[e._v("删除")])],1),r("el-col",{attrs:{span:1.5}},[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["operation:monthly-ranking-reward-config:export"],expression:"['operation:monthly-ranking-reward-config:export']"}],attrs:{type:"warning",plain:"",icon:"el-icon-download",size:"mini"},on:{click:e.handleExport}},[e._v("导出")])],1),r("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1),r("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.rewardConfigList},on:{"selection-change":e.handleSelectionChange}},[r("el-table-column",{attrs:{type:"selection",width:"55",align:"center"}}),r("el-table-column",{attrs:{label:"ID",align:"center",prop:"id",width:"80"}}),r("el-table-column",{attrs:{label:"排名范围",align:"center",prop:"rankRange",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("el-tag",{attrs:{type:"info"}},[e._v(e._s(t.row.rankStart)+"-"+e._s(t.row.rankEnd)+"名")])]}}])}),r("el-table-column",{attrs:{label:"奖励类型",align:"center",prop:"rewardType",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("dict-tag",{attrs:{options:e.dict.type.ranking_reward_type,value:t.row.rewardType}})]}}])}),r("el-table-column",{attrs:{label:"奖励数量",align:"center",prop:"rewardAmount",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("span",{staticClass:"reward-amount"},[e._v(e._s(t.row.rewardAmount))])]}}])}),r("el-table-column",{attrs:{label:"奖励名称",align:"center",prop:"rewardName"}}),r("el-table-column",{attrs:{label:"状态",align:"center",prop:"isActive",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("el-switch",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["operation:monthly-ranking-reward-config:edit"],expression:"['operation:monthly-ranking-reward-config:edit']"}],on:{change:function(r){return e.handleStatusChange(t.row)}},model:{value:t.row.isActive,callback:function(r){e.$set(t.row,"isActive",r)},expression:"scope.row.isActive"}})]}}])}),r("el-table-column",{attrs:{label:"创建时间",align:"center",prop:"createTime",width:"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("span",[e._v(e._s(e.parseTime(t.row.createTime,"{y}-{m}-{d} {h}:{i}:{s}")))])]}}])}),r("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["operation:monthly-ranking-reward-config:edit"],expression:"['operation:monthly-ranking-reward-config:edit']"}],attrs:{size:"mini",type:"text",icon:"el-icon-edit"},on:{click:function(r){return e.handleUpdate(t.row)}}},[e._v("修改")]),r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["operation:monthly-ranking-reward-config:remove"],expression:"['operation:monthly-ranking-reward-config:remove']"}],attrs:{size:"mini",type:"text",icon:"el-icon-delete"},on:{click:function(r){return e.handleDelete(t.row)}}},[e._v("删除")])]}}])})],1),r("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}}),r("el-dialog",{attrs:{title:e.title,visible:e.open,width:"500px","append-to-body":""},on:{"update:visible":function(t){e.open=t}}},[r("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"120px"}},[r("el-form-item",{attrs:{label:"排名起始位置",prop:"rankStart"}},[r("el-input-number",{staticStyle:{width:"100%"},attrs:{min:1,max:9999,"controls-position":"right",placeholder:"请输入排名起始位置"},model:{value:e.form.rankStart,callback:function(t){e.$set(e.form,"rankStart",t)},expression:"form.rankStart"}})],1),r("el-form-item",{attrs:{label:"排名结束位置",prop:"rankEnd"}},[r("el-input-number",{staticStyle:{width:"100%"},attrs:{min:1,max:9999,"controls-position":"right",placeholder:"请输入排名结束位置"},model:{value:e.form.rankEnd,callback:function(t){e.$set(e.form,"rankEnd",t)},expression:"form.rankEnd"}})],1),r("el-form-item",{attrs:{label:"奖励类型",prop:"rewardType"}},[r("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择奖励类型"},model:{value:e.form.rewardType,callback:function(t){e.$set(e.form,"rewardType",t)},expression:"form.rewardType"}},e._l(e.dict.type.ranking_reward_type,(function(e){return r("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),r("el-form-item",{attrs:{label:"奖励数量",prop:"rewardAmount"}},[r("el-input-number",{staticStyle:{width:"100%"},attrs:{min:1,max:999999,"controls-position":"right",placeholder:"请输入奖励数量"},model:{value:e.form.rewardAmount,callback:function(t){e.$set(e.form,"rewardAmount",t)},expression:"form.rewardAmount"}})],1),r("el-form-item",{attrs:{label:"奖励名称",prop:"rewardName"}},[r("el-input",{attrs:{placeholder:"请输入奖励名称",maxlength:"100"},model:{value:e.form.rewardName,callback:function(t){e.$set(e.form,"rewardName",t)},expression:"form.rewardName"}})],1),r("el-form-item",{attrs:{label:"状态",prop:"isActive"}},[r("el-radio-group",{model:{value:e.form.isActive,callback:function(t){e.$set(e.form,"isActive",t)},expression:"form.isActive"}},[r("el-radio",{attrs:{label:!0}},[e._v("启用")]),r("el-radio",{attrs:{label:!1}},[e._v("禁用")])],1)],1)],1),r("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[r("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),r("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1)],1)},n=[],i=r("5530"),o=(r("d81d"),r("d3b7"),r("0643"),r("a573"),r("b775"));function l(e){return Object(o["a"])({url:"/operation/monthly-ranking-reward-config/list",method:"get",params:e})}function s(e){return Object(o["a"])({url:"/operation/monthly-ranking-reward-config/"+e,method:"get"})}function c(e){return Object(o["a"])({url:"/operation/monthly-ranking-reward-config",method:"post",data:e})}function d(e){return Object(o["a"])({url:"/operation/monthly-ranking-reward-config",method:"put",data:e})}function u(e){return Object(o["a"])({url:"/operation/monthly-ranking-reward-config/"+e,method:"delete"})}function m(e,t){var r={id:e,isActive:t};return Object(o["a"])({url:"/operation/monthly-ranking-reward-config/changeStatus",method:"put",data:r})}var p={name:"MonthlyRankingRewardConfig",dicts:["ranking_reward_type"],data:function(){return{loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,rewardConfigList:[],title:"",open:!1,queryParams:{pageNum:1,pageSize:10,rewardType:null,isActive:null},form:{},rules:{rankStart:[{required:!0,message:"排名起始位置不能为空",trigger:"blur"}],rankEnd:[{required:!0,message:"排名结束位置不能为空",trigger:"blur"}],rewardType:[{required:!0,message:"奖励类型不能为空",trigger:"change"}],rewardAmount:[{required:!0,message:"奖励数量不能为空",trigger:"blur"}],rewardName:[{required:!0,message:"奖励名称不能为空",trigger:"blur"}]}}},created:function(){this.getList()},methods:{getList:function(){var e=this;this.loading=!0,l(this.queryParams).then((function(t){e.rewardConfigList=t.rows,e.total=t.total,e.loading=!1}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={id:null,rankStart:null,rankEnd:null,rewardType:null,rewardAmount:null,rewardName:null,isActive:!0},this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.id})),this.single=1!==e.length,this.multiple=!e.length},handleAdd:function(){this.reset(),this.open=!0,this.title="添加奖励配置"},handleUpdate:function(e){var t=this;this.reset();var r=e.id||this.ids;s(r).then((function(e){t.form=e.data,t.open=!0,t.title="修改奖励配置"}))},submitForm:function(){var e=this;this.$refs["form"].validate((function(t){if(t){if(e.form.rankStart>e.form.rankEnd)return void e.$modal.msgError("排名起始位置不能大于结束位置");null!=e.form.id?d(e.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.open=!1,e.getList()})):c(e.form).then((function(t){e.$modal.msgSuccess("新增成功"),e.open=!1,e.getList()}))}}))},handleDelete:function(e){var t=this,r=e.id||this.ids;this.$modal.confirm('是否确认删除奖励配置编号为"'+r+'"的数据项？').then((function(){return u(r)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleExport:function(){this.download("operation/monthly-ranking-reward-config/export",Object(i["a"])({},this.queryParams),"reward_config_".concat((new Date).getTime(),".xlsx"))},handleStatusChange:function(e){var t=this,r=e.isActive?"启用":"停用";this.$modal.confirm('确认要"'+r+'""'+e.rewardName+'"配置吗？').then((function(){return m(e.id,e.isActive)})).then((function(){t.$modal.msgSuccess(r+"成功")})).catch((function(){e.isActive=!1===e.isActive}))}}},h=p,f=(r("da63"),r("2877")),g=Object(f["a"])(h,a,n,!1,null,"abc955a0",null);t["default"]=g.exports},da63:function(e,t,r){"use strict";r("7245")}}]);