(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d0d6e56"],{7502:function(e,t,a){"use strict";a.r(t);var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",attrs:{model:e.queryParams,size:"small",inline:!0,"label-width":"68px"}},[a("el-form-item",{attrs:{label:"用户ID",prop:"userId"}},[a("el-input",{attrs:{placeholder:"请输入用户ID",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.userId,callback:function(t){e.$set(e.queryParams,"userId",t)},expression:"queryParams.userId"}})],1),a("el-form-item",{attrs:{label:"反馈内容",prop:"content"}},[a("el-input",{attrs:{placeholder:"请输入反馈内容",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.content,callback:function(t){e.$set(e.queryParams,"content",t)},expression:"queryParams.content"}})],1),a("el-form-item",{attrs:{label:"联系方式",prop:"contact"}},[a("el-input",{attrs:{placeholder:"请输入联系方式",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.contact,callback:function(t){e.$set(e.queryParams,"contact",t)},expression:"queryParams.contact"}})],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),a("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1),a("el-row",{staticClass:"mb8",attrs:{gutter:10}},[a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["operation:feedback:remove"],expression:"['operation:feedback:remove']"}],attrs:{type:"danger",plain:"",icon:"el-icon-delete",size:"mini",disabled:e.multiple},on:{click:e.handleDelete}},[e._v("删除")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["operation:feedback:export"],expression:"['operation:feedback:export']"}],attrs:{type:"warning",plain:"",icon:"el-icon-download",size:"mini"},on:{click:e.handleExport}},[e._v("导出")])],1),a("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.feedbackList},on:{"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"55",align:"center"}}),a("el-table-column",{attrs:{label:"反馈ID",align:"center",prop:"id"}}),a("el-table-column",{attrs:{label:"用户昵称",align:"center",prop:"nickname"}}),a("el-table-column",{attrs:{label:"反馈内容",align:"center",prop:"content","show-overflow-tooltip":!0}}),a("el-table-column",{attrs:{label:"图片",align:"center",prop:"images",width:"200"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",{staticClass:"image-list"},[t.row.images?e._l(t.row.images.split(","),(function(t,n){return a("image-preview",{key:n,staticStyle:{margin:"2px"},attrs:{src:e.filePrefix+t,width:50,height:50}})})):e._e()],2)]}}])}),a("el-table-column",{attrs:{label:"联系方式",align:"center",prop:"contact"}}),a("el-table-column",{attrs:{label:"提交时间",align:"center",prop:"createTime",width:"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(e.parseTime(t.row.createTime,"{y}-{m}-{d} {h}:{i}:{s}")))])]}}])}),a("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["operation:feedback:query"],expression:"['operation:feedback:query']"}],attrs:{size:"mini",type:"text",icon:"el-icon-view"},on:{click:function(a){return e.handleView(t.row)}}},[e._v("查看")]),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["operation:feedback:remove"],expression:"['operation:feedback:remove']"}],attrs:{size:"mini",type:"text",icon:"el-icon-delete"},on:{click:function(a){return e.handleDelete(t.row)}}},[e._v("删除")])]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}}),a("el-dialog",{attrs:{title:e.title,visible:e.open,width:"600px","append-to-body":""},on:{"update:visible":function(t){e.open=t}}},[a("el-form",{ref:"form",attrs:{model:e.form,"label-width":"80px"}},[a("el-form-item",{attrs:{label:"用户昵称"}},[a("el-input",{attrs:{disabled:!0},model:{value:e.form.nickname,callback:function(t){e.$set(e.form,"nickname",t)},expression:"form.nickname"}})],1),a("el-form-item",{attrs:{label:"反馈内容"}},[a("el-input",{attrs:{type:"textarea",rows:4,disabled:!0},model:{value:e.form.content,callback:function(t){e.$set(e.form,"content",t)},expression:"form.content"}})],1),e.form.images?a("el-form-item",{attrs:{label:"反馈图片"}},[a("image-upload",{attrs:{disabled:!0},model:{value:e.form.images,callback:function(t){e.$set(e.form,"images",t)},expression:"form.images"}})],1):e._e(),a("el-form-item",{attrs:{label:"联系方式"}},[a("el-input",{attrs:{disabled:!0},model:{value:e.form.contact,callback:function(t){e.$set(e.form,"contact",t)},expression:"form.contact"}})],1),a("el-form-item",{attrs:{label:"提交时间"}},[a("el-input",{attrs:{disabled:!0},model:{value:e.form.createTime,callback:function(t){e.$set(e.form,"createTime",t)},expression:"form.createTime"}})],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:e.cancel}},[e._v("关 闭")])],1)],1)],1)},r=[],i=a("5530"),l=(a("d81d"),a("d3b7"),a("0643"),a("a573"),a("b775"));function o(e){return Object(l["a"])({url:"/operation/feedback/list",method:"get",params:e})}function s(e){return Object(l["a"])({url:"/operation/feedback/"+e,method:"get"})}function c(e){return Object(l["a"])({url:"/operation/feedback/"+e,method:"delete"})}var u={name:"Feedback",data:function(){return{loading:!0,filePrefix:"https://ei-image.funnyswap.com/",ids:[],single:!0,multiple:!0,showSearch:!0,total:0,feedbackList:[],title:"",open:!1,queryParams:{pageNum:1,pageSize:10,userId:null,content:null,contact:null},form:{}}},created:function(){this.getList()},methods:{getList:function(){var e=this;this.loading=!0,o(this.queryParams).then((function(t){e.feedbackList=t.rows,e.total=t.total,e.loading=!1}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={id:null,userId:null,content:null,images:null,contact:null,createTime:null},this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.id})),this.single=1!==e.length,this.multiple=!e.length},handleView:function(e){var t=this;this.reset();var a=e.id||this.ids;s(a).then((function(e){t.form=e.data,t.open=!0,t.title="查看反馈详情"}))},handleDelete:function(e){var t=this,a=e.id||this.ids;this.$modal.confirm('是否确认删除用户反馈编号为"'+a+'"的数据项？').then((function(){return c(a)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleExport:function(){this.download("operation/feedback/export",Object(i["a"])({},this.queryParams),"feedback_".concat((new Date).getTime(),".xlsx"))}}},m=u,d=a("2877"),p=Object(d["a"])(m,n,r,!1,null,null,null);t["default"]=p.exports}}]);