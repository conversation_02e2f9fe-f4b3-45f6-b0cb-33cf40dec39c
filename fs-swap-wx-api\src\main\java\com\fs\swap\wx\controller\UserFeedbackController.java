package com.fs.swap.wx.controller;

import com.fs.swap.common.annotation.RepeatSubmit;
import com.fs.swap.common.core.controller.WxApiBaseController;
import com.fs.swap.common.core.domain.AjaxResult;
import com.fs.swap.common.core.domain.entity.UserFeedback;
import com.fs.swap.common.core.page.TableDataInfo;
import com.fs.swap.common.enums.ErrorType;
import com.fs.swap.common.enums.GreenImgType;
import com.fs.swap.common.enums.GreenTextType;
import com.fs.swap.common.exception.ServiceException;
import com.fs.swap.common.green.ContentModerationFacade;
import com.fs.swap.system.service.IUserFeedbackService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 用户反馈Controller
 *
 * <AUTHOR>
 * @date 2025-06-27
 */
@RestController
@RequestMapping("/feedback")
public class UserFeedbackController extends WxApiBaseController
{
    protected final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Value("${dromara.fileDomain}")
    private String fileUrl;

    @Autowired
    private IUserFeedbackService userFeedbackService;

    @Autowired
    private ContentModerationFacade contentModerationFacade;

    /**
     * 查询用户反馈列表
     */
    @GetMapping("/list")
    public TableDataInfo list(UserFeedback userFeedback)
    {
        startPage();
        List<UserFeedback> list = userFeedbackService.selectUserFeedbackList(userFeedback);
        return getDataTable(list);
    }

    /**
     * 获取用户反馈详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(userFeedbackService.selectUserFeedbackById(id));
    }

    /**
     * 新增用户反馈
     */
    @PostMapping
    @RepeatSubmit(interval = 5000, message = "请勿重复提交反馈，请稍后再试")
    public AjaxResult add(@RequestBody UserFeedback userFeedback)
    {
        Long userId = getUserId();

        // 参数验证
        if (userFeedback.getContent() == null || userFeedback.getContent().trim().isEmpty()) {
            throw new ServiceException(ErrorType.E_400);
        }

        // 内容审核 - 反馈内容
        if (userFeedback.getContent() != null && !userFeedback.getContent().trim().isEmpty()) {
            contentModerationFacade.checkText(userFeedback.getContent(), GreenTextType.COMMENT_DETECTION.val());
        }

        // 内容审核 - 反馈图片
        if (userFeedback.getImages() != null && !userFeedback.getImages().trim().isEmpty()) {
            String[] imageUrls = userFeedback.getImages().split(",");
            for (String imageUrl : imageUrls) {
                if (!imageUrl.trim().isEmpty()) {
                    // 构建完整的图片URL
                    String fullImageUrl = fileUrl + imageUrl.trim();
                    contentModerationFacade.checkImage(fullImageUrl, GreenImgType.POST_IMAGE_CHECK.val());
                }
            }
        }

        userFeedback.setUserId(userId);
        int result = userFeedbackService.insertUserFeedback(userFeedback);

        if (result > 0) {
            logger.info("用户反馈提交成功，用户ID: {}, 反馈ID: {}", userId, userFeedback.getId());
        }

        return AjaxResult.success(result);
    }

    /**
     * 修改用户反馈
     */
    @PutMapping
    public AjaxResult edit(@RequestBody UserFeedback userFeedback)
    {
        return AjaxResult.success(userFeedbackService.updateUserFeedback(userFeedback));
    }

    /**
     * 删除用户反馈
     */
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return AjaxResult.success(userFeedbackService.deleteUserFeedbackByIds(ids));
    }

    /**
     * 获取当前用户的反馈列表
     */
    @GetMapping("/my")
    public TableDataInfo myFeedbackList()
    {
        Long userId = getUserId();
        startPage();
        List<UserFeedback> list = userFeedbackService.selectUserFeedbackListByUserId(userId);
        return getDataTable(list);
    }
}
