package com.fs.swap.admin.controller.operation;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.fs.swap.common.annotation.Log;
import com.fs.swap.common.core.controller.BaseController;
import com.fs.swap.common.core.domain.AjaxResult;
import com.fs.swap.common.enums.BusinessType;
import com.fs.swap.common.core.domain.entity.TaskConfig;
import com.fs.swap.system.service.ITaskService;
import com.fs.swap.common.utils.poi.ExcelUtil;
import com.fs.swap.common.core.page.TableDataInfo;
import com.fs.swap.common.core.redis.RedisCache;
import com.fs.swap.common.constant.CacheConstants;

/**
 * 任务配置Controller
 * 
 * <AUTHOR>
 * @date 2025-01-18
 */
@RestController
@RequestMapping("/operation/task-config")
public class TaskConfigController extends BaseController {
    
    @Autowired
    private ITaskService taskService;
    
    @Autowired
    private RedisCache redisCache;

    /**
     * 查询任务配置列表
     */
    @PreAuthorize("@ss.hasPermi('operation:task-config:list')")
    @GetMapping("/list")
    public TableDataInfo list(TaskConfig taskConfig) {
        startPage();
        List<TaskConfig> list = taskService.selectTaskConfigList(taskConfig);
        return getDataTable(list);
    }

    /**
     * 导出任务配置列表
     */
    @PreAuthorize("@ss.hasPermi('operation:task-config:export')")
    @Log(title = "任务配置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TaskConfig taskConfig) {
        List<TaskConfig> list = taskService.selectTaskConfigList(taskConfig);
        ExcelUtil<TaskConfig> util = new ExcelUtil<TaskConfig>(TaskConfig.class);
        util.exportExcel(response, list, "任务配置数据");
    }

    /**
     * 获取任务配置详细信息
     */
    @PreAuthorize("@ss.hasPermi('operation:task-config:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(taskService.selectTaskConfigById(id));
    }

    /**
     * 新增任务配置
     */
    @PreAuthorize("@ss.hasPermi('operation:task-config:add')")
    @Log(title = "任务配置", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TaskConfig taskConfig) {
        int rows = taskService.insertTaskConfig(taskConfig);
        if (rows > 0) {
            clearTaskConfigCache();
        }
        return toAjax(rows);
    }

    /**
     * 修改任务配置
     */
    @PreAuthorize("@ss.hasPermi('operation:task-config:edit')")
    @Log(title = "任务配置", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TaskConfig taskConfig) {
        int rows = taskService.updateTaskConfig(taskConfig);
        if (rows > 0) {
            clearTaskConfigCache();
        }
        return toAjax(rows);
    }

    /**
     * 删除任务配置
     */
    @PreAuthorize("@ss.hasPermi('operation:task-config:remove')")
    @Log(title = "任务配置", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        int rows = taskService.deleteTaskConfigByIds(ids);
        if (rows > 0) {
            clearTaskConfigCache();
        }
        return toAjax(rows);
    }
    
    /**
     * 清理任务配置相关缓存
     */
    private void clearTaskConfigCache() {
        // 清理任务配置缓存
        redisCache.deleteObjectByPattern(CacheConstants.TASK_CONFIG_CACHE_PREFIX);
        // 清理任务事件缓存
        redisCache.deleteObjectByPattern(CacheConstants.TASK_EVENT_CACHE_PREFIX);
        // 清理任务统计缓存
        redisCache.deleteObjectByPattern(CacheConstants.TASK_STATS_CACHE_PREFIX);
    }
} 