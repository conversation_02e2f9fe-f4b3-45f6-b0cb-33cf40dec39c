import request from '@/utils/request'

// 查询邻里互助列表
export function listCommunityHelp(query) {
  return request({
    url: '/operation/communityHelp/list',
    method: 'get',
    params: query
  })
}

// 查询邻里互助详细
export function getCommunityHelp(id) {
  return request({
    url: '/operation/communityHelp/' + id,
    method: 'get'
  })
}

// 新增邻里互助
export function addCommunityHelp(data) {
  return request({
    url: '/operation/communityHelp',
    method: 'post',
    data: data
  })
}

// 修改邻里互助
export function updateCommunityHelp(data) {
  return request({
    url: '/operation/communityHelp',
    method: 'put',
    data: data
  })
}

// 删除邻里互助
export function delCommunityHelp(id) {
  return request({
    url: '/operation/communityHelp/' + id,
    method: 'delete'
  })
}

// 审核邻里互助
export function auditCommunityHelp(id, status, auditRemark) {
  return request({
    url: `/operation/communityHelp/audit/${id}`,
    method: 'put',
    params: {
      status: status,
      auditRemark: auditRemark
    }
  })
}

// 批量审核邻里互助
export function batchAuditCommunityHelp(ids, status, auditRemark) {
  return request({
    url: '/operation/communityHelp/batchAudit',
    method: 'put',
    params: {
      ids: ids,
      status: status,
      auditRemark: auditRemark
    }
  })
}

// 导出邻里互助
export function exportCommunityHelp(query) {
  return request({
    url: '/operation/communityHelp/export',
    method: 'post',
    params: query
  })
}
