package com.fs.swap.system.service;

import java.util.Map;

/**
 * 任务统计Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-18
 */
public interface ITaskStatisticsService {
    
    /**
     * 获取用户任务统计数据
     * 
     * @param userId 用户ID
     * @return 统计数据
     */
    Map<String, Object> getUserTaskStatistics(Long userId);
    
    /**
     * 获取系统任务统计数据
     * 
     * @return 统计数据
     */
    Map<String, Object> getSystemTaskStatistics();
    
    /**
     * 获取任务排行榜
     * 
     * @param taskCode 任务编码
     * @param limit 返回数量
     * @return 排行榜数据
     */
    Map<String, Object> getTaskRanking(String taskCode, int limit);
} 