package com.fs.swap.common.enums;

/**
 * 收藏类型枚举
 * 与订单类型保持一致
 */
public enum CollectionType implements BaseEnum {
    /**
     * 商品收藏
     */
    PRODUCT("PRODUCT", "商品"),

    /**
     * 互助收藏
     */
    HELP("HELP", "互助");

    private final String code;
    private final String info;

    CollectionType(String code, String info) {
        this.code = code;
        this.info = info;
    }

    public String getCode() {
        return code;
    }

    public String getInfo() {
        return info;
    }

    @Override
    public String val() {
        return code;
    }

    /**
     * 根据编码获取收藏类型枚举
     *
     * @param code 编码
     * @return 收藏类型枚举
     */
    public static CollectionType getByCode(String code) {
        if (code == null) {
            return null;
        }
        for (CollectionType type : CollectionType.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
}
