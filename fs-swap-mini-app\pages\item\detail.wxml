<view class="product-detail-container">
  <!-- 状态栏占位 -->
  <view class="status-bar-placeholder" style="height: {{statusBarHeight}}px;"></view>

  <!-- 自定义导航栏 -->
  <view class="custom-nav-bar" style="padding-top: {{statusBarHeight}}px;">
    <view class="custom-nav-content" style="height: {{navBarHeight}}px;">
      <view class="nav-left">
        <!-- 返回按钮 -->
        <view class="back-button" bindtap="onBack" hover-class="button-hover">
          <van-icon name="arrow-left" size="22px" color="#333333" />
        </view>

        <!-- 卖家信息 -->
        <view class="nav-seller-info" bindtap="onSellerDetail" data-id="{{productDetail.userId}}" hover-class="seller-hover">
          <image src="{{productDetail.avatar || '/static/img/default_avatar.png'}}" class="nav-seller-avatar" mode="aspectFill"></image>
          <view class="seller-info-text">
            <view class="nav-seller-name">{{productDetail.nickname || '匿名用户'}}</view>
            <!-- <view class="nav-seller-location">{{productDetail.location || '  12幢'}}</view> -->
          </view>
          <!-- <view class="nav-seller-tag">{{productDetail.userTag || 'L1'}}</view> -->
          <view class="nav-seller-arrow">
            <van-icon name="arrow" size="12px" color="#999999" />
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 添加一个顶部安全区域，让内容在导航栏下方显示 -->
  <view style="height: {{navBarHeight }}px;"></view>

  <!-- 轮播图区域 -->
  <view class="swiper-container">
    <swiper class="product-images" indicator-dots="{{true}}" autoplay="{{false}}"
      interval="{{3000}}" duration="{{500}}" circular="{{true}}"
      indicator-color="rgba(255, 255, 255, 0.4)" indicator-active-color="#ffffff"
      bindchange="onSwiperChange" easing-function="linear"
      previous-margin="0rpx" next-margin="0rpx" display-multiple-items="1">
      <swiper-item wx:for="{{productImages}}" wx:key="index" class="swiper-item">
        <image src="{{item}}" mode="aspectFill" class="product-image" lazy-load="{{true}}" bindtap="previewImage" data-url="{{item}}" data-urls="{{productImages}}"></image>
      </swiper-item>
    </swiper>

    <!-- 图片计数器 -->
    <view class="image-counter">{{currentImageIndex}}/{{productImages.length || 3}}</view>
  </view>

  <!-- 主内容区域 -->
  <view class="content-wrapper">
    <!-- 价格和简略信息区域 -->
    <view class="price-info-row">
      <!-- 价格区域 -->
      <view class="price-container">
        <view wx:if="{{productDetail.price == 0}}">
          <text class="price-free">免费送</text>
        </view>
        <view wx:else>
          <text class="price-symbol">¥</text>
          <text class="price-value">{{productDetail.price}}</text>
        </view>
        <text class="shipping-tag" wx:if="{{productDetail.isFreeShipping}}">自提</text>
      </view>

      <!-- 右侧信息区域 -->
      <view class="right-info">
        <view class="usage-tag" wx:if="{{productDetail.wear}}">{{productDetail.wear}}</view>
        <view class="brief-stats">
          <text class="view-count">{{productDetail.views || 0}}浏览</text>
        </view>
      </view>
    </view>

    <!-- 商品描述 -->
    <view class="product-description">
      {{productDetail.description || '暂无描述'}}
    </view>
  </view>

  <!-- 底部操作区 -->
  <view class="bottom-actions">
    <view class="action-group left">
      <button class="action-item share-button" open-type="share" hover-class="action-hover">
        <van-icon name="share-o" size="22px" color="#1989fa" />
        <text class="share-text">分享</text>
      </button>
      <view class="action-item" bindtap="onFavorite" hover-class="action-hover">
        <van-icon name="{{isFavorite ? 'star' : 'star-o'}}" size="22px" color="{{isFavorite ? '#ff9500' : '#666666'}}" />
        <text>收藏</text>
      </view>
    </view>
    <view class="action-group right">
      <view wx:if="{{!isOwnProduct}}" class="action-button-group">
        <view class="action-button chat" bindtap="onChat" hover-class="button-hover" data-seller-id="{{productDetail.userId}}">
          <van-icon name="chat-o" size="16px" />
          <text>聊一聊</text>
        </view>
        <view class="action-button buy" bindtap="onBuy" hover-class="button-hover">
          <van-icon name="shopping-cart-o" size="16px" />
          <text>立即购买</text>
        </view>
      </view>
      <view wx:else class="action-button-group">
        <!-- 根据商品状态显示不同的按钮 -->
        <view wx:if="{{productDetail.status == '3'}}" class="action-button online" bindtap="onOnline" hover-class="button-hover">
          <text>重新上架</text>
        </view>
        <view wx:else class="action-button offline" bindtap="onOffline" hover-class="button-hover">
          <text>下架</text>
        </view>
        <view class="action-button edit" bindtap="onEdit" hover-class="button-hover">
          <text>编辑</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 购买确认弹窗 -->
  <van-dialog
    use-slot
    title="确认购买"
    show="{{ showBuyDialog }}"
    show-cancel-button
    confirm-button-open-type="getUserInfo"
    bind:close="onBuyDialogClose"
    bind:confirm="onBuyConfirm"
  >
    <view class="buy-dialog-content">
      <view class="buy-dialog-item">
        <text class="buy-dialog-label">商品：</text>
        <text class="buy-dialog-value">{{productDetail.description}}</text>
      </view>
      <view class="buy-dialog-item">
        <text class="buy-dialog-label">卖家：</text>
        <text class="buy-dialog-value">{{productDetail.nickname}}</text>
      </view>
      <view class="buy-dialog-item">
        <text class="buy-dialog-label">价格：</text>
        <text class="buy-dialog-value">
          <text wx:if="{{productDetail.price == 0}}">免费送</text>
          <text wx:else>¥{{productDetail.price}}</text>
        </text>
      </view>
      <view class="buy-dialog-item">
        <text class="buy-dialog-label">服务费：</text>
        <text class="buy-dialog-value">
          <text class="original-price">50碳豆</text>
          {{requiredSilver}}碳豆
        </text>
      </view>
      <view class="buy-dialog-notice">
        <text>购买后将从您的账户扣除相应碳豆</text>
      </view>
    </view>
  </van-dialog>

  <!-- 下架确认弹窗 -->
  <van-dialog
    title="确认下架"
    show="{{ showOfflineDialog }}"
    show-cancel-button
    cancel-button-text="取消"
    confirm-button-text="确认下架"
    bind:close="onOfflineDialogClose"
    bind:confirm="onOfflineConfirm"
  >
    <view class="offline-dialog-content">
      <text>确定要下架此商品吗？下架后其他用户将无法看到此商品。</text>
    </view>
  </van-dialog>

  <!-- 重新上架确认弹窗 -->
  <van-dialog
    title="确认上架"
    show="{{ showOnlineDialog }}"
    show-cancel-button
    cancel-button-text="取消"
    confirm-button-text="确认上架"
    bind:close="onOnlineDialogClose"
    bind:confirm="onOnlineConfirm"
  >
    <view class="online-dialog-content">
      <text>确定要重新上架此商品吗？上架后其他用户将可以看到此商品。</text>
    </view>
  </van-dialog>

  <!-- 购买结果弹窗 -->
  <van-dialog
    use-slot
    title="{{buyResultTitle}}"
    show="{{ showBuyResultDialog }}"
    confirm-button-text="{{buyResultTitle === '购买成功' ? '联系卖家' : '确定'}}"
    show-cancel-button
    cancel-button-text="查看订单"
    bind:close="onBuyResultDialogClose"
    bind:confirm="onShowContactInfo"
    bind:cancel="onViewOrder"
  >
    <view class="buy-result-content">
      <view class="success-icon" wx:if="{{buyResultTitle === '购买成功'}}">
        <van-icon name="success" size="48px" color="#07c160" />
      </view>
      <view class="result-message" wx:if="{{buyResultTitle !== '购买成功'}}">
        <text class="message-text">
          购买失败，请稍后重试
        </text>
      </view>
      <view class="contact-tip" wx:if="{{buyResultTitle === '购买成功'}}">
        <view class="tip-item">
          <van-icon name="phone-o" size="16px" color="#1989fa" />
          <text class="tip-text">点击下方"联系卖家"获取卖家联系方式</text>
        </view>
      </view>
    </view>
  </van-dialog>

  <!-- 联系方式弹窗组件 -->
  <contact-modal
    show="{{showContactModal}}"
    targetNickname="{{targetNickname}}"
    contacts="{{contacts}}"
    fileUrl="{{fileUrl}}"
    bind:close="closeContactModal"
  ></contact-modal>

  <!-- 登录组件 -->
  <login-action id="loginAction" bind:loginSuccess="onLoginSuccess" bind:loginFail="onLoginFail"></login-action>

  <!-- 小区认证弹框 -->
  <residential-auth id="residentialAuth" bind:confirm="onConfirmResidentialAuth" bind:close="onCloseResidentialAuth" />
</view>
