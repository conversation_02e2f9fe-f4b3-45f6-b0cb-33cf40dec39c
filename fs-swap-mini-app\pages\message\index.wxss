/* 消息页面样式 - 类似闲鱼的聊天列表设计 */
page {
  --primary-color: #ffffff;
  --secondary-color: #f8f9fa;
  --accent-color: #3B7FFF;
  --accent-gradient: linear-gradient(135deg, #3B7FFF, #2B6EF3);
  --text-color: #333333;
  --text-secondary: #666666;
  --text-light: #999999;
  --background-color: #f7f8fa;
  --card-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
  --hover-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.09);
  --border-radius: 16rpx;
  --transition: all 0.3s ease;
  --border-color: #eeeeee;
}

.message-container {
  height: 100vh;
  background-color: var(--background-color);
  display: flex;
  flex-direction: column;
}

/* 简单刷新指示器 */
.refresh-indicator {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
  background: rgba(59, 127, 255, 0.95);
  color: #ffffff;
  padding: 16rpx 32rpx;
  z-index: 1000;
  transition: var(--transition);
  backdrop-filter: blur(10rpx);
}

.refresh-indicator.show {
  transform: translateY(0);
  opacity: 1;
}

.refresh-indicator.hide {
  transform: translateY(-100%);
  opacity: 0;
}

.refresh-text {
  font-size: 24rpx;
  color: #ffffff;
}

/* 搜索头部 - 类似微信设计 */
.search-header {
  background: var(--primary-color);
  padding: 16rpx 32rpx 24rpx;
  border-bottom: 1rpx solid var(--border-color);
}

.search-input-wrapper {
  display: flex;
  align-items: center;
  background: var(--secondary-color);
  border-radius: 40rpx;
  padding: 0 32rpx;
  height: 80rpx;
}

.search-icon {
  font-size: 28rpx;
  color: var(--text-light);
  margin-right: 16rpx;
}

.search-input {
  flex: 1;
  font-size: 28rpx;
  color: var(--text-color);
  height: 80rpx;
  line-height: 80rpx;
}

.clear-btn {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.1);
  margin-left: 16rpx;
}

.clear-btn .iconfont {
  font-size: 24rpx;
  color: var(--text-light);
}

/* 会话列表 */
.conversation-list {
  flex: 1;
  background: var(--background-color);
}

.conversation-items {
  background: var(--primary-color);
  margin: 0 0 24rpx;
}

.conversation-item {
  display: flex;
  align-items: center;
  padding: 24rpx 32rpx;
  border-bottom: 1rpx solid var(--border-color);
  transition: var(--transition);
  position: relative;
  background: var(--primary-color);
}

.conversation-item:last-child {
  border-bottom: none;
}

.conversation-item:active {
  background-color: var(--secondary-color);
}

.conversation-item.official {
  background: linear-gradient(90deg, #fff8e1 0%, #ffffff 100%);
}

/* 头像区域 */
.avatar-wrapper {
  position: relative;
  margin-right: 24rpx;
  flex-shrink: 0;
}

.avatar {
  width: 96rpx;
  height: 96rpx;
  border-radius: 50%;
  background: var(--secondary-color);
  border: 2rpx solid var(--primary-color);
  object-fit: cover;
  object-position: center;
  display: block;
}

/* 图标头像样式 */
.icon-avatar {
  width: 96rpx;
  height: 96rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2rpx solid var(--primary-color);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.icon-avatar.system {
  background: linear-gradient(135deg, #e6f4ff 0%, #f0f9ff 100%);
}

.icon-avatar.activity {
  background: linear-gradient(135deg, #fff7e6 0%, #fffbe6 100%);
}

.official-badge {
  position: absolute;
  bottom: -4rpx;
  right: -4rpx;
  width: 32rpx;
  height: 32rpx;
  background: #52c41a;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 3rpx solid var(--primary-color);
}

.official-badge .iconfont {
  font-size: 16rpx;
  color: white;
}

.unread-badge {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  background: #ff4d4f;
  color: white;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 20rpx;
  min-width: 32rpx;
  text-align: center;
  line-height: 1;
  border: 2rpx solid var(--primary-color);
  font-weight: 500;
}

/* 会话信息 */
.conversation-info {
  flex: 1;
  min-width: 0;
  margin-right: 16rpx;
}

.info-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8rpx;
}

.name {
  font-size: 32rpx;
  font-weight: 500;
  color: var(--text-color);
  line-height: 1.4;
  flex: 1;
  margin-right: 16rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.time {
  font-size: 24rpx;
  color: var(--text-light);
  white-space: nowrap;
  flex-shrink: 0;
}

.last-message {
  font-size: 28rpx;
  color: var(--text-secondary);
  line-height: 1.4;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  overflow: hidden;
}

/* 消息状态 */
.message-status {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32rpx;
  height: 32rpx;
}

.message-status .iconfont {
  font-size: 24rpx;
  color: var(--text-light);
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 60rpx;
  text-align: center;
}

.empty-icon {
  font-size: 120rpx;
  line-height: 1;
  margin-bottom: 32rpx;
  opacity: 0.6;
}

.empty-text {
  font-size: 32rpx;
  font-weight: 500;
  color: var(--text-secondary);
  margin-bottom: 16rpx;
}

.empty-desc {
  font-size: 28rpx;
  color: var(--text-light);
  margin-bottom: 48rpx;
}

/* 登录提示按钮 */
.login-tip {
  margin-top: 32rpx;
  padding: 20rpx 40rpx;
  background: var(--accent-gradient);
  color: white;
  border-radius: 40rpx;
  font-size: 28rpx;
  font-weight: 500;
  transition: var(--transition);
  display: inline-block;
}

.login-tip:active {
  transform: scale(0.95);
  opacity: 0.9;
}

.login-btn-text {
  color: white;
  font-size: 28rpx;
  font-weight: 500;
}

.empty-action {
  padding: 20rpx 40rpx;
  background: var(--accent-gradient);
  color: white;
  border-radius: 40rpx;
  font-size: 28rpx;
  font-weight: 500;
  transition: var(--transition);
}

.empty-action:active {
  transform: scale(0.95);
  opacity: 0.9;
}

/* 加载状态 */
.loading-state {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
  gap: 16rpx;
}

.loading-text {
  font-size: 26rpx;
  color: var(--text-secondary);
}

/* 响应式设计 */
@media screen and (max-width: 375px) {
  .conversation-item {
    padding: 20rpx 24rpx;
  }
  
  .avatar {
    width: 80rpx;
    height: 80rpx;
    object-fit: cover;
    object-position: center;
  }
  
  .name {
    font-size: 30rpx;
  }
  
  .last-message {
    font-size: 26rpx;
  }
  
  /* 小屏幕头部优化 */
  .header {
    padding: 28rpx 24rpx 20rpx;
  }
  
  .header-left .title {
    font-size: 32rpx;
  }
  
  .search-btn,
  .add-btn {
    width: 64rpx;
    height: 64rpx;
  }
  
  .search-btn .iconfont,
  .add-btn .iconfont {
    font-size: 28rpx;
  }
  
  /* 搜索栏优化 */
  .search-bar.show {
    padding: 0 24rpx 20rpx;
  }
  
  .search-input-wrapper {
    height: 72rpx;
    padding: 0 28rpx;
  }
  
  .search-input {
    height: 72rpx;
    line-height: 72rpx;
    font-size: 26rpx;
  }
}

/* 超小屏幕适配 */
@media screen and (max-width: 320px) {
  .conversation-item {
    padding: 16rpx 20rpx;
  }
  
  .avatar {
    width: 72rpx;
    height: 72rpx;
    object-fit: cover;
    object-position: center;
  }
  
  .avatar-wrapper {
    margin-right: 20rpx;
  }
  
  .name {
    font-size: 28rpx;
  }
  
  .last-message {
    font-size: 24rpx;
  }
  
  .time {
    font-size: 22rpx;
  }
  
  /* 超小屏幕头部进一步优化 */
  .header {
    padding: 24rpx 20rpx 16rpx;
  }
  
  .header-left .title {
    font-size: 30rpx;
  }
  
  .header-right {
    gap: 16rpx;
  }
  
  .search-btn,
  .add-btn {
    width: 56rpx;
    height: 56rpx;
  }
  
  .search-btn .iconfont,
  .add-btn .iconfont {
    font-size: 24rpx;
  }
  
  /* 超小屏幕搜索栏优化 */
  .search-bar.show {
    padding: 0 20rpx 16rpx;
  }
  
  .search-input-wrapper {
    height: 64rpx;
    padding: 0 24rpx;
  }
  
  .search-input {
    height: 64rpx;
    line-height: 64rpx;
    font-size: 24rpx;
  }
  
  .search-icon {
    font-size: 24rpx;
    margin-right: 12rpx;
  }
  
  .clear-btn {
    width: 40rpx;
    height: 40rpx;
    margin-left: 12rpx;
  }
  
  .clear-btn .iconfont {
    font-size: 20rpx;
  }
}

/* 动画效果 */
.conversation-item {
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
} 