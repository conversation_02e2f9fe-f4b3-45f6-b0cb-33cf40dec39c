(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d21b8fa"],{bfff:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",attrs:{model:e.queryParams,size:"small",inline:!0,"label-width":"68px"}},[a("el-form-item",{attrs:{label:"用户",prop:"userId"}},[a("el-input",{attrs:{placeholder:"请输入用户",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handle<PERSON><PERSON>y(t)}},model:{value:e.queryParams.userId,callback:function(t){e.$set(e.queryParams,"userId",t)},expression:"queryParams.userId"}})],1),a("el-form-item",{attrs:{label:"小区",prop:"residentialId"}},[a("el-input",{attrs:{placeholder:"请输入小区",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.residentialId,callback:function(t){e.$set(e.queryParams,"residentialId",t)},expression:"queryParams.residentialId"}})],1),a("el-form-item",{attrs:{label:"楼栋名称",prop:"buildingName"}},[a("el-input",{attrs:{placeholder:"请输入楼栋名称",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.buildingName,callback:function(t){e.$set(e.queryParams,"buildingName",t)},expression:"queryParams.buildingName"}})],1),a("el-form-item",{attrs:{label:"单元号",prop:"unitNumber"}},[a("el-input",{attrs:{placeholder:"请输入单元号",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.unitNumber,callback:function(t){e.$set(e.queryParams,"unitNumber",t)},expression:"queryParams.unitNumber"}})],1),a("el-form-item",{attrs:{label:"门牌号",prop:"roomNumber"}},[a("el-input",{attrs:{placeholder:"请输入门牌号",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.roomNumber,callback:function(t){e.$set(e.queryParams,"roomNumber",t)},expression:"queryParams.roomNumber"}})],1),a("el-form-item",{attrs:{label:"房屋类型",prop:"homeType"}},[a("el-select",{attrs:{placeholder:"请选择房屋类型",clearable:""},model:{value:e.queryParams.homeType,callback:function(t){e.$set(e.queryParams,"homeType",t)},expression:"queryParams.homeType"}},e._l(e.dict.type.home_type,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",{attrs:{label:"认证状态",prop:"certificationStatus"}},[a("el-select",{attrs:{placeholder:"请选择认证状态",clearable:""},model:{value:e.queryParams.certificationStatus,callback:function(t){e.$set(e.queryParams,"certificationStatus",t)},expression:"queryParams.certificationStatus"}},e._l(e.dict.type.home_certification_status,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",{attrs:{label:"是否选中",prop:"defaultIs"}},[a("el-input",{attrs:{placeholder:"请输入是否选中",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.defaultIs,callback:function(t){e.$set(e.queryParams,"defaultIs",t)},expression:"queryParams.defaultIs"}})],1),a("el-form-item",{attrs:{label:"认证通过时间",prop:"certificationTime"}},[a("el-date-picker",{attrs:{clearable:"",type:"date","value-format":"yyyy-MM-dd",placeholder:"请选择认证通过时间"},model:{value:e.queryParams.certificationTime,callback:function(t){e.$set(e.queryParams,"certificationTime",t)},expression:"queryParams.certificationTime"}})],1),a("el-form-item",{attrs:{label:"逻辑删除标识",prop:"deleted"}},[a("el-select",{attrs:{placeholder:"请选择逻辑删除标识",clearable:""},model:{value:e.queryParams.deleted,callback:function(t){e.$set(e.queryParams,"deleted",t)},expression:"queryParams.deleted"}},e._l(e.dict.type.sys_normal_disable,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),a("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1),a("el-row",{staticClass:"mb8",attrs:{gutter:10}},[a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["operation:userHome:add"],expression:"['operation:userHome:add']"}],attrs:{type:"primary",plain:"",icon:"el-icon-plus",size:"mini"},on:{click:e.handleAdd}},[e._v("新增")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["operation:userHome:edit"],expression:"['operation:userHome:edit']"}],attrs:{type:"success",plain:"",icon:"el-icon-edit",size:"mini",disabled:e.single},on:{click:e.handleUpdate}},[e._v("修改")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["operation:userHome:remove"],expression:"['operation:userHome:remove']"}],attrs:{type:"danger",plain:"",icon:"el-icon-delete",size:"mini",disabled:e.multiple},on:{click:e.handleDelete}},[e._v("删除")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["operation:userHome:export"],expression:"['operation:userHome:export']"}],attrs:{type:"warning",plain:"",icon:"el-icon-download",size:"mini"},on:{click:e.handleExport}},[e._v("导出")])],1),a("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.userHomeList},on:{"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"55",align:"center"}}),a("el-table-column",{attrs:{label:"id",align:"center",prop:"id"}}),a("el-table-column",{attrs:{label:"用户",align:"center",prop:"userId"}}),a("el-table-column",{attrs:{label:"小区",align:"center",prop:"residentialId"}}),a("el-table-column",{attrs:{label:"楼栋名称",align:"center",prop:"buildingName"}}),a("el-table-column",{attrs:{label:"单元号",align:"center",prop:"unitNumber"}}),a("el-table-column",{attrs:{label:"门牌号",align:"center",prop:"roomNumber"}}),a("el-table-column",{attrs:{label:"房屋类型",align:"center",prop:"homeType"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("dict-tag",{attrs:{options:e.dict.type.home_type,value:t.row.homeType}})]}}])}),a("el-table-column",{attrs:{label:"认证状态",align:"center",prop:"certificationStatus"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("dict-tag",{attrs:{options:e.dict.type.home_certification_status,value:t.row.certificationStatus}})]}}])}),a("el-table-column",{attrs:{label:"是否选中",align:"center",prop:"defaultIs"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("dict-tag",{attrs:{options:e.dict.type.sys_yes_no,value:t.row.defaultIs}})]}}])}),a("el-table-column",{attrs:{label:"认证通过时间",align:"center",prop:"certificationTime",width:"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(e.parseTime(t.row.certificationTime,"{y}-{m}-{d}")))])]}}])}),a("el-table-column",{attrs:{label:"逻辑删除标识",align:"center",prop:"deleted"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("dict-tag",{attrs:{options:e.dict.type.sys_normal_disable,value:t.row.deleted}})]}}])}),a("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["operation:userHome:edit"],expression:"['operation:userHome:edit']"}],attrs:{size:"mini",type:"text",icon:"el-icon-edit"},on:{click:function(a){return e.handleUpdate(t.row)}}},[e._v("修改")]),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["operation:userHome:remove"],expression:"['operation:userHome:remove']"}],attrs:{size:"mini",type:"text",icon:"el-icon-delete"},on:{click:function(a){return e.handleDelete(t.row)}}},[e._v("删除")])]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}}),a("el-dialog",{attrs:{title:e.title,visible:e.open,width:"500px","append-to-body":""},on:{"update:visible":function(t){e.open=t}}},[a("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"80px"}},[a("el-form-item",{attrs:{label:"用户",prop:"userId"}},[a("el-input",{attrs:{placeholder:"请输入用户"},model:{value:e.form.userId,callback:function(t){e.$set(e.form,"userId",t)},expression:"form.userId"}})],1),a("el-form-item",{attrs:{label:"小区",prop:"residentialId"}},[a("el-input",{attrs:{placeholder:"请输入小区"},model:{value:e.form.residentialId,callback:function(t){e.$set(e.form,"residentialId",t)},expression:"form.residentialId"}})],1),a("el-form-item",{attrs:{label:"楼栋名称",prop:"buildingName"}},[a("el-input",{attrs:{placeholder:"请输入楼栋名称"},model:{value:e.form.buildingName,callback:function(t){e.$set(e.form,"buildingName",t)},expression:"form.buildingName"}})],1),a("el-form-item",{attrs:{label:"单元号",prop:"unitNumber"}},[a("el-input",{attrs:{placeholder:"请输入单元号"},model:{value:e.form.unitNumber,callback:function(t){e.$set(e.form,"unitNumber",t)},expression:"form.unitNumber"}})],1),a("el-form-item",{attrs:{label:"门牌号",prop:"roomNumber"}},[a("el-input",{attrs:{placeholder:"请输入门牌号"},model:{value:e.form.roomNumber,callback:function(t){e.$set(e.form,"roomNumber",t)},expression:"form.roomNumber"}})],1),a("el-form-item",{attrs:{label:"房屋类型",prop:"homeType"}},[a("el-select",{attrs:{placeholder:"请选择房屋类型"},model:{value:e.form.homeType,callback:function(t){e.$set(e.form,"homeType",t)},expression:"form.homeType"}},e._l(e.dict.type.home_type,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",{attrs:{label:"认证状态",prop:"certificationStatus"}},[a("el-select",{attrs:{placeholder:"请选择认证状态"},model:{value:e.form.certificationStatus,callback:function(t){e.$set(e.form,"certificationStatus",t)},expression:"form.certificationStatus"}},e._l(e.dict.type.home_certification_status,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",{attrs:{label:"是否选中",prop:"defaultIs"}},[a("el-input",{attrs:{placeholder:"请输入是否选中"},model:{value:e.form.defaultIs,callback:function(t){e.$set(e.form,"defaultIs",t)},expression:"form.defaultIs"}})],1),a("el-form-item",{attrs:{label:"逻辑删除标识",prop:"deleted"}},[a("el-select",{attrs:{placeholder:"请选择逻辑删除标识"},model:{value:e.form.deleted,callback:function(t){e.$set(e.form,"deleted",t)},expression:"form.deleted"}},e._l(e.dict.type.sys_normal_disable,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:parseInt(e.value)}})})),1)],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),a("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1)],1)},l=[],i=a("5530"),n=(a("d81d"),a("d3b7"),a("0643"),a("a573"),a("b775"));function o(e){return Object(n["a"])({url:"/operation/userHome/list",method:"get",params:e})}function s(e){return Object(n["a"])({url:"/operation/userHome/"+e,method:"get"})}function u(e){return Object(n["a"])({url:"/operation/userHome",method:"post",data:e})}function m(e){return Object(n["a"])({url:"/operation/userHome",method:"put",data:e})}function c(e){return Object(n["a"])({url:"/operation/userHome/"+e,method:"delete"})}var d={name:"UserHome",dicts:["home_certification_status","home_type","sys_normal_disable"],data:function(){return{loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,userHomeList:[],title:"",open:!1,queryParams:{pageNum:1,pageSize:10,userId:null,residentialId:null,buildingName:null,unitNumber:null,roomNumber:null,homeType:null,certificationStatus:null,defaultIs:null,certificationTime:null,deleted:null},form:{},rules:{userId:[{required:!0,message:"用户不能为空",trigger:"blur"}],residentialId:[{required:!0,message:"小区不能为空",trigger:"blur"}],buildingName:[{required:!0,message:"楼栋名称不能为空",trigger:"blur"}],roomNumber:[{required:!0,message:"门牌号不能为空",trigger:"blur"}],certificationStatus:[{required:!0,message:"认证状态不能为空",trigger:"change"}],defaultIs:[{required:!0,message:"是否选中不能为空",trigger:"blur"}],deleted:[{required:!0,message:"逻辑删除标识不能为空",trigger:"change"}]}}},created:function(){this.getList()},methods:{getList:function(){var e=this;this.loading=!0,o(this.queryParams).then((function(t){e.userHomeList=t.rows,e.total=t.total,e.loading=!1}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={id:null,userId:null,residentialId:null,buildingName:null,unitNumber:null,roomNumber:null,homeType:null,certificationStatus:null,defaultIs:null,certificationTime:null,createTime:null,updateTime:null,deleted:null},this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.id})),this.single=1!==e.length,this.multiple=!e.length},handleAdd:function(){this.reset(),this.open=!0,this.title="添加用户房产"},handleUpdate:function(e){var t=this;this.reset();var a=e.id||this.ids;s(a).then((function(e){t.form=e.data,t.open=!0,t.title="修改用户房产"}))},submitForm:function(){var e=this;this.$refs["form"].validate((function(t){t&&(null!=e.form.id?m(e.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.open=!1,e.getList()})):u(e.form).then((function(t){e.$modal.msgSuccess("新增成功"),e.open=!1,e.getList()})))}))},handleDelete:function(e){var t=this,a=e.id||this.ids;this.$modal.confirm('是否确认删除用户房产编号为"'+a+'"的数据项？').then((function(){return c(a)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleExport:function(){this.download("operation/userHome/export",Object(i["a"])({},this.queryParams),"userHome_".concat((new Date).getTime(),".xlsx"))}}},p=d,f=a("2877"),b=Object(f["a"])(p,r,l,!1,null,null,null);t["default"]=b.exports}}]);