<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fs.swap.system.mapper.ActivitySignupMapper">

    <resultMap type="ActivitySignup" id="ActivitySignupResult">
        <result property="id" column="id"/>
        <result property="activityId" column="activity_id"/>
        <result property="userId" column="user_id"/>
        <result property="status" column="status"/>
        <result property="signupTime" column="signup_time"/>

        <result property="additionalInfo" column="additional_info"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="nickname" column="nickname"/>
        <result property="avatar" column="avatar"/>
        <result property="activityTitle" column="activity_title"/>
        <result property="activityImages" column="activity_images"/>
    </resultMap>

    <sql id="selectActivitySignupVo">
        select s.id,
               s.activity_id,
               s.user_id,
               s.status,
               s.signup_time,
               s.additional_info,
               s.create_time,
               s.update_time,
               u.nickname,
               u.avatar,
               a.title  as activity_title,
               a.images as activity_images
        from activity_signup s
                 left join user_info u on s.user_id = u.id
                 left join activity a on s.activity_id = a.id
    </sql>

    <select id="selectActivitySignupList" parameterType="ActivitySignup" resultMap="ActivitySignupResult">
        <include refid="selectActivitySignupVo"/>
        <where>
            <if test="activityId != null ">and s.activity_id = #{activityId}</if>
            <if test="userId != null ">and s.user_id = #{userId}</if>
            and s.status in ('0', '1') <!-- 待审核和已通过的报名 -->
        </where>
        order by s.signup_time desc
    </select>

    <select id="selectActivitySignupById" parameterType="Long" resultMap="ActivitySignupResult">
        <include refid="selectActivitySignupVo"/>
        where s.id = #{id}
    </select>

    <select id="selectActivitySignupListByActivityId" parameterType="Long" resultMap="ActivitySignupResult">
        <include refid="selectActivitySignupVo"/>
        where s.activity_id = #{activityId}
        order by s.signup_time desc
    </select>

    <select id="selectActivitySignupListByUserId" parameterType="Long" resultMap="ActivitySignupResult">
        <include refid="selectActivitySignupVo"/>
        where s.user_id = #{userId}
        order by s.signup_time desc
    </select>

    <select id="selectActivitySignupByActivityIdAndUserId" resultMap="ActivitySignupResult">
        <include refid="selectActivitySignupVo"/>
        where s.activity_id = #{activityId} and s.user_id = #{userId}
        limit 1
    </select>

    <select id="getActivitySignupCount" parameterType="Long" resultType="int">
        select count(1) from activity_signup
        where activity_id = #{activityId}
        and status in ('0', '1') <!-- 待审核和已通过的报名 -->
    </select>

    <insert id="insertActivitySignup" parameterType="ActivitySignup" useGeneratedKeys="true" keyProperty="id">
        insert into activity_signup
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="activityId != null">activity_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="status != null">status,</if>
            <if test="signupTime != null">signup_time,</if>

            <if test="additionalInfo != null">additional_info,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="activityId != null">#{activityId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="status != null">#{status},</if>
            <if test="signupTime != null">#{signupTime},</if>

            <if test="additionalInfo != null">#{additionalInfo},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateActivitySignup" parameterType="ActivitySignup">
        update activity_signup
        <trim prefix="SET" suffixOverrides=",">
            <if test="activityId != null">activity_id = #{activityId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="status != null">status = #{status},</if>
            <if test="signupTime != null">signup_time = #{signupTime},</if>

            <if test="additionalInfo != null">additional_info = #{additionalInfo},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteActivitySignupById" parameterType="Long">
        delete
        from activity_signup
        where id = #{id}
    </delete>

    <delete id="deleteActivitySignupByIds" parameterType="String">
        delete from activity_signup where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
