const app = getApp()
const api = require('../../config/api')

Page({
  data: {
    tasks: [],
    loading: true,
    isClaiming: false
  },

  onLoad() {
    this.loadTaskData()
  },

  /**
   * 下拉刷新
   */
  async onPullDownRefresh() {
    try {
      await this.loadTaskData()
    } catch (error) {
      console.error('刷新任务列表失败:', error)
    } finally {
      wx.stopPullDownRefresh()
    }
  },

  /**
   * 加载任务数据
   */
  async loadTaskData() {
    this.setData({ loading: true })
    
    try {
      // 只请求任务列表
      const tasksRes = await api.getTaskList()

      if (tasksRes && tasksRes.code === 200) {
        const tasks = Array.isArray(tasksRes.data) ? tasksRes.data : []
        
        // 确保任务数据的完整性
        const processedTasks = tasks.map(task => ({
          id: task.id || '',
          taskCode: task.taskCode || '',
          taskName: task.taskName || '任务名称',
          taskDesc: task.taskDesc || '任务描述',
          taskType: task.taskType || 'DAILY',
          taskTypeName: task.taskTypeName || '每日任务',
          status: task.status || 0,
          statusName: task.statusName || '进行中',
          currentCount: task.currentCount || 0,
          targetCount: task.targetCount || 1,
          progressPercent: task.progressPercent || 0,
          rewardSilver: task.rewardSilver || 0,
          canClaim: task.canClaim || false,
          isCompleted: task.isCompleted || false,
          page: task.page || ''
        }))

        this.setData({
          tasks: processedTasks,
          loading: false
        })
      } else {
        throw new Error('数据格式错误')
      }
    } catch (error) {
      console.error('加载任务数据失败:', error)
      wx.showToast({
        title: '加载失败',
        icon: 'error'
      })
      this.setData({ 
        loading: false,
        tasks: []
      })
    }
  },

  /**
   * 领取单个任务奖励
   */
  async claimReward(e) {
    if (this.data.isClaiming) {
      return
    }

    const taskId = e.currentTarget.dataset.taskId
    if (!taskId) {
      console.error('任务ID不存在')
      wx.showToast({
        title: '任务ID不存在',
        icon: 'error'
      })
      return
    }

    try {
      this.setData({ isClaiming: true })
      wx.showLoading({ title: '领取中...' })
      
      const res = await api.claimTaskReward(taskId)
      if (res && res.code === 200) {
        wx.showToast({
          title: '领取成功',
          icon: 'success'
        })
        
        // 重新加载任务列表
        await this.loadTaskData()
      } else {
        throw new Error(res?.msg || '领取失败')
      }
    } catch (error) {
      console.error('领取奖励失败:', error)
      wx.showToast({
        title: error.message || '领取失败',
        icon: 'error'
      })
    } finally {
      wx.hideLoading()
      this.setData({ isClaiming: false })
    }
  },

  /**
   * 跳转到任务相关页面
   */
  goToTask(e) {
    const taskCode = e.currentTarget.dataset.taskCode
    const task = this.data.tasks.find(t => t.taskCode === taskCode)
    
    if (task && task.page) {
      // 确保路径以 / 开头
      const pagePath = task.page.startsWith('/') ? task.page : `/${task.page}`
      
      // 判断是否是 tabbar 页面
      const tabbarPages = ['/pages/index/index', '/pages/message/index', '/pages/publish/index', '/pages/activity/list/index', '/pages/user/index']
      
      if (tabbarPages.includes(pagePath)) {
        wx.switchTab({
          url: pagePath,
          fail: (err) => {
            console.error('页面跳转失败:', err)
            wx.showToast({
              title: '页面跳转失败',
              icon: 'none'
            })
          }
        })
      } else {
        wx.navigateTo({
          url: pagePath,
          fail: (err) => {
            console.error('页面跳转失败:', err)
            wx.showToast({
              title: '页面跳转失败',
              icon: 'none'
            })
          }
        })
      }
    } else {
      wx.showToast({
        title: '功能开发中',
        icon: 'none'
      })
    }
  },

  /**
   * 查看任务详情（预留功能）
   */
  viewTaskDetail(e) {
    const taskCode = e.currentTarget.dataset.taskCode
    wx.navigateTo({
      url: `/pages/task-detail/task-detail?taskCode=${taskCode}`
    })
  },

  /**
   * 分享页面
   */
  onShareAppMessage() {
    return {
      title: '任务中心 - 完成任务赚碳豆',
      path: '/pages/task/task',
      imageUrl: '/images/share-task.png'
    }
  }
}) 