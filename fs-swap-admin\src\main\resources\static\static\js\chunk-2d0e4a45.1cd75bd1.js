(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d0e4a45"],{"90a9":function(e,t,r){"use strict";r.r(t);var l=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticClass:"app-container"},[r("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",attrs:{model:e.queryParams,size:"small",inline:!0,"label-width":"68px"}},[r("el-form-item",{attrs:{label:"订单编号",prop:"orderNo"}},[r("el-input",{attrs:{placeholder:"请输入订单编号",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.orderNo,callback:function(t){e.$set(e.queryParams,"orderNo",t)},expression:"queryParams.orderNo"}})],1),r("el-form-item",{attrs:{label:"订单类型",prop:"orderType"}},[r("el-select",{attrs:{placeholder:"请选择订单类型",clearable:""},model:{value:e.queryParams.orderType,callback:function(t){e.$set(e.queryParams,"orderType",t)},expression:"queryParams.orderType"}},e._l(e.dict.type.order_type,(function(e){return r("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),r("el-form-item",{attrs:{label:"购买用户",prop:"buyerId"}},[r("el-input",{attrs:{placeholder:"请输入购买用户",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.buyerId,callback:function(t){e.$set(e.queryParams,"buyerId",t)},expression:"queryParams.buyerId"}})],1),r("el-form-item",{attrs:{label:"出售用户",prop:"sellerId"}},[r("el-input",{attrs:{placeholder:"请输入出售用户",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.sellerId,callback:function(t){e.$set(e.queryParams,"sellerId",t)},expression:"queryParams.sellerId"}})],1),r("el-form-item",{attrs:{label:"业务ID",prop:"businessId"}},[r("el-input",{attrs:{placeholder:"请输入业务ID",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.businessId,callback:function(t){e.$set(e.queryParams,"businessId",t)},expression:"queryParams.businessId"}})],1),r("el-form-item",{attrs:{label:"积分",prop:"points"}},[r("el-input",{attrs:{placeholder:"请输入积分",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.points,callback:function(t){e.$set(e.queryParams,"points",t)},expression:"queryParams.points"}})],1),r("el-form-item",{attrs:{label:"订单状态",prop:"status"}},[r("el-select",{attrs:{placeholder:"请选择订单状态",clearable:""},model:{value:e.queryParams.status,callback:function(t){e.$set(e.queryParams,"status",t)},expression:"queryParams.status"}},e._l(e.dict.type.order_status,(function(e){return r("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),r("el-form-item",{attrs:{label:"完成时间",prop:"completeTime"}},[r("el-date-picker",{attrs:{clearable:"",type:"date","value-format":"yyyy-MM-dd",placeholder:"请选择完成时间"},model:{value:e.queryParams.completeTime,callback:function(t){e.$set(e.queryParams,"completeTime",t)},expression:"queryParams.completeTime"}})],1),r("el-form-item",{attrs:{label:"是否删除",prop:"deleted"}},[r("el-input",{attrs:{placeholder:"请输入是否删除",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.deleted,callback:function(t){e.$set(e.queryParams,"deleted",t)},expression:"queryParams.deleted"}})],1),r("el-form-item",[r("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),r("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1),r("el-row",{staticClass:"mb8",attrs:{gutter:10}},[r("el-col",{attrs:{span:1.5}},[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["operation:order:add"],expression:"['operation:order:add']"}],attrs:{type:"primary",plain:"",icon:"el-icon-plus",size:"mini"},on:{click:e.handleAdd}},[e._v("新增")])],1),r("el-col",{attrs:{span:1.5}},[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["operation:order:edit"],expression:"['operation:order:edit']"}],attrs:{type:"success",plain:"",icon:"el-icon-edit",size:"mini",disabled:e.single},on:{click:e.handleUpdate}},[e._v("修改")])],1),r("el-col",{attrs:{span:1.5}},[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["operation:order:remove"],expression:"['operation:order:remove']"}],attrs:{type:"danger",plain:"",icon:"el-icon-delete",size:"mini",disabled:e.multiple},on:{click:e.handleDelete}},[e._v("删除")])],1),r("el-col",{attrs:{span:1.5}},[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["operation:order:export"],expression:"['operation:order:export']"}],attrs:{type:"warning",plain:"",icon:"el-icon-download",size:"mini"},on:{click:e.handleExport}},[e._v("导出")])],1),r("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1),r("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.orderList},on:{"selection-change":e.handleSelectionChange}},[r("el-table-column",{attrs:{type:"selection",width:"55",align:"center"}}),r("el-table-column",{attrs:{label:"id",align:"center",prop:"id"}}),r("el-table-column",{attrs:{label:"订单编号",align:"center",prop:"orderNo"}}),r("el-table-column",{attrs:{label:"订单类型",align:"center",prop:"orderType"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("dict-tag",{attrs:{options:e.dict.type.order_type,value:t.row.orderType}})]}}])}),r("el-table-column",{attrs:{label:"购买用户",align:"center",prop:"buyerId"}}),r("el-table-column",{attrs:{label:"出售用户",align:"center",prop:"sellerId"}}),r("el-table-column",{attrs:{label:"业务ID",align:"center",prop:"businessId"}}),r("el-table-column",{attrs:{label:"积分",align:"center",prop:"points"}}),r("el-table-column",{attrs:{label:"订单状态",align:"center",prop:"status"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("dict-tag",{attrs:{options:e.dict.type.order_status,value:t.row.status}})]}}])}),r("el-table-column",{attrs:{label:"纠纷原因",align:"center",prop:"disputeReason"}}),r("el-table-column",{attrs:{label:"完成时间",align:"center",prop:"completeTime",width:"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("span",[e._v(e._s(e.parseTime(t.row.completeTime,"{y}-{m}-{d}")))])]}}])}),r("el-table-column",{attrs:{label:"备注",align:"center",prop:"remark"}}),r("el-table-column",{attrs:{label:"是否删除",align:"center",prop:"deleted"}}),r("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["operation:order:edit"],expression:"['operation:order:edit']"}],attrs:{size:"mini",type:"text",icon:"el-icon-edit"},on:{click:function(r){return e.handleUpdate(t.row)}}},[e._v("修改")]),r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["operation:order:remove"],expression:"['operation:order:remove']"}],attrs:{size:"mini",type:"text",icon:"el-icon-delete"},on:{click:function(r){return e.handleDelete(t.row)}}},[e._v("删除")])]}}])})],1),r("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}}),r("el-dialog",{attrs:{title:e.title,visible:e.open,width:"500px","append-to-body":""},on:{"update:visible":function(t){e.open=t}}},[r("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"80px"}},[r("el-form-item",{attrs:{label:"订单编号",prop:"orderNo"}},[r("el-input",{attrs:{placeholder:"请输入订单编号"},model:{value:e.form.orderNo,callback:function(t){e.$set(e.form,"orderNo",t)},expression:"form.orderNo"}})],1),r("el-form-item",{attrs:{label:"购买用户",prop:"buyerId"}},[r("el-input",{attrs:{placeholder:"请输入购买用户"},model:{value:e.form.buyerId,callback:function(t){e.$set(e.form,"buyerId",t)},expression:"form.buyerId"}})],1),r("el-form-item",{attrs:{label:"出售用户",prop:"sellerId"}},[r("el-input",{attrs:{placeholder:"请输入出售用户"},model:{value:e.form.sellerId,callback:function(t){e.$set(e.form,"sellerId",t)},expression:"form.sellerId"}})],1),r("el-form-item",{attrs:{label:"业务ID",prop:"businessId"}},[r("el-input",{attrs:{placeholder:"请输入业务ID"},model:{value:e.form.businessId,callback:function(t){e.$set(e.form,"businessId",t)},expression:"form.businessId"}})],1),r("el-form-item",{attrs:{label:"积分",prop:"points"}},[r("el-input",{attrs:{placeholder:"请输入积分"},model:{value:e.form.points,callback:function(t){e.$set(e.form,"points",t)},expression:"form.points"}})],1),r("el-form-item",{attrs:{label:"订单状态",prop:"status"}},[r("el-select",{attrs:{placeholder:"请选择订单状态"},model:{value:e.form.status,callback:function(t){e.$set(e.form,"status",t)},expression:"form.status"}},e._l(e.dict.type.order_status,(function(e){return r("el-option",{key:e.value,attrs:{label:e.label,value:parseInt(e.value)}})})),1)],1),r("el-form-item",{attrs:{label:"纠纷原因",prop:"disputeReason"}},[r("el-input",{attrs:{type:"textarea",placeholder:"请输入内容"},model:{value:e.form.disputeReason,callback:function(t){e.$set(e.form,"disputeReason",t)},expression:"form.disputeReason"}})],1),r("el-form-item",{attrs:{label:"完成时间",prop:"completeTime"}},[r("el-date-picker",{attrs:{clearable:"",type:"date","value-format":"yyyy-MM-dd",placeholder:"请选择完成时间"},model:{value:e.form.completeTime,callback:function(t){e.$set(e.form,"completeTime",t)},expression:"form.completeTime"}})],1),r("el-form-item",{attrs:{label:"备注",prop:"remark"}},[r("el-input",{attrs:{placeholder:"请输入备注"},model:{value:e.form.remark,callback:function(t){e.$set(e.form,"remark",t)},expression:"form.remark"}})],1),r("el-form-item",{attrs:{label:"是否删除",prop:"deleted"}},[r("el-input",{attrs:{placeholder:"请输入是否删除"},model:{value:e.form.deleted,callback:function(t){e.$set(e.form,"deleted",t)},expression:"form.deleted"}})],1)],1),r("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[r("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),r("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1)],1)},a=[],o=r("5530"),n=(r("d81d"),r("d3b7"),r("0643"),r("a573"),r("b775"));function s(e){return Object(n["a"])({url:"/operation/order/list",method:"get",params:e})}function i(e){return Object(n["a"])({url:"/operation/order/"+e,method:"get"})}function u(e){return Object(n["a"])({url:"/operation/order",method:"post",data:e})}function d(e){return Object(n["a"])({url:"/operation/order",method:"put",data:e})}function c(e){return Object(n["a"])({url:"/operation/order/"+e,method:"delete"})}var p={name:"Order",dicts:["order_status","order_type"],data:function(){return{loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,orderList:[],title:"",open:!1,queryParams:{pageNum:1,pageSize:10,orderNo:null,orderType:null,buyerId:null,sellerId:null,businessId:null,points:null,status:null,disputeReason:null,completeTime:null,deleted:null},form:{},rules:{orderNo:[{required:!0,message:"订单编号不能为空",trigger:"blur"}],buyerId:[{required:!0,message:"购买用户不能为空",trigger:"blur"}],sellerId:[{required:!0,message:"出售用户不能为空",trigger:"blur"}],businessId:[{required:!0,message:"业务ID不能为空",trigger:"blur"}],points:[{required:!0,message:"积分不能为空",trigger:"blur"}],deleted:[{required:!0,message:"是否删除不能为空",trigger:"blur"}]}}},created:function(){this.getList()},methods:{getList:function(){var e=this;this.loading=!0,s(this.queryParams).then((function(t){e.orderList=t.rows,e.total=t.total,e.loading=!1}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={id:null,orderNo:null,orderType:null,buyerId:null,sellerId:null,businessId:null,points:null,status:null,disputeReason:null,completeTime:null,remark:null,deleted:null,createTime:null,updateTime:null},this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.id})),this.single=1!==e.length,this.multiple=!e.length},handleAdd:function(){this.reset(),this.open=!0,this.title="添加交易订单"},handleUpdate:function(e){var t=this;this.reset();var r=e.id||this.ids;i(r).then((function(e){t.form=e.data,t.open=!0,t.title="修改交易订单"}))},submitForm:function(){var e=this;this.$refs["form"].validate((function(t){t&&(null!=e.form.id?d(e.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.open=!1,e.getList()})):u(e.form).then((function(t){e.$modal.msgSuccess("新增成功"),e.open=!1,e.getList()})))}))},handleDelete:function(e){var t=this,r=e.id||this.ids;this.$modal.confirm('是否确认删除交易订单编号为"'+r+'"的数据项？').then((function(){return c(r)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleExport:function(){this.download("operation/order/export",Object(o["a"])({},this.queryParams),"order_".concat((new Date).getTime(),".xlsx"))}}},m=p,f=r("2877"),b=Object(f["a"])(m,l,a,!1,null,null,null);t["default"]=b.exports}}]);