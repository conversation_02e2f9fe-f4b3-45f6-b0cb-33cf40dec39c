(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-54575c72"],{"1c59":function(t,e,r){"use strict";var n=r("6d61"),o=r("6566");n("Set",(function(t){return function(){return t(this,arguments.length?arguments[0]:void 0)}}),o)},"1e5a":function(t,e,r){"use strict";var n=r("23e7"),o=r("9961"),i=r("dad2");n({target:"Set",proto:!0,real:!0,forced:!i("symmetricDifference")},{symmetricDifference:o})},"1e70":function(t,e,r){"use strict";var n=r("23e7"),o=r("a5f7"),i=r("dad2");n({target:"Set",proto:!0,real:!0,forced:!i("difference")},{difference:o})},"384f":function(t,e,r){"use strict";var n=r("e330"),o=r("5388"),i=r("cb27"),s=i.Set,a=i.proto,c=n(a.forEach),h=n(a.keys),p=h(new s).next;t.exports=function(t,e,r){return r?o({iterator:h(t),next:p},e):c(t,e)}},"395e":function(t,e,r){"use strict";var n=r("dc19"),o=r("cb27").has,i=r("8e16"),s=r("7f65"),a=r("5388"),c=r("2a62");t.exports=function(t){var e=n(this),r=s(t);if(i(e)<r.size)return!1;var h=r.getIterator();return!1!==a(h,(function(t){if(!o(e,t))return c(h,"normal",!1)}))}},5388:function(t,e,r){"use strict";var n=r("c65b");t.exports=function(t,e,r){var o,i,s=r?t:t.iterator,a=t.next;while(!(o=n(a,s)).done)if(i=e(o.value),void 0!==i)return i}},6062:function(t,e,r){"use strict";r("1c59")},"68df":function(t,e,r){"use strict";var n=r("dc19"),o=r("8e16"),i=r("384f"),s=r("7f65");t.exports=function(t){var e=n(this),r=s(t);return!(o(e)>r.size)&&!1!==i(e,(function(t){if(!r.includes(t))return!1}),!0)}},"72c3":function(t,e,r){"use strict";var n=r("23e7"),o=r("e9bc"),i=r("dad2");n({target:"Set",proto:!0,real:!0,forced:!i("union")},{union:o})},"79a4":function(t,e,r){"use strict";var n=r("23e7"),o=r("d039"),i=r("953b"),s=r("dad2"),a=!s("intersection")||o((function(){return"3,2"!==String(Array.from(new Set([1,2,3]).intersection(new Set([3,2]))))}));n({target:"Set",proto:!0,real:!0,forced:a},{intersection:i})},"7e79":function(t,e,r){!function(e,r){t.exports=r()}(window,(function(){return function(t){var e={};function r(n){if(e[n])return e[n].exports;var o=e[n]={i:n,l:!1,exports:{}};return t[n].call(o.exports,o,o.exports,r),o.l=!0,o.exports}return r.m=t,r.c=e,r.d=function(t,e,n){r.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:n})},r.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},r.t=function(t,e){if(1&e&&(t=r(t)),8&e)return t;if(4&e&&"object"==typeof t&&t&&t.__esModule)return t;var n=Object.create(null);if(r.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var o in t)r.d(n,o,function(e){return t[e]}.bind(null,o));return n},r.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return r.d(e,"a",e),e},r.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},r.p="",r(r.s=6)}([function(t,e,r){var n=r(2);"string"==typeof n&&(n=[[t.i,n,""]]);var o={hmr:!0,transform:void 0,insertInto:void 0};r(4)(n,o),n.locals&&(t.exports=n.locals)},function(t,e,r){"use strict";var n=r(0);r.n(n).a},function(t,e,r){(t.exports=r(3)(!1)).push([t.i,'\n.vue-cropper[data-v-6dae58fd] {\n  position: relative;\n  width: 100%;\n  height: 100%;\n  box-sizing: border-box;\n  user-select: none;\n  -webkit-user-select: none;\n  -moz-user-select: none;\n  -ms-user-select: none;\n  direction: ltr;\n  touch-action: none;\n  text-align: left;\n  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQAQMAAAAlPW0iAAAAA3NCSVQICAjb4U/gAAAABlBMVEXMzMz////TjRV2AAAACXBIWXMAAArrAAAK6wGCiw1aAAAAHHRFWHRTb2Z0d2FyZQBBZG9iZSBGaXJld29ya3MgQ1M26LyyjAAAABFJREFUCJlj+M/AgBVhF/0PAH6/D/HkDxOGAAAAAElFTkSuQmCC");\n}\n.cropper-box[data-v-6dae58fd],\n.cropper-box-canvas[data-v-6dae58fd],\n.cropper-drag-box[data-v-6dae58fd],\n.cropper-crop-box[data-v-6dae58fd],\n.cropper-face[data-v-6dae58fd] {\n  position: absolute;\n  top: 0;\n  right: 0;\n  bottom: 0;\n  left: 0;\n  user-select: none;\n}\n.cropper-box-canvas img[data-v-6dae58fd] {\n  position: relative;\n  text-align: left;\n  user-select: none;\n  transform: none;\n  max-width: none;\n  max-height: none;\n}\n.cropper-box[data-v-6dae58fd] {\n  overflow: hidden;\n}\n.cropper-move[data-v-6dae58fd] {\n  cursor: move;\n}\n.cropper-crop[data-v-6dae58fd] {\n  cursor: crosshair;\n}\n.cropper-modal[data-v-6dae58fd] {\n  background: rgba(0, 0, 0, 0.5);\n}\n.cropper-crop-box[data-v-6dae58fd] {\n  /*border: 2px solid #39f;*/\n}\n.cropper-view-box[data-v-6dae58fd] {\n  display: block;\n  overflow: hidden;\n  width: 100%;\n  height: 100%;\n  outline: 1px solid #39f;\n  outline-color: rgba(51, 153, 255, 0.75);\n  user-select: none;\n}\n.cropper-view-box img[data-v-6dae58fd] {\n  user-select: none;\n  text-align: left;\n  max-width: none;\n  max-height: none;\n}\n.cropper-face[data-v-6dae58fd] {\n  top: 0;\n  left: 0;\n  background-color: #fff;\n  opacity: 0.1;\n}\n.crop-info[data-v-6dae58fd] {\n  position: absolute;\n  left: 0px;\n  min-width: 65px;\n  text-align: center;\n  color: white;\n  line-height: 20px;\n  background-color: rgba(0, 0, 0, 0.8);\n  font-size: 12px;\n}\n.crop-line[data-v-6dae58fd] {\n  position: absolute;\n  display: block;\n  width: 100%;\n  height: 100%;\n  opacity: 0.1;\n}\n.line-w[data-v-6dae58fd] {\n  top: -3px;\n  left: 0;\n  height: 5px;\n  cursor: n-resize;\n}\n.line-a[data-v-6dae58fd] {\n  top: 0;\n  left: -3px;\n  width: 5px;\n  cursor: w-resize;\n}\n.line-s[data-v-6dae58fd] {\n  bottom: -3px;\n  left: 0;\n  height: 5px;\n  cursor: s-resize;\n}\n.line-d[data-v-6dae58fd] {\n  top: 0;\n  right: -3px;\n  width: 5px;\n  cursor: e-resize;\n}\n.crop-point[data-v-6dae58fd] {\n  position: absolute;\n  width: 8px;\n  height: 8px;\n  opacity: 0.75;\n  background-color: #39f;\n  border-radius: 100%;\n}\n.point1[data-v-6dae58fd] {\n  top: -4px;\n  left: -4px;\n  cursor: nw-resize;\n}\n.point2[data-v-6dae58fd] {\n  top: -5px;\n  left: 50%;\n  margin-left: -3px;\n  cursor: n-resize;\n}\n.point3[data-v-6dae58fd] {\n  top: -4px;\n  right: -4px;\n  cursor: ne-resize;\n}\n.point4[data-v-6dae58fd] {\n  top: 50%;\n  left: -4px;\n  margin-top: -3px;\n  cursor: w-resize;\n}\n.point5[data-v-6dae58fd] {\n  top: 50%;\n  right: -4px;\n  margin-top: -3px;\n  cursor: e-resize;\n}\n.point6[data-v-6dae58fd] {\n  bottom: -5px;\n  left: -4px;\n  cursor: sw-resize;\n}\n.point7[data-v-6dae58fd] {\n  bottom: -5px;\n  left: 50%;\n  margin-left: -3px;\n  cursor: s-resize;\n}\n.point8[data-v-6dae58fd] {\n  bottom: -5px;\n  right: -4px;\n  cursor: se-resize;\n}\n@media screen and (max-width: 500px) {\n.crop-point[data-v-6dae58fd] {\n    position: absolute;\n    width: 20px;\n    height: 20px;\n    opacity: 0.45;\n    background-color: #39f;\n    border-radius: 100%;\n}\n.point1[data-v-6dae58fd] {\n    top: -10px;\n    left: -10px;\n}\n.point2[data-v-6dae58fd],\n  .point4[data-v-6dae58fd],\n  .point5[data-v-6dae58fd],\n  .point7[data-v-6dae58fd] {\n    display: none;\n}\n.point3[data-v-6dae58fd] {\n    top: -10px;\n    right: -10px;\n}\n.point4[data-v-6dae58fd] {\n    top: 0;\n    left: 0;\n}\n.point6[data-v-6dae58fd] {\n    bottom: -10px;\n    left: -10px;\n}\n.point8[data-v-6dae58fd] {\n    bottom: -10px;\n    right: -10px;\n}\n}\n',""])},function(t,e){t.exports=function(t){var e=[];return e.toString=function(){return this.map((function(e){var r=function(t,e){var r=t[1]||"",n=t[3];if(!n)return r;if(e&&"function"==typeof btoa){var o=function(t){return"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,"+btoa(unescape(encodeURIComponent(JSON.stringify(t))))+" */"}(n),i=n.sources.map((function(t){return"/*# sourceURL="+n.sourceRoot+t+" */"}));return[r].concat(i).concat([o]).join("\n")}return[r].join("\n")}(e,t);return e[2]?"@media "+e[2]+"{"+r+"}":r})).join("")},e.i=function(t,r){"string"==typeof t&&(t=[[null,t,""]]);for(var n={},o=0;o<this.length;o++){var i=this[o][0];"number"==typeof i&&(n[i]=!0)}for(o=0;o<t.length;o++){var s=t[o];"number"==typeof s[0]&&n[s[0]]||(r&&!s[2]?s[2]=r:r&&(s[2]="("+s[2]+") and ("+r+")"),e.push(s))}},e}},function(t,e,r){var n={},o=function(t){var e;return function(){return void 0===e&&(e=t.apply(this,arguments)),e}}((function(){return window&&document&&document.all&&!window.atob})),i=function(t){var e={};return function(t,r){if("function"==typeof t)return t();if(void 0===e[t]){var n=function(t,e){return e?e.querySelector(t):document.querySelector(t)}.call(this,t,r);if(window.HTMLIFrameElement&&n instanceof window.HTMLIFrameElement)try{n=n.contentDocument.head}catch(t){n=null}e[t]=n}return e[t]}}(),s=null,a=0,c=[],h=r(5);function p(t,e){for(var r=0;r<t.length;r++){var o=t[r],i=n[o.id];if(i){i.refs++;for(var s=0;s<i.parts.length;s++)i.parts[s](o.parts[s]);for(;s<o.parts.length;s++)i.parts.push(v(o.parts[s],e))}else{var a=[];for(s=0;s<o.parts.length;s++)a.push(v(o.parts[s],e));n[o.id]={id:o.id,refs:1,parts:a}}}}function u(t,e){for(var r=[],n={},o=0;o<t.length;o++){var i=t[o],s=e.base?i[0]+e.base:i[0],a={css:i[1],media:i[2],sourceMap:i[3]};n[s]?n[s].parts.push(a):r.push(n[s]={id:s,parts:[a]})}return r}function f(t,e){var r=i(t.insertInto);if(!r)throw new Error("Couldn't find a style target. This probably means that the value for the 'insertInto' parameter is invalid.");var n=c[c.length-1];if("top"===t.insertAt)n?n.nextSibling?r.insertBefore(e,n.nextSibling):r.appendChild(e):r.insertBefore(e,r.firstChild),c.push(e);else if("bottom"===t.insertAt)r.appendChild(e);else{if("object"!=typeof t.insertAt||!t.insertAt.before)throw new Error("[Style Loader]\n\n Invalid value for parameter 'insertAt' ('options.insertAt') found.\n Must be 'top', 'bottom', or Object.\n (https://github.com/webpack-contrib/style-loader#insertat)\n");var o=i(t.insertAt.before,r);r.insertBefore(e,o)}}function d(t){if(null===t.parentNode)return!1;t.parentNode.removeChild(t);var e=c.indexOf(t);e>=0&&c.splice(e,1)}function l(t){var e=document.createElement("style");if(void 0===t.attrs.type&&(t.attrs.type="text/css"),void 0===t.attrs.nonce){var n=function(){return r.nc}();n&&(t.attrs.nonce=n)}return g(e,t.attrs),f(t,e),e}function g(t,e){Object.keys(e).forEach((function(r){t.setAttribute(r,e[r])}))}function v(t,e){var r,n,o,i;if(e.transform&&t.css){if(!(i="function"==typeof e.transform?e.transform(t.css):e.transform.default(t.css)))return function(){};t.css=i}if(e.singleton){var c=a++;r=s||(s=l(e)),n=w.bind(null,r,c,!1),o=w.bind(null,r,c,!0)}else t.sourceMap&&"function"==typeof URL&&"function"==typeof URL.createObjectURL&&"function"==typeof URL.revokeObjectURL&&"function"==typeof Blob&&"function"==typeof btoa?(r=function(t){var e=document.createElement("link");return void 0===t.attrs.type&&(t.attrs.type="text/css"),t.attrs.rel="stylesheet",g(e,t.attrs),f(t,e),e}(e),n=function(t,e,r){var n=r.css,o=r.sourceMap,i=void 0===e.convertToAbsoluteUrls&&o;(e.convertToAbsoluteUrls||i)&&(n=h(n)),o&&(n+="\n/*# sourceMappingURL=data:application/json;base64,"+btoa(unescape(encodeURIComponent(JSON.stringify(o))))+" */");var s=new Blob([n],{type:"text/css"}),a=t.href;t.href=URL.createObjectURL(s),a&&URL.revokeObjectURL(a)}.bind(null,r,e),o=function(){d(r),r.href&&URL.revokeObjectURL(r.href)}):(r=l(e),n=function(t,e){var r=e.css,n=e.media;if(n&&t.setAttribute("media",n),t.styleSheet)t.styleSheet.cssText=r;else{for(;t.firstChild;)t.removeChild(t.firstChild);t.appendChild(document.createTextNode(r))}}.bind(null,r),o=function(){d(r)});return n(t),function(e){if(e){if(e.css===t.css&&e.media===t.media&&e.sourceMap===t.sourceMap)return;n(t=e)}else o()}}t.exports=function(t,e){if("undefined"!=typeof DEBUG&&DEBUG&&"object"!=typeof document)throw new Error("The style-loader cannot be used in a non-browser environment");(e=e||{}).attrs="object"==typeof e.attrs?e.attrs:{},e.singleton||"boolean"==typeof e.singleton||(e.singleton=o()),e.insertInto||(e.insertInto="head"),e.insertAt||(e.insertAt="bottom");var r=u(t,e);return p(r,e),function(t){for(var o=[],i=0;i<r.length;i++){var s=r[i];(a=n[s.id]).refs--,o.push(a)}for(t&&p(u(t,e),e),i=0;i<o.length;i++){var a;if(0===(a=o[i]).refs){for(var c=0;c<a.parts.length;c++)a.parts[c]();delete n[a.id]}}}};var m=function(){var t=[];return function(e,r){return t[e]=r,t.filter(Boolean).join("\n")}}();function w(t,e,r,n){var o=r?"":n.css;if(t.styleSheet)t.styleSheet.cssText=m(e,o);else{var i=document.createTextNode(o),s=t.childNodes;s[e]&&t.removeChild(s[e]),s.length?t.insertBefore(i,s[e]):t.appendChild(i)}}},function(t,e){t.exports=function(t){var e="undefined"!=typeof window&&window.location;if(!e)throw new Error("fixUrls requires window.location");if(!t||"string"!=typeof t)return t;var r=e.protocol+"//"+e.host,n=r+e.pathname.replace(/\/[^\/]*$/,"/");return t.replace(/url\s*\(((?:[^)(]|\((?:[^)(]+|\([^)(]*\))*\))*)\)/gi,(function(t,e){var o,i=e.trim().replace(/^"(.*)"$/,(function(t,e){return e})).replace(/^'(.*)'$/,(function(t,e){return e}));return/^(#|data:|http:\/\/|https:\/\/|file:\/\/\/|\s*$)/i.test(i)?t:(o=0===i.indexOf("//")?i:0===i.indexOf("/")?r+i:n+i.replace(/^\.\//,""),"url("+JSON.stringify(o)+")")}))}},function(t,e,r){"use strict";r.r(e);var n=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",{ref:"cropper",staticClass:"vue-cropper",on:{mouseover:t.scaleImg,mouseout:t.cancelScale}},[t.imgs?r("div",{staticClass:"cropper-box"},[r("div",{directives:[{name:"show",rawName:"v-show",value:!t.loading,expression:"!loading"}],staticClass:"cropper-box-canvas",style:{width:t.trueWidth+"px",height:t.trueHeight+"px",transform:"scale("+t.scale+","+t.scale+") translate3d("+t.x/t.scale+"px,"+t.y/t.scale+"px,0)rotateZ("+90*t.rotate+"deg)"}},[r("img",{ref:"cropperImg",attrs:{src:t.imgs,alt:"cropper-img"}})])]):t._e(),t._v(" "),r("div",{staticClass:"cropper-drag-box",class:{"cropper-move":t.move&&!t.crop,"cropper-crop":t.crop,"cropper-modal":t.cropping},on:{mousedown:t.startMove,touchstart:t.startMove}}),t._v(" "),r("div",{directives:[{name:"show",rawName:"v-show",value:t.cropping,expression:"cropping"}],staticClass:"cropper-crop-box",style:{width:t.cropW+"px",height:t.cropH+"px",transform:"translate3d("+t.cropOffsertX+"px,"+t.cropOffsertY+"px,0)"}},[r("span",{staticClass:"cropper-view-box"},[r("img",{style:{width:t.trueWidth+"px",height:t.trueHeight+"px",transform:"scale("+t.scale+","+t.scale+") translate3d("+(t.x-t.cropOffsertX)/t.scale+"px,"+(t.y-t.cropOffsertY)/t.scale+"px,0)rotateZ("+90*t.rotate+"deg)"},attrs:{src:t.imgs,alt:"cropper-img"}})]),t._v(" "),r("span",{staticClass:"cropper-face cropper-move",on:{mousedown:t.cropMove,touchstart:t.cropMove}}),t._v(" "),t.info?r("span",{staticClass:"crop-info",style:{top:t.cropInfo.top}},[t._v(t._s(this.cropInfo.width)+" × "+t._s(this.cropInfo.height))]):t._e(),t._v(" "),t.fixedBox?t._e():r("span",[r("span",{staticClass:"crop-line line-w",on:{mousedown:function(e){t.changeCropSize(e,!1,!0,0,1)},touchstart:function(e){t.changeCropSize(e,!1,!0,0,1)}}}),t._v(" "),r("span",{staticClass:"crop-line line-a",on:{mousedown:function(e){t.changeCropSize(e,!0,!1,1,0)},touchstart:function(e){t.changeCropSize(e,!0,!1,1,0)}}}),t._v(" "),r("span",{staticClass:"crop-line line-s",on:{mousedown:function(e){t.changeCropSize(e,!1,!0,0,2)},touchstart:function(e){t.changeCropSize(e,!1,!0,0,2)}}}),t._v(" "),r("span",{staticClass:"crop-line line-d",on:{mousedown:function(e){t.changeCropSize(e,!0,!1,2,0)},touchstart:function(e){t.changeCropSize(e,!0,!1,2,0)}}}),t._v(" "),r("span",{staticClass:"crop-point point1",on:{mousedown:function(e){t.changeCropSize(e,!0,!0,1,1)},touchstart:function(e){t.changeCropSize(e,!0,!0,1,1)}}}),t._v(" "),r("span",{staticClass:"crop-point point2",on:{mousedown:function(e){t.changeCropSize(e,!1,!0,0,1)},touchstart:function(e){t.changeCropSize(e,!1,!0,0,1)}}}),t._v(" "),r("span",{staticClass:"crop-point point3",on:{mousedown:function(e){t.changeCropSize(e,!0,!0,2,1)},touchstart:function(e){t.changeCropSize(e,!0,!0,2,1)}}}),t._v(" "),r("span",{staticClass:"crop-point point4",on:{mousedown:function(e){t.changeCropSize(e,!0,!1,1,0)},touchstart:function(e){t.changeCropSize(e,!0,!1,1,0)}}}),t._v(" "),r("span",{staticClass:"crop-point point5",on:{mousedown:function(e){t.changeCropSize(e,!0,!1,2,0)},touchstart:function(e){t.changeCropSize(e,!0,!1,2,0)}}}),t._v(" "),r("span",{staticClass:"crop-point point6",on:{mousedown:function(e){t.changeCropSize(e,!0,!0,1,2)},touchstart:function(e){t.changeCropSize(e,!0,!0,1,2)}}}),t._v(" "),r("span",{staticClass:"crop-point point7",on:{mousedown:function(e){t.changeCropSize(e,!1,!0,0,2)},touchstart:function(e){t.changeCropSize(e,!1,!0,0,2)}}}),t._v(" "),r("span",{staticClass:"crop-point point8",on:{mousedown:function(e){t.changeCropSize(e,!0,!0,2,2)},touchstart:function(e){t.changeCropSize(e,!0,!0,2,2)}}})])])])};n._withStripped=!0;var o={getData:function(t){return new Promise((function(e,r){var n={};(function(t){var e=null;return new Promise((function(r,n){if(t.src)if(/^data\:/i.test(t.src))e=function(t){t=t.replace(/^data\:([^\;]+)\;base64,/gim,"");for(var e=atob(t),r=e.length,n=new ArrayBuffer(r),o=new Uint8Array(n),i=0;i<r;i++)o[i]=e.charCodeAt(i);return n}(t.src),r(e);else if(/^blob\:/i.test(t.src)){var o=new FileReader;o.onload=function(t){e=t.target.result,r(e)},function(t,e){var r=new XMLHttpRequest;r.open("GET",t,!0),r.responseType="blob",r.onload=function(t){200!=this.status&&0!==this.status||e(this.response)},r.send()}(t.src,(function(t){o.readAsArrayBuffer(t)}))}else{var i=new XMLHttpRequest;i.onload=function(){if(200!=this.status&&0!==this.status)throw"Could not load image";e=i.response,r(e),i=null},i.open("GET",t.src,!0),i.responseType="arraybuffer",i.send(null)}else n("img error")}))})(t).then((function(t){n.arrayBuffer=t,n.orientation=function(t){var e,r,n,o,i,s,a,c,h,p=new DataView(t),u=p.byteLength;if(255===p.getUint8(0)&&216===p.getUint8(1))for(c=2;c<u;){if(255===p.getUint8(c)&&225===p.getUint8(c+1)){s=c;break}c++}if(s&&(r=s+10,"Exif"===function(t,e,r){var n,o="";for(n=e,r+=e;n<r;n++)o+=String.fromCharCode(t.getUint8(n));return o}(p,s+4,4)&&(i=p.getUint16(r),((o=18761===i)||19789===i)&&42===p.getUint16(r+2,o)&&(n=p.getUint32(r+4,o))>=8&&(a=r+n))),a)for(u=p.getUint16(a,o),h=0;h<u;h++)if(c=a+12*h+2,274===p.getUint16(c,o)){c+=8,e=p.getUint16(c,o);break}return e}(t),e(n)})).catch((function(t){r(t)}))}))}},i=o,s={data:function(){return{w:0,h:0,scale:1,x:0,y:0,loading:!0,trueWidth:0,trueHeight:0,move:!0,moveX:0,moveY:0,crop:!1,cropping:!1,cropW:0,cropH:0,cropOldW:0,cropOldH:0,canChangeX:!1,canChangeY:!1,changeCropTypeX:1,changeCropTypeY:1,cropX:0,cropY:0,cropChangeX:0,cropChangeY:0,cropOffsertX:0,cropOffsertY:0,support:"",touches:[],touchNow:!1,rotate:0,isIos:!1,orientation:0,imgs:"",coe:.2,scaling:!1,scalingSet:"",coeStatus:"",isCanShow:!0}},props:{img:{type:[String,Blob,null,File],default:""},outputSize:{type:Number,default:1},outputType:{type:String,default:"jpeg"},info:{type:Boolean,default:!0},canScale:{type:Boolean,default:!0},autoCrop:{type:Boolean,default:!1},autoCropWidth:{type:[Number,String],default:0},autoCropHeight:{type:[Number,String],default:0},fixed:{type:Boolean,default:!1},fixedNumber:{type:Array,default:function(){return[1,1]}},fixedBox:{type:Boolean,default:!1},full:{type:Boolean,default:!1},canMove:{type:Boolean,default:!0},canMoveBox:{type:Boolean,default:!0},original:{type:Boolean,default:!1},centerBox:{type:Boolean,default:!1},high:{type:Boolean,default:!0},infoTrue:{type:Boolean,default:!1},maxImgSize:{type:[Number,String],default:2e3},enlarge:{type:[Number,String],default:1},preW:{type:[Number,String],default:0},mode:{type:String,default:"contain"},limitMinSize:{type:[Number,Array,String],default:function(){return 10}}},computed:{cropInfo:function(){var t={};if(t.top=this.cropOffsertY>21?"-21px":"0px",t.width=this.cropW>0?this.cropW:0,t.height=this.cropH>0?this.cropH:0,this.infoTrue){var e=1;this.high&&!this.full&&(e=window.devicePixelRatio),1!==this.enlarge&!this.full&&(e=Math.abs(Number(this.enlarge))),t.width=t.width*e,t.height=t.height*e,this.full&&(t.width=t.width/this.scale,t.height=t.height/this.scale)}return t.width=t.width.toFixed(0),t.height=t.height.toFixed(0),t},isIE:function(){navigator.userAgent;var t=!!window.ActiveXObject||"ActiveXObject"in window;return t},passive:function(){return this.isIE?null:{passive:!1}}},watch:{img:function(){this.checkedImg()},imgs:function(t){""!==t&&this.reload()},cropW:function(){this.showPreview()},cropH:function(){this.showPreview()},cropOffsertX:function(){this.showPreview()},cropOffsertY:function(){this.showPreview()},scale:function(t,e){this.showPreview()},x:function(){this.showPreview()},y:function(){this.showPreview()},autoCrop:function(t){t&&this.goAutoCrop()},autoCropWidth:function(){this.autoCrop&&this.goAutoCrop()},autoCropHeight:function(){this.autoCrop&&this.goAutoCrop()},mode:function(){this.checkedImg()},rotate:function(){this.showPreview(),(this.autoCrop||this.cropW>0||this.cropH>0)&&this.goAutoCrop(this.cropW,this.cropH)}},methods:{getVersion:function(t){for(var e=navigator.userAgent.split(" "),r="",n=new RegExp(t,"i"),o=0;o<e.length;o++)n.test(e[o])&&(r=e[o]);return r?r.split("/")[1].split("."):["0","0","0"]},checkOrientationImage:function(t,e,r,n){var o=this;if(this.getVersion("chrome")[0]>=81)e=-1;else if(this.getVersion("safari")[0]>=605){var i=this.getVersion("version");i[0]>13&&i[1]>1&&(e=-1)}else{var s=navigator.userAgent.toLowerCase().match(/cpu iphone os (.*?) like mac os/);if(s){var a=s[1];((a=a.split("_"))[0]>13||a[0]>=13&&a[1]>=4)&&(e=-1)}}var c=document.createElement("canvas"),h=c.getContext("2d");switch(h.save(),e){case 2:c.width=r,c.height=n,h.translate(r,0),h.scale(-1,1);break;case 3:c.width=r,c.height=n,h.translate(r/2,n/2),h.rotate(180*Math.PI/180),h.translate(-r/2,-n/2);break;case 4:c.width=r,c.height=n,h.translate(0,n),h.scale(1,-1);break;case 5:c.height=r,c.width=n,h.rotate(.5*Math.PI),h.scale(1,-1);break;case 6:c.width=n,c.height=r,h.translate(n/2,r/2),h.rotate(90*Math.PI/180),h.translate(-r/2,-n/2);break;case 7:c.height=r,c.width=n,h.rotate(.5*Math.PI),h.translate(r,-n),h.scale(-1,1);break;case 8:c.height=r,c.width=n,h.translate(n/2,r/2),h.rotate(-90*Math.PI/180),h.translate(-r/2,-n/2);break;default:c.width=r,c.height=n}h.drawImage(t,0,0,r,n),h.restore(),c.toBlob((function(t){var e=URL.createObjectURL(t);URL.revokeObjectURL(o.imgs),o.imgs=e}),"image/"+this.outputType,1)},checkedImg:function(){var t=this;if(null===this.img||""===this.img)return this.imgs="",void this.clearCrop();this.loading=!0,this.scale=1,this.rotate=0,this.clearCrop();var e=new Image;if(e.onload=function(){if(""===t.img)return t.$emit("imgLoad","error"),t.$emit("img-load","error"),!1;var r=e.width,n=e.height;i.getData(e).then((function(o){t.orientation=o.orientation||1;var i=t.maxImgSize;!t.orientation&&r<i&n<i?t.imgs=t.img:(r>i&&(n=n/r*i,r=i),n>i&&(r=r/n*i,n=i),t.checkOrientationImage(e,t.orientation,r,n))}))},e.onerror=function(){t.$emit("imgLoad","error"),t.$emit("img-load","error")},"data"!==this.img.substr(0,4)&&(e.crossOrigin=""),this.isIE){var r=new XMLHttpRequest;r.onload=function(){var t=URL.createObjectURL(this.response);e.src=t},r.open("GET",this.img,!0),r.responseType="blob",r.send()}else e.src=this.img},startMove:function(t){if(t.preventDefault(),this.move&&!this.crop){if(!this.canMove)return!1;this.moveX=(t.clientX?t.clientX:t.touches[0].clientX)-this.x,this.moveY=(t.clientY?t.clientY:t.touches[0].clientY)-this.y,t.touches?(window.addEventListener("touchmove",this.moveImg),window.addEventListener("touchend",this.leaveImg),2==t.touches.length&&(this.touches=t.touches,window.addEventListener("touchmove",this.touchScale),window.addEventListener("touchend",this.cancelTouchScale))):(window.addEventListener("mousemove",this.moveImg),window.addEventListener("mouseup",this.leaveImg)),this.$emit("imgMoving",{moving:!0,axis:this.getImgAxis()}),this.$emit("img-moving",{moving:!0,axis:this.getImgAxis()})}else this.cropping=!0,window.addEventListener("mousemove",this.createCrop),window.addEventListener("mouseup",this.endCrop),window.addEventListener("touchmove",this.createCrop),window.addEventListener("touchend",this.endCrop),this.cropOffsertX=t.offsetX?t.offsetX:t.touches[0].pageX-this.$refs.cropper.offsetLeft,this.cropOffsertY=t.offsetY?t.offsetY:t.touches[0].pageY-this.$refs.cropper.offsetTop,this.cropX=t.clientX?t.clientX:t.touches[0].clientX,this.cropY=t.clientY?t.clientY:t.touches[0].clientY,this.cropChangeX=this.cropOffsertX,this.cropChangeY=this.cropOffsertY,this.cropW=0,this.cropH=0},touchScale:function(t){var e=this;t.preventDefault();var r=this.scale,n=this.touches[0].clientX,o=this.touches[0].clientY,i=t.touches[0].clientX,s=t.touches[0].clientY,a=this.touches[1].clientX,c=this.touches[1].clientY,h=t.touches[1].clientX,p=t.touches[1].clientY,u=Math.sqrt(Math.pow(n-a,2)+Math.pow(o-c,2)),f=Math.sqrt(Math.pow(i-h,2)+Math.pow(s-p,2))-u,d=1,l=(d=(d=d/this.trueWidth>d/this.trueHeight?d/this.trueHeight:d/this.trueWidth)>.1?.1:d)*f;if(!this.touchNow){if(this.touchNow=!0,f>0?r+=Math.abs(l):f<0&&r>Math.abs(l)&&(r-=Math.abs(l)),this.touches=t.touches,setTimeout((function(){e.touchNow=!1}),8),!this.checkoutImgAxis(this.x,this.y,r))return!1;this.scale=r}},cancelTouchScale:function(t){window.removeEventListener("touchmove",this.touchScale)},moveImg:function(t){var e=this;if(t.preventDefault(),t.touches&&2===t.touches.length)return this.touches=t.touches,window.addEventListener("touchmove",this.touchScale),window.addEventListener("touchend",this.cancelTouchScale),window.removeEventListener("touchmove",this.moveImg),!1;var r,n,o=t.clientX?t.clientX:t.touches[0].clientX,i=t.clientY?t.clientY:t.touches[0].clientY;r=o-this.moveX,n=i-this.moveY,this.$nextTick((function(){if(e.centerBox){var t,o,i,s,a=e.getImgAxis(r,n,e.scale),c=e.getCropAxis(),h=e.trueHeight*e.scale,p=e.trueWidth*e.scale;switch(e.rotate){case 1:case-1:case 3:case-3:t=e.cropOffsertX-e.trueWidth*(1-e.scale)/2+(h-p)/2,o=e.cropOffsertY-e.trueHeight*(1-e.scale)/2+(p-h)/2,i=t-h+e.cropW,s=o-p+e.cropH;break;default:t=e.cropOffsertX-e.trueWidth*(1-e.scale)/2,o=e.cropOffsertY-e.trueHeight*(1-e.scale)/2,i=t-p+e.cropW,s=o-h+e.cropH}a.x1>=c.x1&&(r=t),a.y1>=c.y1&&(n=o),a.x2<=c.x2&&(r=i),a.y2<=c.y2&&(n=s)}e.x=r,e.y=n,e.$emit("imgMoving",{moving:!0,axis:e.getImgAxis()}),e.$emit("img-moving",{moving:!0,axis:e.getImgAxis()})}))},leaveImg:function(t){window.removeEventListener("mousemove",this.moveImg),window.removeEventListener("touchmove",this.moveImg),window.removeEventListener("mouseup",this.leaveImg),window.removeEventListener("touchend",this.leaveImg),this.$emit("imgMoving",{moving:!1,axis:this.getImgAxis()}),this.$emit("img-moving",{moving:!1,axis:this.getImgAxis()})},scaleImg:function(){this.canScale&&window.addEventListener(this.support,this.changeSize,this.passive)},cancelScale:function(){this.canScale&&window.removeEventListener(this.support,this.changeSize)},changeSize:function(t){var e=this;t.preventDefault();var r=this.scale,n=t.deltaY||t.wheelDelta;n=navigator.userAgent.indexOf("Firefox")>0?30*n:n,this.isIE&&(n=-n);var o=this.coe,i=(o=o/this.trueWidth>o/this.trueHeight?o/this.trueHeight:o/this.trueWidth)*n;i<0?r+=Math.abs(i):r>Math.abs(i)&&(r-=Math.abs(i));var s=i<0?"add":"reduce";if(s!==this.coeStatus&&(this.coeStatus=s,this.coe=.2),this.scaling||(this.scalingSet=setTimeout((function(){e.scaling=!1,e.coe=e.coe+=.01}),50)),this.scaling=!0,!this.checkoutImgAxis(this.x,this.y,r))return!1;this.scale=r},changeScale:function(t){var e=this.scale;t=t||1;var r=20;if((t*=r=r/this.trueWidth>r/this.trueHeight?r/this.trueHeight:r/this.trueWidth)>0?e+=Math.abs(t):e>Math.abs(t)&&(e-=Math.abs(t)),!this.checkoutImgAxis(this.x,this.y,e))return!1;this.scale=e},createCrop:function(t){var e=this;t.preventDefault();var r=t.clientX?t.clientX:t.touches?t.touches[0].clientX:0,n=t.clientY?t.clientY:t.touches?t.touches[0].clientY:0;this.$nextTick((function(){var t=r-e.cropX,o=n-e.cropY;if(t>0?(e.cropW=t+e.cropChangeX>e.w?e.w-e.cropChangeX:t,e.cropOffsertX=e.cropChangeX):(e.cropW=e.w-e.cropChangeX+Math.abs(t)>e.w?e.cropChangeX:Math.abs(t),e.cropOffsertX=e.cropChangeX+t>0?e.cropChangeX+t:0),e.fixed){var i=e.cropW/e.fixedNumber[0]*e.fixedNumber[1];i+e.cropOffsertY>e.h?(e.cropH=e.h-e.cropOffsertY,e.cropW=e.cropH/e.fixedNumber[1]*e.fixedNumber[0],e.cropOffsertX=t>0?e.cropChangeX:e.cropChangeX-e.cropW):e.cropH=i,e.cropOffsertY=e.cropOffsertY}else o>0?(e.cropH=o+e.cropChangeY>e.h?e.h-e.cropChangeY:o,e.cropOffsertY=e.cropChangeY):(e.cropH=e.h-e.cropChangeY+Math.abs(o)>e.h?e.cropChangeY:Math.abs(o),e.cropOffsertY=e.cropChangeY+o>0?e.cropChangeY+o:0)}))},changeCropSize:function(t,e,r,n,o){t.preventDefault(),window.addEventListener("mousemove",this.changeCropNow),window.addEventListener("mouseup",this.changeCropEnd),window.addEventListener("touchmove",this.changeCropNow),window.addEventListener("touchend",this.changeCropEnd),this.canChangeX=e,this.canChangeY=r,this.changeCropTypeX=n,this.changeCropTypeY=o,this.cropX=t.clientX?t.clientX:t.touches[0].clientX,this.cropY=t.clientY?t.clientY:t.touches[0].clientY,this.cropOldW=this.cropW,this.cropOldH=this.cropH,this.cropChangeX=this.cropOffsertX,this.cropChangeY=this.cropOffsertY,this.fixed&&this.canChangeX&&this.canChangeY&&(this.canChangeY=0),this.$emit("change-crop-size",{width:this.cropW,height:this.cropH})},changeCropNow:function(t){var e=this;t.preventDefault();var r=t.clientX?t.clientX:t.touches?t.touches[0].clientX:0,n=t.clientY?t.clientY:t.touches?t.touches[0].clientY:0,o=this.w,i=this.h,s=0,a=0;if(this.centerBox){var c=this.getImgAxis(),h=c.x2,p=c.y2;s=c.x1>0?c.x1:0,a=c.y1>0?c.y1:0,o>h&&(o=h),i>p&&(i=p)}this.$nextTick((function(){var t=r-e.cropX,c=n-e.cropY;if(e.canChangeX&&(1===e.changeCropTypeX?e.cropOldW-t>0?(e.cropW=o-e.cropChangeX-t<=o-s?e.cropOldW-t:e.cropOldW+e.cropChangeX-s,e.cropOffsertX=o-e.cropChangeX-t<=o-s?e.cropChangeX+t:s):(e.cropW=Math.abs(t)+e.cropChangeX<=o?Math.abs(t)-e.cropOldW:o-e.cropOldW-e.cropChangeX,e.cropOffsertX=e.cropChangeX+e.cropOldW):2===e.changeCropTypeX&&(e.cropOldW+t>0?(e.cropW=e.cropOldW+t+e.cropOffsertX<=o?e.cropOldW+t:o-e.cropOffsertX,e.cropOffsertX=e.cropChangeX):(e.cropW=o-e.cropChangeX+Math.abs(t+e.cropOldW)<=o-s?Math.abs(t+e.cropOldW):e.cropChangeX-s,e.cropOffsertX=o-e.cropChangeX+Math.abs(t+e.cropOldW)<=o-s?e.cropChangeX-Math.abs(t+e.cropOldW):s))),e.canChangeY&&(1===e.changeCropTypeY?e.cropOldH-c>0?(e.cropH=i-e.cropChangeY-c<=i-a?e.cropOldH-c:e.cropOldH+e.cropChangeY-a,e.cropOffsertY=i-e.cropChangeY-c<=i-a?e.cropChangeY+c:a):(e.cropH=Math.abs(c)+e.cropChangeY<=i?Math.abs(c)-e.cropOldH:i-e.cropOldH-e.cropChangeY,e.cropOffsertY=e.cropChangeY+e.cropOldH):2===e.changeCropTypeY&&(e.cropOldH+c>0?(e.cropH=e.cropOldH+c+e.cropOffsertY<=i?e.cropOldH+c:i-e.cropOffsertY,e.cropOffsertY=e.cropChangeY):(e.cropH=i-e.cropChangeY+Math.abs(c+e.cropOldH)<=i-a?Math.abs(c+e.cropOldH):e.cropChangeY-a,e.cropOffsertY=i-e.cropChangeY+Math.abs(c+e.cropOldH)<=i-a?e.cropChangeY-Math.abs(c+e.cropOldH):a))),e.canChangeX&&e.fixed){var h=e.cropW/e.fixedNumber[0]*e.fixedNumber[1];h+e.cropOffsertY>i?(e.cropH=i-e.cropOffsertY,e.cropW=e.cropH/e.fixedNumber[1]*e.fixedNumber[0]):e.cropH=h}if(e.canChangeY&&e.fixed){var p=e.cropH/e.fixedNumber[1]*e.fixedNumber[0];p+e.cropOffsertX>o?(e.cropW=o-e.cropOffsertX,e.cropH=e.cropW/e.fixedNumber[0]*e.fixedNumber[1]):e.cropW=p}}))},checkCropLimitSize:function(){this.cropW,this.cropH;var t=this.limitMinSize,e=new Array;return e=Array.isArray[t]?t:[t,t],[parseFloat(e[0]),parseFloat(e[1])]},changeCropEnd:function(t){window.removeEventListener("mousemove",this.changeCropNow),window.removeEventListener("mouseup",this.changeCropEnd),window.removeEventListener("touchmove",this.changeCropNow),window.removeEventListener("touchend",this.changeCropEnd)},endCrop:function(){0===this.cropW&&0===this.cropH&&(this.cropping=!1),window.removeEventListener("mousemove",this.createCrop),window.removeEventListener("mouseup",this.endCrop),window.removeEventListener("touchmove",this.createCrop),window.removeEventListener("touchend",this.endCrop)},startCrop:function(){this.crop=!0},stopCrop:function(){this.crop=!1},clearCrop:function(){this.cropping=!1,this.cropW=0,this.cropH=0},cropMove:function(t){if(t.preventDefault(),!this.canMoveBox)return this.crop=!1,this.startMove(t),!1;if(t.touches&&2===t.touches.length)return this.crop=!1,this.startMove(t),this.leaveCrop(),!1;window.addEventListener("mousemove",this.moveCrop),window.addEventListener("mouseup",this.leaveCrop),window.addEventListener("touchmove",this.moveCrop),window.addEventListener("touchend",this.leaveCrop);var e,r,n=t.clientX?t.clientX:t.touches[0].clientX,o=t.clientY?t.clientY:t.touches[0].clientY;e=n-this.cropOffsertX,r=o-this.cropOffsertY,this.cropX=e,this.cropY=r,this.$emit("cropMoving",{moving:!0,axis:this.getCropAxis()}),this.$emit("crop-moving",{moving:!0,axis:this.getCropAxis()})},moveCrop:function(t,e){var r=this,n=0,o=0;t&&(t.preventDefault(),n=t.clientX?t.clientX:t.touches[0].clientX,o=t.clientY?t.clientY:t.touches[0].clientY),this.$nextTick((function(){var t,i,s=n-r.cropX,a=o-r.cropY;if(e&&(s=r.cropOffsertX,a=r.cropOffsertY),t=s<=0?0:s+r.cropW>r.w?r.w-r.cropW:s,i=a<=0?0:a+r.cropH>r.h?r.h-r.cropH:a,r.centerBox){var c=r.getImgAxis();t<=c.x1&&(t=c.x1),t+r.cropW>c.x2&&(t=c.x2-r.cropW),i<=c.y1&&(i=c.y1),i+r.cropH>c.y2&&(i=c.y2-r.cropH)}r.cropOffsertX=t,r.cropOffsertY=i,r.$emit("cropMoving",{moving:!0,axis:r.getCropAxis()}),r.$emit("crop-moving",{moving:!0,axis:r.getCropAxis()})}))},getImgAxis:function(t,e,r){t=t||this.x,e=e||this.y,r=r||this.scale;var n={x1:0,x2:0,y1:0,y2:0},o=this.trueWidth*r,i=this.trueHeight*r;switch(this.rotate){case 0:n.x1=t+this.trueWidth*(1-r)/2,n.x2=n.x1+this.trueWidth*r,n.y1=e+this.trueHeight*(1-r)/2,n.y2=n.y1+this.trueHeight*r;break;case 1:case-1:case 3:case-3:n.x1=t+this.trueWidth*(1-r)/2+(o-i)/2,n.x2=n.x1+this.trueHeight*r,n.y1=e+this.trueHeight*(1-r)/2+(i-o)/2,n.y2=n.y1+this.trueWidth*r;break;default:n.x1=t+this.trueWidth*(1-r)/2,n.x2=n.x1+this.trueWidth*r,n.y1=e+this.trueHeight*(1-r)/2,n.y2=n.y1+this.trueHeight*r}return n},getCropAxis:function(){var t={x1:0,x2:0,y1:0,y2:0};return t.x1=this.cropOffsertX,t.x2=t.x1+this.cropW,t.y1=this.cropOffsertY,t.y2=t.y1+this.cropH,t},leaveCrop:function(t){window.removeEventListener("mousemove",this.moveCrop),window.removeEventListener("mouseup",this.leaveCrop),window.removeEventListener("touchmove",this.moveCrop),window.removeEventListener("touchend",this.leaveCrop),this.$emit("cropMoving",{moving:!1,axis:this.getCropAxis()}),this.$emit("crop-moving",{moving:!1,axis:this.getCropAxis()})},getCropChecked:function(t){var e=this,r=document.createElement("canvas"),n=new Image,o=this.rotate,i=this.trueWidth,s=this.trueHeight,a=this.cropOffsertX,c=this.cropOffsertY;function h(t,e){r.width=Math.round(t),r.height=Math.round(e)}n.onload=function(){if(0!==e.cropW){var p=r.getContext("2d"),u=1;e.high&!e.full&&(u=window.devicePixelRatio),1!==e.enlarge&!e.full&&(u=Math.abs(Number(e.enlarge)));var f=e.cropW*u,d=e.cropH*u,l=i*e.scale*u,g=s*e.scale*u,v=(e.x-a+e.trueWidth*(1-e.scale)/2)*u,m=(e.y-c+e.trueHeight*(1-e.scale)/2)*u;switch(h(f,d),p.save(),o){case 0:e.full?(h(f/e.scale,d/e.scale),p.drawImage(n,v/e.scale,m/e.scale,l/e.scale,g/e.scale)):p.drawImage(n,v,m,l,g);break;case 1:case-3:e.full?(h(f/e.scale,d/e.scale),v=v/e.scale+(l/e.scale-g/e.scale)/2,m=m/e.scale+(g/e.scale-l/e.scale)/2,p.rotate(90*o*Math.PI/180),p.drawImage(n,m,-v-g/e.scale,l/e.scale,g/e.scale)):(v+=(l-g)/2,m+=(g-l)/2,p.rotate(90*o*Math.PI/180),p.drawImage(n,m,-v-g,l,g));break;case 2:case-2:e.full?(h(f/e.scale,d/e.scale),p.rotate(90*o*Math.PI/180),v/=e.scale,m/=e.scale,p.drawImage(n,-v-l/e.scale,-m-g/e.scale,l/e.scale,g/e.scale)):(p.rotate(90*o*Math.PI/180),p.drawImage(n,-v-l,-m-g,l,g));break;case 3:case-1:e.full?(h(f/e.scale,d/e.scale),v=v/e.scale+(l/e.scale-g/e.scale)/2,m=m/e.scale+(g/e.scale-l/e.scale)/2,p.rotate(90*o*Math.PI/180),p.drawImage(n,-m-l/e.scale,v,l/e.scale,g/e.scale)):(v+=(l-g)/2,m+=(g-l)/2,p.rotate(90*o*Math.PI/180),p.drawImage(n,-m-l,v,l,g));break;default:e.full?(h(f/e.scale,d/e.scale),p.drawImage(n,v/e.scale,m/e.scale,l/e.scale,g/e.scale)):p.drawImage(n,v,m,l,g)}p.restore()}else{var w=i*e.scale,x=s*e.scale,b=r.getContext("2d");switch(b.save(),o){case 0:h(w,x),b.drawImage(n,0,0,w,x);break;case 1:case-3:h(x,w),b.rotate(90*o*Math.PI/180),b.drawImage(n,0,-x,w,x);break;case 2:case-2:h(w,x),b.rotate(90*o*Math.PI/180),b.drawImage(n,-w,-x,w,x);break;case 3:case-1:h(x,w),b.rotate(90*o*Math.PI/180),b.drawImage(n,-w,0,w,x);break;default:h(w,x),b.drawImage(n,0,0,w,x)}b.restore()}t(r)},"data"!==this.img.substr(0,4)&&(n.crossOrigin="Anonymous"),n.src=this.imgs},getCropData:function(t){var e=this;this.getCropChecked((function(r){t(r.toDataURL("image/"+e.outputType,e.outputSize))}))},getCropBlob:function(t){var e=this;this.getCropChecked((function(r){r.toBlob((function(e){return t(e)}),"image/"+e.outputType,e.outputSize)}))},showPreview:function(){var t=this;if(!this.isCanShow)return!1;this.isCanShow=!1,setTimeout((function(){t.isCanShow=!0}),16);var e=this.cropW,r=this.cropH,n=this.scale,o={};o.div={width:"".concat(e,"px"),height:"".concat(r,"px")};var i=(this.x-this.cropOffsertX)/n,s=(this.y-this.cropOffsertY)/n;o.w=e,o.h=r,o.url=this.imgs,o.img={width:"".concat(this.trueWidth,"px"),height:"".concat(this.trueHeight,"px"),transform:"scale(".concat(n,")translate3d(").concat(i,"px, ").concat(s,"px, ").concat(0,"px)rotateZ(").concat(90*this.rotate,"deg)")},o.html='\n      <div class="show-preview" style="width: '.concat(o.w,"px; height: ").concat(o.h,'px,; overflow: hidden">\n        <div style="width: ').concat(e,"px; height: ").concat(r,'px">\n          <img src=').concat(o.url,' style="width: ').concat(this.trueWidth,"px; height: ").concat(this.trueHeight,"px; transform:\n          scale(").concat(n,")translate3d(").concat(i,"px, ").concat(s,"px, ").concat(0,"px)rotateZ(").concat(90*this.rotate,'deg)">\n        </div>\n      </div>'),this.$emit("realTime",o),this.$emit("real-time",o)},reload:function(){var t=this,e=new Image;e.onload=function(){t.w=parseFloat(window.getComputedStyle(t.$refs.cropper).width),t.h=parseFloat(window.getComputedStyle(t.$refs.cropper).height),t.trueWidth=e.width,t.trueHeight=e.height,t.original?t.scale=1:t.scale=t.checkedMode(),t.$nextTick((function(){t.x=-(t.trueWidth-t.trueWidth*t.scale)/2+(t.w-t.trueWidth*t.scale)/2,t.y=-(t.trueHeight-t.trueHeight*t.scale)/2+(t.h-t.trueHeight*t.scale)/2,t.loading=!1,t.autoCrop&&t.goAutoCrop(),t.$emit("img-load","success"),t.$emit("imgLoad","success"),setTimeout((function(){t.showPreview()}),20)}))},e.onerror=function(){t.$emit("imgLoad","error"),t.$emit("img-load","error")},e.src=this.imgs},checkedMode:function(){var t=1,e=(this.trueWidth,this.trueHeight),r=this.mode.split(" ");switch(r[0]){case"contain":this.trueWidth>this.w&&(t=this.w/this.trueWidth),this.trueHeight*t>this.h&&(t=this.h/this.trueHeight);break;case"cover":(e*=t=this.w/this.trueWidth)<this.h&&(t=(e=this.h)/this.trueHeight);break;default:try{var n=r[0];if(-1!==n.search("px")&&(n=n.replace("px",""),t=parseFloat(n)/this.trueWidth),-1!==n.search("%")&&(n=n.replace("%",""),t=parseFloat(n)/100*this.w/this.trueWidth),2===r.length&&"auto"===n){var o=r[1];-1!==o.search("px")&&(o=o.replace("px",""),t=(e=parseFloat(o))/this.trueHeight),-1!==o.search("%")&&(o=o.replace("%",""),t=(e=parseFloat(o)/100*this.h)/this.trueHeight)}}catch(e){t=1}}return t},goAutoCrop:function(t,e){if(""!==this.imgs&&null!==this.imgs){this.clearCrop(),this.cropping=!0;var r=this.w,n=this.h;if(this.centerBox){var o=this.trueWidth*this.scale,i=this.trueHeight*this.scale;r=o<r?o:r,n=i<n?i:n}var s=t||parseFloat(this.autoCropWidth),a=e||parseFloat(this.autoCropHeight);0!==s&&0!==a||(s=.8*r,a=.8*n),s=s>r?r:s,a=a>n?n:a,this.fixed&&(a=s/this.fixedNumber[0]*this.fixedNumber[1]),a>this.h&&(s=(a=this.h)/this.fixedNumber[1]*this.fixedNumber[0]),this.changeCrop(s,a)}},changeCrop:function(t,e){var r=this;if(this.centerBox){var n=this.getImgAxis();t>n.x2-n.x1&&(e=(t=n.x2-n.x1)/this.fixedNumber[0]*this.fixedNumber[1]),e>n.y2-n.y1&&(t=(e=n.y2-n.y1)/this.fixedNumber[1]*this.fixedNumber[0])}this.cropW=t,this.cropH=e,this.checkCropLimitSize(),this.$nextTick((function(){r.cropOffsertX=(r.w-r.cropW)/2,r.cropOffsertY=(r.h-r.cropH)/2,r.centerBox&&r.moveCrop(null,!0)}))},refresh:function(){var t=this;this.img,this.imgs="",this.scale=1,this.crop=!1,this.rotate=0,this.w=0,this.h=0,this.trueWidth=0,this.trueHeight=0,this.clearCrop(),this.$nextTick((function(){t.checkedImg()}))},rotateLeft:function(){this.rotate=this.rotate<=-3?0:this.rotate-1},rotateRight:function(){this.rotate=this.rotate>=3?0:this.rotate+1},rotateClear:function(){this.rotate=0},checkoutImgAxis:function(t,e,r){t=t||this.x,e=e||this.y,r=r||this.scale;var n=!0;if(this.centerBox){var o=this.getImgAxis(t,e,r),i=this.getCropAxis();o.x1>=i.x1&&(n=!1),o.x2<=i.x2&&(n=!1),o.y1>=i.y1&&(n=!1),o.y2<=i.y2&&(n=!1)}return n}},mounted:function(){this.support="onwheel"in document.createElement("div")?"wheel":void 0!==document.onmousewheel?"mousewheel":"DOMMouseScroll";var t=this,e=navigator.userAgent;this.isIOS=!!e.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/),HTMLCanvasElement.prototype.toBlob||Object.defineProperty(HTMLCanvasElement.prototype,"toBlob",{value:function(e,r,n){for(var o=atob(this.toDataURL(r,n).split(",")[1]),i=o.length,s=new Uint8Array(i),a=0;a<i;a++)s[a]=o.charCodeAt(a);e(new Blob([s],{type:t.type||"image/png"}))}}),this.showPreview(),this.checkedImg()},destroyed:function(){window.removeEventListener("mousemove",this.moveCrop),window.removeEventListener("mouseup",this.leaveCrop),window.removeEventListener("touchmove",this.moveCrop),window.removeEventListener("touchend",this.leaveCrop)}};r(1);var a=function(t,e,r,n,o,i,s,a){var c,h="function"==typeof t?t.options:t;if(e&&(h.render=e,h.staticRenderFns=r,h._compiled=!0),n&&(h.functional=!0),i&&(h._scopeId="data-v-"+i),s?(c=function(t){(t=t||this.$vnode&&this.$vnode.ssrContext||this.parent&&this.parent.$vnode&&this.parent.$vnode.ssrContext)||"undefined"==typeof __VUE_SSR_CONTEXT__||(t=__VUE_SSR_CONTEXT__),o&&o.call(this,t),t&&t._registeredComponents&&t._registeredComponents.add(s)},h._ssrRegister=c):o&&(c=a?function(){o.call(this,this.$root.$options.shadowRoot)}:o),c)if(h.functional){h._injectStyles=c;var p=h.render;h.render=function(t,e){return c.call(e),p(t,e)}}else{var u=h.beforeCreate;h.beforeCreate=u?[].concat(u,c):[c]}return{exports:t,options:h}}(s,n,[],!1,null,"6dae58fd",null);a.options.__file="src/vue-cropper.vue";var c=a.exports;r.d(e,"VueCropper",(function(){return c}));var h=function(t){t.component("VueCropper",c)};"undefined"!=typeof window&&window.Vue&&h(window.Vue),e.default={version:"0.5.5",install:h,VueCropper:c,vueCropper:c}}])}))},"7f65":function(t,e,r){"use strict";var n=r("59ed"),o=r("825a"),i=r("c65b"),s=r("5926"),a=r("46c4"),c="Invalid size",h=RangeError,p=TypeError,u=Math.max,f=function(t,e){this.set=t,this.size=u(e,0),this.has=n(t.has),this.keys=n(t.keys)};f.prototype={getIterator:function(){return a(o(i(this.keys,this.set)))},includes:function(t){return i(this.has,this.set,t)}},t.exports=function(t){o(t);var e=+t.size;if(e!==e)throw new p(c);var r=s(e);if(r<0)throw new h(c);return new f(t,r)}},"83b9e":function(t,e,r){"use strict";var n=r("cb27"),o=r("384f"),i=n.Set,s=n.add;t.exports=function(t){var e=new i;return o(t,(function(t){s(e,t)})),e}},"8b00":function(t,e,r){"use strict";var n=r("23e7"),o=r("68df"),i=r("dad2");n({target:"Set",proto:!0,real:!0,forced:!i("isSubsetOf")},{isSubsetOf:o})},"8e16":function(t,e,r){"use strict";var n=r("7282"),o=r("cb27");t.exports=n(o.proto,"size","get")||function(t){return t.size}},"953b":function(t,e,r){"use strict";var n=r("dc19"),o=r("cb27"),i=r("8e16"),s=r("7f65"),a=r("384f"),c=r("5388"),h=o.Set,p=o.add,u=o.has;t.exports=function(t){var e=n(this),r=s(t),o=new h;return i(e)>r.size?c(r.getIterator(),(function(t){u(e,t)&&p(o,t)})):a(e,(function(t){r.includes(t)&&p(o,t)})),o}},9961:function(t,e,r){"use strict";var n=r("dc19"),o=r("cb27"),i=r("83b9e"),s=r("7f65"),a=r("5388"),c=o.add,h=o.has,p=o.remove;t.exports=function(t){var e=n(this),r=s(t).getIterator(),o=i(e);return a(r,(function(t){h(e,t)?p(o,t):c(o,t)})),o}},a4e7:function(t,e,r){"use strict";var n=r("23e7"),o=r("395e"),i=r("dad2");n({target:"Set",proto:!0,real:!0,forced:!i("isSupersetOf")},{isSupersetOf:o})},a5f7:function(t,e,r){"use strict";var n=r("dc19"),o=r("cb27"),i=r("83b9e"),s=r("8e16"),a=r("7f65"),c=r("384f"),h=r("5388"),p=o.has,u=o.remove;t.exports=function(t){var e=n(this),r=a(t),o=i(e);return s(e)<=r.size?c(e,(function(t){r.includes(t)&&u(o,t)})):h(r.getIterator(),(function(t){p(e,t)&&u(o,t)})),o}},b4bc:function(t,e,r){"use strict";var n=r("dc19"),o=r("cb27").has,i=r("8e16"),s=r("7f65"),a=r("384f"),c=r("5388"),h=r("2a62");t.exports=function(t){var e=n(this),r=s(t);if(i(e)<=r.size)return!1!==a(e,(function(t){if(r.includes(t))return!1}),!0);var p=r.getIterator();return!1!==c(p,(function(t){if(o(e,t))return h(p,"normal",!1)}))}},c1a1:function(t,e,r){"use strict";var n=r("23e7"),o=r("b4bc"),i=r("dad2");n({target:"Set",proto:!0,real:!0,forced:!i("isDisjointFrom")},{isDisjointFrom:o})},cb27:function(t,e,r){"use strict";var n=r("e330"),o=Set.prototype;t.exports={Set:Set,add:n(o.add),has:n(o.has),remove:n(o["delete"]),proto:o}},dad2:function(t,e,r){"use strict";var n=r("d066"),o=function(t){return{size:t,has:function(){return!1},keys:function(){return{next:function(){return{done:!0}}}}}};t.exports=function(t){var e=n("Set");try{(new e)[t](o(0));try{return(new e)[t](o(-1)),!1}catch(r){return!0}}catch(i){return!1}}},dc19:function(t,e,r){"use strict";var n=r("cb27").has;t.exports=function(t){return n(t),t}},e9bc:function(t,e,r){"use strict";var n=r("dc19"),o=r("cb27").add,i=r("83b9e"),s=r("7f65"),a=r("5388");t.exports=function(t){var e=n(this),r=s(t).getIterator(),c=i(e);return a(r,(function(t){o(c,t)})),c}}}]);