package com.fs.swap.system.mapper;

import java.util.List;
import java.util.Map;
import com.fs.swap.common.core.domain.entity.Message;
import com.fs.swap.common.core.domain.dto.ConversationListDTO;
import com.fs.swap.common.core.domain.dto.UnreadCountDTO;
import org.apache.ibatis.annotations.Param;

/**
 * 消息Mapper接口
 * 
 * <AUTHOR>
 * @date 2024
 */
public interface MessageMapper 
{
    /**
     * 查询消息
     * 
     * @param id 消息主键
     * @return 消息
     */
    public Message selectMessageById(String id);

    /**
     * 查询消息列表
     * 
     * @param message 消息
     * @return 消息集合
     */
    public List<Message> selectMessageList(Message message);

    /**
     * 查询用户消息列表（用于PageHelper分页）
     * 
     * @param userId 用户ID
     * @param type 消息类型
     * @return 消息集合
     */
    public List<Message> selectUserMessageList(@Param("userId") Long userId, @Param("type") String type);

    /**
     * 分页查询用户消息列表
     * 
     * @param userId 用户ID
     * @param type 消息类型
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 消息集合
     */
    public List<Message> selectUserMessageListWithPage(@Param("userId") Long userId, 
                                                      @Param("type") String type,
                                                      @Param("offset") int offset, 
                                                      @Param("limit") int limit);

    /**
     * 获取用户消息总数
     * 
     * @param userId 用户ID
     * @param type 消息类型
     * @return 总数
     */
    public long countUserMessages(@Param("userId") Long userId, @Param("type") String type);

    /**
     * 新增消息
     * 
     * @param message 消息
     * @return 结果
     */
    public int insertMessage(Message message);

    /**
     * 修改消息
     * 
     * @param message 消息
     * @return 结果
     */
    public int updateMessage(Message message);

    /**
     * 删除消息
     * 
     * @param id 消息主键
     * @return 结果
     */
    public int deleteMessageById(String id);

    /**
     * 批量删除消息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMessageByIds(String[] ids);

    /**
     * 获取用户未读消息数量统计
     * 
     * @param userId 用户ID
     * @return 未读消息统计
     */
    public List<UnreadCountDTO> getUserUnreadCount(@Param("userId") Long userId);

    /**
     * 标记消息为已读
     * 
     * @param messageIds 消息ID列表
     * @param userId 用户ID
     * @return 结果
     */
    public int markMessagesAsRead(@Param("messageIds") List<String> messageIds, 
                                 @Param("userId") Long userId);

    /**
     * 批量标记某类型消息为已读
     * 
     * @param userId 用户ID
     * @param type 消息类型
     * @return 结果
     */
    public int markAllMessagesAsRead(@Param("userId") Long userId, @Param("type") String type);

    /**
     * 搜索消息
     * 
     * @param userId 用户ID
     * @param keyword 关键词
     * @param type 消息类型
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 消息集合
     */
    public List<Message> searchMessages(@Param("userId") Long userId, 
                                       @Param("keyword") String keyword,
                                       @Param("type") String type,
                                       @Param("offset") int offset, 
                                       @Param("limit") int limit);

    /**
     * 搜索消息（用于PageHelper分页）
     * 
     * @param userId 用户ID
     * @param keyword 关键词
     * @param type 消息类型
     * @return 消息集合
     */
    public List<Message> searchMessagesList(@Param("userId") Long userId, 
                                           @Param("keyword") String keyword,
                                           @Param("type") String type);

    /**
     * 撤回消息
     * 
     * @param messageId 消息ID
     * @param userId 用户ID
     * @return 结果
     */
    public int recallMessage(@Param("messageId") String messageId, @Param("userId") Long userId);

    /**
     * 批量插入消息（用于群发系统通知）
     * 
     * @param messages 消息列表
     * @return 结果
     */
    public int batchInsertMessages(@Param("messages") List<Message> messages);

    /**
     * 获取会话消息列表
     * 
     * @param conversationId 会话ID
     * @param userId 用户ID
     * @return 消息列表
     */
    public List<Message> getConversationMessages(@Param("conversationId") String conversationId, 
                                                @Param("userId") Long userId);

    /**
     * 获取消息所属的会话ID集合
     * 
     * @param messageIds 消息ID列表
     * @return 会话ID集合
     */
    public List<String> getConversationIdsByMessageIds(@Param("messageIds") List<String> messageIds);

    /**
     * 获取会话中最新的消息ID
     * 
     * @param conversationId 会话ID
     * @return 最新消息ID
     */
    public String getLastMessageIdInConversation(@Param("conversationId") String conversationId);

    /**
     * 根据消息ID列表查询消息
     * 
     * @param messageIds 消息ID列表
     * @return 消息集合
     */
    public List<Message> selectMessagesByIds(@Param("messageIds") List<String> messageIds);

    /**
     * 获取会话中的未读消息
     * 
     * @param conversationId 会话ID
     * @param userId 用户ID
     * @return 未读消息列表
     */
    public List<Message> getUnreadConversationMessages(@Param("conversationId") String conversationId,
                                                      @Param("userId") Long userId);
} 