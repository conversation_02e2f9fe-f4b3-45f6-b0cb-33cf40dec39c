/* 任务中心页面样式 */
.task-container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 快捷操作 */
.quick-actions {
  margin-bottom: 30rpx;
}

.claim-all-btn {
  background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
  color: white;
  border-radius: 50rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
  font-weight: bold;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 6rpx 20rpx rgba(255, 154, 158, 0.4);
  animation: pulse 2s infinite;
}

.claim-all-btn.disabled {
  opacity: 0.6;
  animation: none;
  pointer-events: none;
}

.claim-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 16rpx;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

/* 任务列表 */
.task-list {
  margin-bottom: 40rpx;
}

.task-item {
  background: #fff;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.task-item:active {
  transform: scale(0.98);
}

.task-icon {
  width: 120rpx;
  height: 120rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
  border-radius: 12rpx;
  margin-right: 24rpx;
}

.task-icon .reward-amount {
  font-size: 48rpx;
  color: #ff6b6b;
  font-weight: 600;
  line-height: 1;
  margin-bottom: 8rpx;
  background: linear-gradient(135deg, #ff6b6b 0%, #ff4757 100%);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

.task-icon .reward-label {
  font-size: 24rpx;
  color: #999;
  font-weight: 500;
}

.task-icon image {
  width: 100%;
  height: 100%;
  border-radius: 12rpx;
}

.task-content {
  flex: 1;
  margin-right: 20rpx;
}

.task-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.task-desc {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 16rpx;
  line-height: 1.4;
}

/* 进度条 */
.progress-container {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}

.progress-bar {
  flex: 1;
  height: 4rpx;
  background: #f5f5f5;
  border-radius: 2rpx;
  overflow: hidden;
  margin-right: 16rpx;
}

.progress-fill {
  height: 100%;
  background: #3671FF;
  border-radius: 2rpx;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 22rpx;
  color: #666;
  min-width: 100rpx;
  text-align: right;
}

/* 任务状态 */
.task-status {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status-text {
  font-size: 24rpx;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-weight: 500;
}

.status-1 {
  background: #e3f2fd;
  color: #1976d2;
}

.status-2 {
  background: #e8f5e8;
  color: #4caf50;
}

.status-3 {
  background: #f3e5f5;
  color: #9c27b0;
}

.status-4 {
  background: #fafafa;
  color: #999;
}

.task-type {
  font-size: 22rpx;
  color: #999;
  font-weight: 500;
}

/* 任务操作 */
.task-action {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex-shrink: 0;
}

.reward-info {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.silver-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 8rpx;
}

.reward-amount {
  font-size: 28rpx;
  font-weight: bold;
  color: #ff6b6b;
}

.action-btn {
  min-width: 120rpx;
  height: 48rpx;
  line-height: 48rpx;
  border-radius: 24rpx;
  font-size: 24rpx;
  font-weight: 500;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.action-btn:active {
  opacity: 0.9;
  transform: scale(0.98);
}

.claim-btn {
  background: #10b981;
  color: white;
}

.claim-btn.disabled {
  opacity: 0.6;
  pointer-events: none;
}

.completed-btn {
  background: #f5f5f5;
  color: #999;
}

.progress-btn {
  background: #3671FF;
  color: white;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 100rpx 40rpx;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 40rpx;
  opacity: 0.6;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 加载状态 */
.loading {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 30rpx 60rpx;
  border-radius: 20rpx;
  font-size: 28rpx;
  z-index: 9999;
} 
 