package com.fs.swap.admin.controller.operation;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

import com.fs.swap.admin.dto.NearbyAuditDTO;
import com.fs.swap.common.core.domain.entity.CommunityNearby;
import com.fs.swap.common.utils.AdminSecurityUtils;
import com.fs.swap.system.service.ICommunityNearbyService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.fs.swap.common.annotation.Log;
import com.fs.swap.common.core.controller.AdminBaseController;
import com.fs.swap.common.core.domain.AjaxResult;
import com.fs.swap.common.enums.BusinessType;
import com.fs.swap.common.utils.poi.ExcelUtil;
import com.fs.swap.common.core.page.TableDataInfo;

/**
 * 社区服务-周边推荐Controller
 * 
 * <AUTHOR>
 * @date 2025-05-28
 */
@RestController
@RequestMapping("/operation/nearby")
public class CommunityNearbyController extends AdminBaseController
{
    @Autowired
    private ICommunityNearbyService communityNearbyService;

    /**
     * 查询社区服务-周边推荐列表
     */
    @PreAuthorize("@ss.hasPermi('operation:nearby:list')")
    @GetMapping("/list")
    public TableDataInfo list(CommunityNearby communityNearby)
    {
        startPage();
        List<CommunityNearby> list = communityNearbyService.selectCommunityNearbyList(communityNearby);
        return getDataTable(list);
    }

    /**
     * 导出社区服务-周边推荐列表
     */
    @PreAuthorize("@ss.hasPermi('operation:nearby:export')")
    @Log(title = "社区服务-周边推荐", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CommunityNearby communityNearby)
    {
        List<CommunityNearby> list = communityNearbyService.selectCommunityNearbyList(communityNearby);
        ExcelUtil<CommunityNearby> util = new ExcelUtil<CommunityNearby>(CommunityNearby.class);
        util.exportExcel(response, list, "社区服务-周边推荐数据");
    }

    /**
     * 获取社区服务-周边推荐详细信息
     */
    @PreAuthorize("@ss.hasPermi('operation:nearby:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(communityNearbyService.selectCommunityNearbyById(id));
    }

    /**
     * 新增社区服务-周边推荐
     */
    @PreAuthorize("@ss.hasPermi('operation:nearby:add')")
    @Log(title = "社区服务-周边推荐", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CommunityNearby communityNearby)
    {
        return toAjax(communityNearbyService.insertCommunityNearby(communityNearby));
    }

    /**
     * 修改社区服务-周边推荐
     */
    @PreAuthorize("@ss.hasPermi('operation:nearby:edit')")
    @Log(title = "社区服务-周边推荐", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CommunityNearby communityNearby)
    {
        return toAjax(communityNearbyService.updateCommunityNearby(communityNearby));
    }

    /**
     * 审核社区服务-周边推荐
     */
    @PreAuthorize("@ss.hasPermi('operation:nearby:audit')")
    @Log(title = "审核周边推荐", businessType = BusinessType.UPDATE)
    @PutMapping("/audit")
    public AjaxResult audit(@Valid @RequestBody NearbyAuditDTO auditDTO)
    {
        // 获取当前登录用户ID
        Long auditUserId = AdminSecurityUtils.getUserId();
        
        // 使用批量审核方法
        int successCount = communityNearbyService.batchAuditCommunityNearby(
            auditDTO.getIds(), 
            auditDTO.getAuditStatus(), 
            auditUserId, 
            auditDTO.getAuditRemark()
        );
        
        if (successCount == auditDTO.getIds().size()) {
            return success("审核成功");
        } else if (successCount > 0) {
            return success(String.format("部分审核成功，成功数量：%d/%d", successCount, auditDTO.getIds().size()));
        } else {
            return error("审核失败");
        }
    }

    /**
     * 删除社区服务-周边推荐
     */
    @PreAuthorize("@ss.hasPermi('operation:nearby:remove')")
    @Log(title = "社区服务-周边推荐", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(communityNearbyService.deleteCommunityNearbyByIds(ids));
    }
}
