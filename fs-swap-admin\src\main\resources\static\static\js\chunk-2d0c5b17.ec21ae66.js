(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d0c5b17"],{"3fd2":function(e,t,a){"use strict";a.r(t);var l=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",attrs:{model:e.queryParams,size:"small",inline:!0,"label-width":"68px"}},[a("el-form-item",{attrs:{label:"id",prop:"id"}},[a("el-input",{attrs:{placeholder:"请输入id",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handle<PERSON><PERSON>y(t)}},model:{value:e.queryParams.id,callback:function(t){e.$set(e.queryParams,"id",t)},expression:"queryParams.id"}})],1),a("el-form-item",{attrs:{label:"user_id",prop:"userId"}},[a("el-input",{attrs:{placeholder:"请输入user_id",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.userId,callback:function(t){e.$set(e.queryParams,"userId",t)},expression:"queryParams.userId"}})],1),a("el-form-item",{attrs:{label:"标题",prop:"title"}},[a("el-input",{attrs:{placeholder:"请输入标题",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.title,callback:function(t){e.$set(e.queryParams,"title",t)},expression:"queryParams.title"}})],1),a("el-form-item",{attrs:{label:"类型",prop:"type"}},[a("el-select",{attrs:{placeholder:"请选择类型",clearable:""},model:{value:e.queryParams.type,callback:function(t){e.$set(e.queryParams,"type",t)},expression:"queryParams.type"}},e._l(e.dict.type.cases_type,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",{attrs:{label:"是否匿名",prop:"anonymousIs"}},[a("el-select",{attrs:{placeholder:"请选择是否匿名",clearable:""},model:{value:e.queryParams.anonymousIs,callback:function(t){e.$set(e.queryParams,"anonymousIs",t)},expression:"queryParams.anonymousIs"}},e._l(e.dict.type.sys_yes_no,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),a("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1),a("el-row",{staticClass:"mb8",attrs:{gutter:10}},[a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["operation:cases:export"],expression:"['operation:cases:export']"}],attrs:{type:"warning",plain:"",icon:"el-icon-download",size:"mini"},on:{click:e.handleExport}},[e._v("导出")])],1),a("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.casesList},on:{"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"55",align:"center"}}),a("el-table-column",{attrs:{label:"id",align:"center",prop:"id"}}),a("el-table-column",{attrs:{label:"user_id",align:"center",prop:"userId"}}),a("el-table-column",{attrs:{label:"标题",align:"center",width:"150","show-overflow-tooltip":"",prop:"title"}}),a("el-table-column",{attrs:{label:"描述",align:"center","show-overflow-tooltip":"",prop:"description"}}),a("el-table-column",{attrs:{label:"AI总结",align:"center","show-overflow-tooltip":"",prop:"summary"}}),a("el-table-column",{attrs:{label:"支持文本",align:"center",width:"120",prop:"approveText"}}),a("el-table-column",{attrs:{label:"反对文本",align:"center",width:"120",prop:"againstText"}}),a("el-table-column",{attrs:{label:"类型",align:"center",width:"120",prop:"type"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("dict-tag",{attrs:{options:e.dict.type.cases_type,value:t.row.type}})]}}])}),a("el-table-column",{attrs:{label:"标签",align:"center",prop:"labels"}}),a("el-table-column",{attrs:{label:"附件",align:"center",prop:"evidence",width:"300"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",{staticClass:"image-list"},[t.row.evidence?e._l(t.row.evidence.split(","),(function(t,l){return a("image-preview",{key:l,staticStyle:{margin:"2px"},attrs:{src:e.filePrefix+t,width:50,height:50}})})):e._e()],2)]}}])}),a("el-table-column",{attrs:{label:"邀请状态",align:"center",prop:"inviteStatus"}}),a("el-table-column",{attrs:{label:"创建时间",align:"center",prop:"createTime",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(e.parseTime(t.row.createTime,"{y}-{m}-{d}")))])]}}])}),a("el-table-column",{attrs:{label:"截止时间",align:"center",prop:"endTime",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(e.parseTime(t.row.endTime,"{y}-{m}-{d}")))])]}}])}),a("el-table-column",{attrs:{label:"判决结果",align:"center",prop:"result"}}),a("el-table-column",{attrs:{label:"加速截止时间",align:"center",prop:"boostExpireTime",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(e.parseTime(t.row.boostExpireTime,"{y}-{m}-{d}")))])]}}])}),a("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["operation:cases:edit"],expression:"['operation:cases:edit']"}],attrs:{size:"mini",type:"text",icon:"el-icon-edit"},on:{click:function(a){return e.handleUpdate(t.row)}}},[e._v("修改")])]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}}),a("el-dialog",{attrs:{title:e.title,visible:e.open,width:"500px","append-to-body":""},on:{"update:visible":function(t){e.open=t}}},[a("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"80px"}}),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),a("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1)],1)},n=[],r=a("5530"),i=(a("d81d"),a("d3b7"),a("0643"),a("a573"),a("b775"));function s(e){return Object(i["a"])({url:"/operation/cases/list",method:"get",params:e})}function o(e){return Object(i["a"])({url:"/operation/cases/"+e,method:"get"})}function u(e){return Object(i["a"])({url:"/operation/cases",method:"post",data:e})}function c(e){return Object(i["a"])({url:"/operation/cases",method:"put",data:e})}function p(e){return Object(i["a"])({url:"/operation/cases/"+e,method:"delete"})}var d={name:"Cases",dicts:["cases_type","sys_yes_no"],data:function(){return{loading:!0,filePrefix:"https://ei-image.funnyswap.com/",ids:[],single:!0,multiple:!0,showSearch:!0,total:0,casesList:[],title:"",open:!1,queryParams:{pageNum:1,pageSize:10,id:null,userId:null,title:null,approveText:null,againstText:null,type:null,anonymousIs:null,deleted:null},form:{},rules:{}}},created:function(){this.getList()},methods:{getList:function(){var e=this;this.loading=!0,s(this.queryParams).then((function(t){e.casesList=t.rows,e.total=t.total,e.loading=!1}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={id:null,userId:null,title:null,description:null,summary:null,approveText:null,againstText:null,type:null,labels:null,evidence:null,inviteStatus:null,createTime:null,updateTime:null,endTime:null,anonymousIs:null,result:null,boostExpireTime:null,deleted:null},this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.id})),this.single=1!==e.length,this.multiple=!e.length},handleAdd:function(){this.reset(),this.open=!0,this.title="添加案件列表"},handleUpdate:function(e){var t=this;this.reset();var a=e.id||this.ids;o(a).then((function(e){t.form=e.data,t.open=!0,t.title="修改案件列表"}))},submitForm:function(){var e=this;this.$refs["form"].validate((function(t){t&&(null!=e.form.id?c(e.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.open=!1,e.getList()})):u(e.form).then((function(t){e.$modal.msgSuccess("新增成功"),e.open=!1,e.getList()})))}))},handleDelete:function(e){var t=this,a=e.id||this.ids;this.$modal.confirm('是否确认删除案件列表编号为"'+a+'"的数据项？').then((function(){return p(a)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleExport:function(){this.download("operation/cases/export",Object(r["a"])({},this.queryParams),"cases_".concat((new Date).getTime(),".xlsx"))}}},m=d,h=a("2877"),f=Object(h["a"])(m,l,n,!1,null,null,null);t["default"]=f.exports}}]);