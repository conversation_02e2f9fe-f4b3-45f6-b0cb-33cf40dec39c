(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d0efd67"],{"9a8a":function(e,t,r){"use strict";r.r(t);var a=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticClass:"app-container"},[r("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",attrs:{model:e.queryParams,size:"small",inline:!0,"label-width":"68px"}},[r("el-form-item",{attrs:{label:"卖家id",prop:"userId"}},[r("el-input",{attrs:{placeholder:"请输入卖家id",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.userId,callback:function(t){e.$set(e.queryParams,"userId",t)},expression:"queryParams.userId"}})],1),r("el-form-item",{attrs:{label:"买家id",prop:"buyerId"}},[r("el-input",{attrs:{placeholder:"请输入买家id",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.buyerId,callback:function(t){e.$set(e.queryParams,"buyerId",t)},expression:"queryParams.buyerId"}})],1),r("el-form-item",{attrs:{label:"所属小区ID",prop:"residentialId"}},[r("el-input",{attrs:{placeholder:"请输入所属小区ID",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.residentialId,callback:function(t){e.$set(e.queryParams,"residentialId",t)},expression:"queryParams.residentialId"}})],1),r("el-form-item",{attrs:{label:"标题",prop:"title"}},[r("el-input",{attrs:{placeholder:"请输入标题",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.title,callback:function(t){e.$set(e.queryParams,"title",t)},expression:"queryParams.title"}})],1),r("el-form-item",{attrs:{label:"积分价格",prop:"price"}},[r("el-input",{attrs:{placeholder:"请输入积分价格",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.price,callback:function(t){e.$set(e.queryParams,"price",t)},expression:"queryParams.price"}})],1),r("el-form-item",{attrs:{label:"商品状态",prop:"status"}},[r("el-select",{attrs:{placeholder:"请选择商品状态",clearable:""},model:{value:e.queryParams.status,callback:function(t){e.$set(e.queryParams,"status",t)},expression:"queryParams.status"}},e._l(e.dict.type.product_status,(function(e){return r("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),r("el-form-item",{attrs:{label:"观看人数",prop:"views"}},[r("el-input",{attrs:{placeholder:"请输入观看人数",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.views,callback:function(t){e.$set(e.queryParams,"views",t)},expression:"queryParams.views"}})],1),r("el-form-item",{attrs:{label:"收藏人数",prop:"collections"}},[r("el-input",{attrs:{placeholder:"请输入收藏人数",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.collections,callback:function(t){e.$set(e.queryParams,"collections",t)},expression:"queryParams.collections"}})],1),r("el-form-item",{attrs:{label:"成色",prop:"wear"}},[r("el-select",{attrs:{placeholder:"请选择商品成色",clearable:""},model:{value:e.queryParams.wear,callback:function(t){e.$set(e.queryParams,"wear",t)},expression:"queryParams.wear"}},e._l(e.dict.type.product_wear,(function(e){return r("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),r("el-form-item",[r("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),r("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1),r("el-row",{staticClass:"mb8",attrs:{gutter:10}},[r("el-col",{attrs:{span:1.5}},[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["operation:product:add"],expression:"['operation:product:add']"}],attrs:{type:"primary",plain:"",icon:"el-icon-plus",size:"mini"},on:{click:e.handleAdd}},[e._v("新增")])],1),r("el-col",{attrs:{span:1.5}},[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["operation:product:edit"],expression:"['operation:product:edit']"}],attrs:{type:"success",plain:"",icon:"el-icon-edit",size:"mini",disabled:e.single},on:{click:e.handleUpdate}},[e._v("修改")])],1),r("el-col",{attrs:{span:1.5}},[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["operation:product:remove"],expression:"['operation:product:remove']"}],attrs:{type:"danger",plain:"",icon:"el-icon-delete",size:"mini",disabled:e.multiple},on:{click:e.handleDelete}},[e._v("删除")])],1),r("el-col",{attrs:{span:1.5}},[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["operation:product:export"],expression:"['operation:product:export']"}],attrs:{type:"warning",plain:"",icon:"el-icon-download",size:"mini"},on:{click:e.handleExport}},[e._v("导出")])],1),r("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1),r("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.productList},on:{"selection-change":e.handleSelectionChange}},[r("el-table-column",{attrs:{type:"selection",width:"55",align:"center"}}),r("el-table-column",{attrs:{label:"id",align:"center",prop:"id"}}),r("el-table-column",{attrs:{label:"卖家id",align:"center",prop:"userId"}}),r("el-table-column",{attrs:{label:"买家id",align:"center",prop:"buyerId"}}),r("el-table-column",{attrs:{label:"所属小区ID",align:"center",prop:"residentialId"}}),r("el-table-column",{attrs:{label:"标题",align:"center",prop:"title"}}),r("el-table-column",{attrs:{label:"描述",align:"center",prop:"description"}}),r("el-table-column",{attrs:{label:"积分价格",align:"center",prop:"price"}}),r("el-table-column",{attrs:{label:"商品状态",align:"center",prop:"status"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("dict-tag",{attrs:{options:e.dict.type.product_status,value:t.row.status}})]}}])}),r("el-table-column",{attrs:{label:"图片",align:"center",prop:"images",width:"300"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("div",{staticClass:"image-list"},[t.row.images?e._l(t.row.images.split(","),(function(t,a){return r("image-preview",{key:a,staticStyle:{margin:"2px"},attrs:{src:e.filePrefix+t,width:50,height:50}})})):e._e()],2)]}}])}),r("el-table-column",{attrs:{label:"观看人数",align:"center",prop:"views"}}),r("el-table-column",{attrs:{label:"收藏人数",align:"center",prop:"collections"}}),r("el-table-column",{attrs:{label:"成色",align:"center",prop:"wear"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("dict-tag",{attrs:{options:e.dict.type.product_wear,value:t.row.wear}})]}}])}),r("el-table-column",{attrs:{label:"创建时间",align:"center",prop:"createTime",width:"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("span",[e._v(e._s(e.parseTime(t.row.createTime,"{y}-{m}-{d}")))])]}}])}),r("el-table-column",{attrs:{label:"更新时间",align:"center",prop:"updateTime",width:"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("span",[e._v(e._s(e.parseTime(t.row.updateTime,"{y}-{m}-{d}")))])]}}])}),r("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["operation:product:edit"],expression:"['operation:product:edit']"}],attrs:{size:"mini",type:"text",icon:"el-icon-edit"},on:{click:function(r){return e.handleUpdate(t.row)}}},[e._v("修改")]),r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["operation:product:remove"],expression:"['operation:product:remove']"}],attrs:{size:"mini",type:"text",icon:"el-icon-delete"},on:{click:function(r){return e.handleDelete(t.row)}}},[e._v("删除")])]}}])})],1),r("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}}),r("el-dialog",{attrs:{title:e.title,visible:e.open,width:"500px","append-to-body":""},on:{"update:visible":function(t){e.open=t}}},[r("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"80px"}},[r("el-form-item",{attrs:{label:"卖家id",prop:"userId"}},[r("el-input",{attrs:{placeholder:"请输入卖家id"},model:{value:e.form.userId,callback:function(t){e.$set(e.form,"userId",t)},expression:"form.userId"}})],1),r("el-form-item",{attrs:{label:"买家id",prop:"buyerId"}},[r("el-input",{attrs:{placeholder:"请输入买家id"},model:{value:e.form.buyerId,callback:function(t){e.$set(e.form,"buyerId",t)},expression:"form.buyerId"}})],1),r("el-form-item",{attrs:{label:"所属小区ID",prop:"residentialId"}},[r("el-input",{attrs:{placeholder:"请输入所属小区ID"},model:{value:e.form.residentialId,callback:function(t){e.$set(e.form,"residentialId",t)},expression:"form.residentialId"}})],1),r("el-form-item",{attrs:{label:"标题",prop:"title"}},[r("el-input",{attrs:{placeholder:"请输入标题"},model:{value:e.form.title,callback:function(t){e.$set(e.form,"title",t)},expression:"form.title"}})],1),r("el-form-item",{attrs:{label:"描述",prop:"description"}},[r("el-input",{attrs:{type:"textarea",placeholder:"请输入内容"},model:{value:e.form.description,callback:function(t){e.$set(e.form,"description",t)},expression:"form.description"}})],1),r("el-form-item",{attrs:{label:"积分价格",prop:"price"}},[r("el-input",{attrs:{placeholder:"请输入积分价格"},model:{value:e.form.price,callback:function(t){e.$set(e.form,"price",t)},expression:"form.price"}})],1),r("el-form-item",{attrs:{label:"商品状态",prop:"status"}},[r("el-select",{attrs:{placeholder:"请选择商品状态"},model:{value:e.form.status,callback:function(t){e.$set(e.form,"status",t)},expression:"form.status"}},e._l(e.dict.type.product_status,(function(e){return r("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),r("el-form-item",{attrs:{label:"图片",prop:"images"}},[r("image-upload",{attrs:{type:3},model:{value:e.form.images,callback:function(t){e.$set(e.form,"images",t)},expression:"form.images"}})],1),r("el-form-item",{attrs:{label:"观看人数",prop:"views"}},[r("el-input",{attrs:{placeholder:"请输入观看人数"},model:{value:e.form.views,callback:function(t){e.$set(e.form,"views",t)},expression:"form.views"}})],1),r("el-form-item",{attrs:{label:"收藏人数",prop:"collections"}},[r("el-input",{attrs:{placeholder:"请输入收藏人数"},model:{value:e.form.collections,callback:function(t){e.$set(e.form,"collections",t)},expression:"form.collections"}})],1),r("el-form-item",{attrs:{label:"成色",prop:"wear"}},[r("el-select",{attrs:{placeholder:"请选择商品成色"},model:{value:e.form.wear,callback:function(t){e.$set(e.form,"wear",t)},expression:"form.wear"}},e._l(e.dict.type.product_wear,(function(e){return r("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1)],1),r("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[r("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),r("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1)],1)},l=[],i=r("5530"),n=(r("d81d"),r("d3b7"),r("0643"),r("a573"),r("b775"));function o(e){return Object(n["a"])({url:"/operation/product/list",method:"get",params:e})}function s(e){return Object(n["a"])({url:"/operation/product/"+e,method:"get"})}function u(e){return Object(n["a"])({url:"/operation/product",method:"post",data:e})}function c(e){return Object(n["a"])({url:"/operation/product",method:"put",data:e})}function p(e){return Object(n["a"])({url:"/operation/product/"+e,method:"delete"})}var d={name:"Product",dicts:["product_status","product_wear"],data:function(){return{loading:!0,filePrefix:"https://ei-image.funnyswap.com/",ids:[],single:!0,multiple:!0,showSearch:!0,total:0,productList:[],title:"",open:!1,queryParams:{pageNum:1,pageSize:10,userId:null,buyerId:null,residentialId:null,title:null,description:null,price:null,status:null,images:null,views:null,collections:null,wear:null},form:{},rules:{userId:[{required:!0,message:"卖家id不能为空",trigger:"blur"}],residentialId:[{required:!0,message:"所属小区ID不能为空",trigger:"blur"}],price:[{required:!0,message:"积分价格不能为空",trigger:"blur"}],collections:[{required:!0,message:"收藏人数不能为空",trigger:"blur"}],wear:[{required:!0,message:"商品成色不能为空",trigger:"change"}]}}},created:function(){this.getList()},methods:{getList:function(){var e=this;this.loading=!0,o(this.queryParams).then((function(t){e.productList=t.rows,e.total=t.total,e.loading=!1}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={id:null,userId:null,buyerId:null,residentialId:null,title:null,description:null,price:null,status:null,images:null,views:null,collections:null,wear:null},this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.id})),this.single=1!==e.length,this.multiple=!e.length},handleAdd:function(){this.reset(),this.open=!0,this.title="添加商品列表"},handleUpdate:function(e){var t=this;this.reset();var r=e.id||this.ids;s(r).then((function(e){t.form=e.data,t.open=!0,t.title="修改商品列表"}))},submitForm:function(){var e=this;this.$refs["form"].validate((function(t){t&&(null!=e.form.id?c(e.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.open=!1,e.getList()})):u(e.form).then((function(t){e.$modal.msgSuccess("新增成功"),e.open=!1,e.getList()})))}))},handleDelete:function(e){var t=this,r=e.id||this.ids;this.$modal.confirm('是否确认删除商品列表编号为"'+r+'"的数据项？').then((function(){return p(r)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleExport:function(){this.download("operation/product/export",Object(i["a"])({},this.queryParams),"product_".concat((new Date).getTime(),".xlsx"))}}},m=d,f=r("2877"),h=Object(f["a"])(m,a,l,!1,null,null,null);t["default"]=h.exports}}]);