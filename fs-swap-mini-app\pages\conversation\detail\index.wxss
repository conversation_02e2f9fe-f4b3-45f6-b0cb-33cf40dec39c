/* 聊天详情页面样式 */
page {
  --primary-color: #ffffff;
  --secondary-color: #f8f9fa;
  --accent-color: #95EC69; /* 微信绿色 */
  --accent-light: #E8F3FF;
  --text-color: #333333;
  --text-secondary: #666666;
  --text-light: #999999;
  --background-color: #EDEDED; /* 微信背景色 */
  --border-radius: 10rpx; /* 微信圆角 */
  --border-color: #eeeeee;
  /* 防止页面左右滑动 */
  overflow-x: hidden;
  width: 100vw;
}

.chat-container {
  height: 100vh;
  width: 100vw;
  background-color: var(--background-color);
  display: flex;
  flex-direction: column;
  /* 防止容器左右滑动 */
  overflow-x: hidden;
  box-sizing: border-box;
}

/* 消息列表 */
.message-list {
  flex: 1;
  padding: 20rpx 16rpx;
  /* 防止消息列表左右滚动 */
  overflow-x: hidden;
  box-sizing: border-box;
}

.message-items {
  padding-bottom: 20rpx;
  /* 确保内容不会超出容器 */
  width: 100%;
  box-sizing: border-box;
}

.message-item {
  margin-bottom: 16rpx; /* 调整为更紧凑的微信间距 */
  /* 防止消息项超出容器 */
  width: 100%;
  box-sizing: border-box;
}

/* 时间分组 */
.time-divider {
  text-align: center;
  margin: 32rpx 0 24rpx;
}

.time-text {
  font-size: 24rpx;
  color: var(--text-light);
  background: rgba(255, 255, 255, 0.8);
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
}

/* 消息内容 */
.message-wrapper {
  display: flex;
  align-items: flex-start; /* 改为顶部对齐 */
  margin-bottom: 12rpx; /* 调整为更紧凑的微信间距 */
  /* 修改最大宽度，确保不超出屏幕 */
  max-width: 100%;
  width: 100%;
  box-sizing: border-box;
}

/* 我的消息右对齐 */
.message-item.mine .message-wrapper {
  justify-content: flex-end;
}

/* 消息内容容器 */
.message-content {
  display: flex;
  flex-direction: column;
  max-width: calc(100vw - 80rpx - 32rpx - 32rpx - 60rpx);
  margin-top: 6rpx; /* 与头像顶部对齐 */
}

.message-content.mine {
  align-items: flex-end; /* 我的消息右对齐 */
}

/* 头像 */
.avatar-wrapper {
  margin: 0 16rpx; /* 增加头像间距 */
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.avatar {
  width: 80rpx; /* 微信头像大小 */
  height: 80rpx;
  border-radius: 8rpx; /* 微信头像圆角 */
  background: var(--secondary-color);
  border: none; /* 移除边框 */
  display: block;
  object-fit: cover; /* 确保图片正确缩放 */
  object-position: center; /* 居中显示 */
}

.nickname {
  font-size: 20rpx;
  color: var(--text-light);
  margin-top: 8rpx;
  text-align: center;
  max-width: 80rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 消息气泡 */
.message-bubble {
  /* 重新计算最大宽度：100vw - 头像宽度 - 头像左右边距 - 容器左右内边距 - 安全边距 */
  max-width: calc(100vw - 80rpx - 32rpx - 32rpx - 60rpx);
  min-width: 120rpx;
  padding: 16rpx 20rpx; /* 调整内边距 */
  border-radius: 10rpx; /* 微信圆角 */
  position: relative;
  word-wrap: break-word;
  word-break: break-all;
  box-sizing: border-box;
  overflow-wrap: break-word;
  hyphens: auto;
  /* 确保气泡不会超出 */
  overflow: hidden;
}

/* 添加微信风格的小尖角 */
.message-bubble.other::before {
  content: '';
  position: absolute;
  left: -8rpx;
  bottom: 8rpx;
  width: 0;
  height: 0;
  border: 8rpx solid transparent;
  border-right-color: var(--primary-color);
  border-left: none;
}

.message-bubble.mine::before {
  content: '';
  position: absolute;
  right: -8rpx;
  bottom: 8rpx;
  width: 0;
  height: 0;
  border: 8rpx solid transparent;
  border-left-color: var(--accent-color);
  border-right: none;
}

.message-bubble.other {
  background: var(--primary-color);
  color: var(--text-color);
  border-bottom-left-radius: 4rpx; /* 微信左下角小圆角 */
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
  /* 为其他人的消息设置更精确的最大宽度 */
  max-width: calc(100vw - 80rpx - 32rpx - 32rpx - 60rpx);
}

.message-bubble.mine {
  background: var(--accent-color); /* 微信绿色 */
  color: #333333; /* 深色文字 */
  border-bottom-right-radius: 4rpx; /* 微信右下角小圆角 */
  /* 为我的消息设置更精确的最大宽度 */
  max-width: calc(100vw - 80rpx - 32rpx - 32rpx - 60rpx);
}

.message-bubble.sending {
  opacity: 0.6;
}

.message-text {
  font-size: 32rpx; /* 微信字体大小 */
  line-height: 1.4;
  display: block;
  word-wrap: break-word;
  word-break: break-all;
  overflow-wrap: break-word;
  white-space: pre-wrap;
  max-width: 100%;
  /* 确保文本不会超出气泡 */
  overflow: hidden;
}

/* 发送状态 */
.send-status {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-top: 8rpx;
  margin-right: 0;
}

.status-text {
  font-size: 20rpx;
  color: rgba(51, 51, 51, 0.6); /* 调整状态文字颜色 */
}

.loading {
  display: flex;
  align-items: center;
}

/* 底部占位符 */
.message-bottom {
  height: 20rpx;
}

/* 加载状态 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
}

.loading-text {
  font-size: 28rpx;
  color: var(--text-light);
  margin-top: 16rpx;
}

/* 输入栏 */
.input-bar {
  background: var(--primary-color);
  padding: 16rpx 20rpx;
  padding-bottom: calc(16rpx + env(safe-area-inset-bottom));
  border-top: 1rpx solid var(--border-color);
  box-sizing: border-box;
  /* 确保输入栏不会超出屏幕 */
  width: 100vw;
  overflow-x: hidden;
}

.input-wrapper {
  display: flex;
  align-items: center;
  background: var(--secondary-color);
  border-radius: 48rpx;
  padding: 8rpx 16rpx;
  max-width: 100%;
  box-sizing: border-box;
  /* 防止输入框超出 */
  overflow: hidden;
}

.message-input {
  flex: 1;
  height: 64rpx;
  line-height: 64rpx;
  font-size: 30rpx;
  background: transparent;
  border: none;
  outline: none;
  color: var(--text-color);
  min-width: 0;
  /* 确保输入框不会超出 */
  box-sizing: border-box;
}

.send-btn {
  background: var(--border-color);
  color: var(--text-light);
  border: none;
  border-radius: 32rpx;
  padding: 12rpx 24rpx;
  font-size: 28rpx;
  margin-left: 16rpx;
  transition: all 0.3s ease;
  min-width: 96rpx;
  flex-shrink: 0;
  white-space: nowrap;
}

.send-btn.active {
  background: var(--accent-color); /* 使用微信绿色 */
  color: #333333; /* 深色文字 */
}

.send-btn::after {
  border: none;
}

.send-btn:disabled {
  opacity: 0.6;
}

/* 动画效果 */
.message-item {
  animation: messageSlideIn 0.3s ease-out;
}

@keyframes messageSlideIn {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式优化 - 小屏幕适配 */
@media screen and (max-width: 375px) {
  .message-list {
    padding: 16rpx 12rpx;
  }
  
  .message-content {
    max-width: calc(100vw - 70rpx - 24rpx - 24rpx - 50rpx);
    margin-top: 6rpx;
  }
  
  .avatar-wrapper {
    margin: 0 12rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  
  .avatar {
    width: 70rpx; /* 小屏幕微信头像大小 */
    height: 70rpx;
    border-radius: 8rpx; /* 保持微信圆角 */
    object-fit: cover; /* 确保图片正确缩放 */
    object-position: center; /* 居中显示 */
  }
  
  .nickname {
    font-size: 18rpx;
    margin-top: 6rpx;
    max-width: 70rpx;
  }
  
  .message-bubble {
    /* 小屏幕下重新计算最大宽度 */
    max-width: calc(100vw - 70rpx - 24rpx - 24rpx - 50rpx);
    padding: 14rpx 18rpx;
    border-radius: 10rpx; /* 保持微信圆角 */
  }
  
  .message-bubble.other {
    max-width: calc(100vw - 70rpx - 24rpx - 24rpx - 50rpx);
    border-bottom-left-radius: 4rpx; /* 保持微信左下角小圆角 */
  }
  
  .message-bubble.mine {
    max-width: calc(100vw - 70rpx - 24rpx - 24rpx - 50rpx);
    border-bottom-right-radius: 4rpx; /* 保持微信右下角小圆角 */
  }
  
  .message-text {
    font-size: 30rpx; /* 小屏幕字体 */
  }
  
  /* 小屏幕输入栏优化 */
  .input-bar {
    padding: 12rpx 16rpx;
    padding-bottom: calc(12rpx + env(safe-area-inset-bottom));
  }
  
  .input-wrapper {
    padding: 6rpx 12rpx;
  }
  
  .message-input {
    height: 56rpx;
    line-height: 56rpx;
    font-size: 28rpx;
  }
  
  .send-btn {
    padding: 10rpx 20rpx;
    font-size: 26rpx;
    min-width: 80rpx;
    margin-left: 12rpx;
  }
}

/* 超小屏幕适配 */
@media screen and (max-width: 320px) {
  .message-content {
    max-width: calc(100vw - 60rpx - 20rpx - 20rpx - 40rpx);
    margin-top: 6rpx;
  }
  
  .message-bubble {
    /* 超小屏幕下重新计算最大宽度 */
    max-width: calc(100vw - 60rpx - 20rpx - 20rpx - 40rpx);
    padding: 12rpx 16rpx;
    border-radius: 10rpx; /* 保持微信圆角 */
  }
  
  .message-bubble.other {
    max-width: calc(100vw - 60rpx - 20rpx - 20rpx - 40rpx);
    border-bottom-left-radius: 4rpx; /* 保持微信左下角小圆角 */
  }
  
  .message-bubble.mine {
    max-width: calc(100vw - 60rpx - 20rpx - 20rpx - 40rpx);
    border-bottom-right-radius: 4rpx; /* 保持微信右下角小圆角 */
  }
  
  .avatar-wrapper {
    margin: 0 10rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  
  .avatar {
    width: 60rpx; /* 超小屏幕微信头像大小 */
    height: 60rpx;
    border-radius: 8rpx; /* 保持微信圆角 */
    object-fit: cover; /* 确保图片正确缩放 */
    object-position: center; /* 居中显示 */
  }
  
  .nickname {
    font-size: 16rpx;
    margin-top: 4rpx;
    max-width: 60rpx;
  }
  
  /* 超小屏幕输入栏进一步优化 */
  .input-bar {
    padding: 10rpx 12rpx;
    padding-bottom: calc(10rpx + env(safe-area-inset-bottom));
  }
  
  .input-wrapper {
    padding: 4rpx 10rpx;
    border-radius: 40rpx;
  }
  
  .message-input {
    height: 48rpx;
    line-height: 48rpx;
    font-size: 26rpx;
  }
  
  .send-btn {
    padding: 8rpx 16rpx;
    font-size: 24rpx;
    min-width: 70rpx;
    margin-left: 10rpx;
    border-radius: 28rpx;
  }
} 