<!-- pages/user/myProducts/myProducts.wxml -->
<view class="container">
  <!-- 自定义标签页 -->
  <view class="custom-tabs-container">
    <!-- 一级标签页 -->
    <view class="main-tabs">
      <view
        class="main-tab-item {{mainActiveTab === 0 ? 'active' : ''}}"
        bindtap="onMainTabChange"
        data-index="0"
      >
        我的闲置
      </view>
      <view
        class="main-tab-item {{mainActiveTab === 1 ? 'active' : ''}}"
        bindtap="onMainTabChange"
        data-index="1"
      >
        我的互助
      </view>
    </view>

    <!-- 二级标签页 -->
    <view class="sub-tabs">
      <view
        class="sub-tab-item {{subActiveTab === 0 ? 'active' : ''}}"
        bindtap="onSubTabChange"
        data-index="0"
      >
        {{mainActiveTab === 0 ? '在售' : '发布中'}}
      </view>
      <view
        class="sub-tab-item {{subActiveTab === 1 ? 'active' : ''}}"
        bindtap="onSubTabChange"
        data-index="1"
      >
        已下架
      </view>
    </view>
  </view>

  <!-- 商品内容区域 -->
  <view class="content-area" wx:if="{{mainActiveTab === 0}}">
    <!-- 商品在售 -->
    <view class="product-list" wx:if="{{subActiveTab === 0}}">
      <block wx:if="{{productList.length > 0}}">
        <view class="product-item" wx:for="{{productList}}" wx:key="id">
          <!-- 商品信息区域 -->
          <view class="product-content" bindtap="onProductTap" data-id="{{item.id}}">
            <!-- 商品图片 -->
            <image class="product-image" src="{{item.productImageUrl}}" mode="aspectFill" lazy-load></image>

            <!-- 商品详情 -->
            <view class="product-details">
              <view class="product-description">{{item.description}}</view>
              <view class="product-meta">
                <view class="product-price">¥{{item.price}}</view>
              </view>
              <view class="product-stats">
                <view class="product-views">
                  <van-icon name="eye-o" size="12px" />
                  <text>{{item.views || 0}}</text>
                </view>
                <view class="product-likes">
                  <van-icon name="star-o" size="12px" />
                  <text>{{item.collections || 0}}</text>
                </view>
              </view>
            </view>
          </view>

          <!-- 操作按钮区域 -->
          <view class="product-actions">
            <view class="action-button" catchtap="showProductActionMenu" data-product="{{item}}">
              <van-icon name="ellipsis" size="20px" />
            </view>
          </view>
        </view>
      </block>

      <!-- 空状态 -->
      <view wx:if="{{!productLoading && productList.length === 0}}" class="empty-state">
        <van-empty description="暂无在售商品" />
      </view>

      <!-- 加载状态 -->
      <view wx:if="{{productLoading}}" class="loading-state">
        <van-loading size="24px" color="#FF69B4">加载中...</van-loading>
      </view>

      <!-- 加载完成状态 -->
      <view wx:if="{{!productLoading && !productHasMore && productList.length > 0}}" class="no-more">
        没有更多了~
      </view>
    </view>

    <!-- 商品已下架 -->
    <view class="product-list" wx:if="{{subActiveTab === 1}}">
      <block wx:if="{{productList.length > 0}}">
        <view class="product-item" wx:for="{{productList}}" wx:key="id">
          <!-- 商品信息区域 -->
          <view class="product-content" bindtap="onProductTap" data-id="{{item.id}}">
            <!-- 商品图片 -->
            <image class="product-image" src="{{item.productImageUrl}}" mode="aspectFill" lazy-load></image>

            <!-- 商品详情 -->
            <view class="product-details">
              <view class="product-description">{{item.description}}</view>
              <view class="product-meta">
                <view class="product-price">¥{{item.price}}</view>
              </view>
              <view class="product-stats">
                <view class="product-views">
                  <van-icon name="eye-o" size="12px" />
                  <text>{{item.views || 0}}</text>
                </view>
                <view class="product-likes">
                  <van-icon name="star-o" size="12px" />
                  <text>{{item.collections || 0}}</text>
                </view>
              </view>
            </view>
          </view>

          <!-- 操作按钮区域 -->
          <view class="product-actions">
            <view class="action-button" catchtap="showProductActionMenu" data-product="{{item}}">
              <van-icon name="ellipsis" size="20px" />
            </view>
          </view>
        </view>
      </block>

      <!-- 空状态 -->
      <view wx:if="{{!productLoading && productList.length === 0}}" class="empty-state">
        <van-empty description="暂无下架商品" />
      </view>

      <!-- 加载状态 -->
      <view wx:if="{{productLoading}}" class="loading-state">
        <van-loading size="24px" color="#FF69B4">加载中...</van-loading>
      </view>

      <!-- 加载完成状态 -->
      <view wx:if="{{!productLoading && !productHasMore && productList.length > 0}}" class="no-more">
        没有更多了~
      </view>
    </view>
  </view>

  <!-- 邻里互助内容区域 -->
  <view class="content-area" wx:if="{{mainActiveTab === 1}}">
    <!-- 邻里互助发布中 -->
    <view class="help-list" wx:if="{{subActiveTab === 0}}">
      <block wx:if="{{helpList.length > 0}}">
        <view class="help-item" wx:for="{{helpList}}" wx:key="id" bindtap="onHelpTap" data-id="{{item.id}}">
          <!-- 主要内容区域 -->
          <view class="main-content">
            <!-- 左侧内容 -->
            <view class="content-left">
              <!-- 标题前的标签区域 -->
              <view class="title-tags-section">
                <view class="publish-type-tag {{ item.publishType === '1' ? 'request-tag' : 'offer-tag' }}">
                  <text class="tag-text">{{ item.publishType === '1' ? '发布需求' : '提供服务' }}</text>
                </view>
                <text class="tag-separator">|</text>
                <view class="category-tag">
                  <text class="category-text">{{ item.categoryName || item.category || '其他' }}</text>
                </view>
              </view>

              <!-- 标题 -->
              <view wx:if="{{ item.title }}" class="help-title">{{ item.title }}</view>

              <!-- 内容描述 -->
              <view class="help-content">{{ item.content }}</view>
            </view>

            <!-- 右侧图片 -->
            <view wx:if="{{ item.firstImage }}" class="content-right">
              <view class="image-container">
                <van-image
                  width="100%"
                  height="100%"
                  src="{{ item.firstImage }}"
                  fit="cover"
                  radius="8"
                  lazy-load
                />
              </view>
            </view>
          </view>

          <!-- 底部信息栏 - 简化版本，不显示用户信息 -->
          <view class="bottom-section-simple">
            <view class="left-info">
              <view class="publish-time">发布时间：{{ item.createTime }}</view>
              <view wx:if="{{ item.endTime }}" class="end-time">
                <text class="end-time-text">截止时间：{{ item.endTime }}</text>
              </view>
            </view>

            <view class="right-actions">
              <view class="stat-item">
                <van-icon name="eye-o" size="14px" color="#8a8e99" />
                <text class="stat-text">{{ item.viewCount || 0 }}</text>
              </view>
              <!-- 操作按钮 -->
              <view class="action-button" catchtap="showHelpActionMenu" data-help="{{item}}">
                <van-icon name="ellipsis" size="20px" color="#8a8e99" />
              </view>
            </view>
          </view>
        </view>
      </block>

      <!-- 空状态 -->
      <view wx:if="{{!helpLoading && helpList.length === 0}}" class="empty-state">
        <van-empty description="暂无发布中的互助" />
      </view>

      <!-- 加载状态 -->
      <view wx:if="{{helpLoading}}" class="loading-state">
        <van-loading size="24px" color="#FF69B4">加载中...</van-loading>
      </view>

      <!-- 加载完成状态 -->
      <view wx:if="{{!helpLoading && !helpHasMore && helpList.length > 0}}" class="no-more">
        没有更多了~
      </view>
    </view>

    <!-- 邻里互助已下架 -->
    <view class="help-list" wx:if="{{subActiveTab === 1}}">
      <block wx:if="{{helpList.length > 0}}">
        <view class="help-item" wx:for="{{helpList}}" wx:key="id" bindtap="onHelpTap" data-id="{{item.id}}">
          <!-- 主要内容区域 -->
          <view class="main-content">
            <!-- 左侧内容 -->
            <view class="content-left">
              <!-- 标题前的标签区域 -->
              <view class="title-tags-section">
                <view class="publish-type-tag {{ item.publishType === '1' ? 'request-tag' : 'offer-tag' }}">
                  <text class="tag-text">{{ item.publishType === '1' ? '需求' : '服务' }}</text>
                </view>
                <text class="tag-separator">|</text>
                <view class="category-tag">
                  <text class="category-text">{{ item.categoryName || item.category || '其他' }}</text>
                </view>
              </view>

              <!-- 标题 -->
              <view wx:if="{{ item.title }}" class="help-title">{{ item.title }}</view>

              <!-- 内容描述 -->
              <view class="help-content">{{ item.content }}</view>
            </view>

            <!-- 右侧图片 -->
            <view wx:if="{{ item.firstImage }}" class="content-right">
              <view class="image-container">
                <van-image
                  width="100%"
                  height="100%"
                  src="{{ item.firstImage }}"
                  fit="cover"
                  radius="8"
                  lazy-load
                />
              </view>
            </view>
          </view>

          <!-- 底部信息栏 - 简化版本，不显示用户信息 -->
          <view class="bottom-section-simple">
            <view class="left-info">
              <view class="publish-time">发布时间：{{ item.createTime }}</view>
              <view wx:if="{{ item.endTime }}" class="end-time">
                <van-icon name="clock-o" size="11px" color="#8a8e99" />
                <text class="end-time-text">截止：{{ item.endTime }}</text>
              </view>
            </view>

            <view class="right-actions">
              <view class="stat-item">
                <van-icon name="eye-o" size="14px" color="#8a8e99" />
                <text class="stat-text">{{ item.viewCount || 0 }}</text>
              </view>
              <!-- 操作按钮 -->
              <view class="action-button" catchtap="showHelpActionMenu" data-help="{{item}}">
                <van-icon name="ellipsis" size="20px" color="#8a8e99" />
              </view>
            </view>
          </view>
        </view>
      </block>

      <!-- 空状态 -->
      <view wx:if="{{!helpLoading && helpList.length === 0}}" class="empty-state">
        <van-empty description="暂无下架的互助" />
      </view>

      <!-- 加载状态 -->
      <view wx:if="{{helpLoading}}" class="loading-state">
        <van-loading size="24px" color="#FF69B4">加载中...</van-loading>
      </view>

      <!-- 加载完成状态 -->
      <view wx:if="{{!helpLoading && !helpHasMore && helpList.length > 0}}" class="no-more">
        没有更多了~
      </view>
    </view>
  </view>

  <!-- 操作确认弹窗 -->
  <van-dialog
    use-slot
    title="操作确认"
    show="{{ showActionDialog }}"
    show-cancel-button
    confirm-button-text="确定"
    cancel-button-text="取消"
    bind:confirm="confirmAction"
    bind:cancel="cancelAction"
    confirm-button-color="#FF69B4"
  >
    <view class="dialog-content">
      <text>{{ confirmText }}</text>
    </view>
  </van-dialog>
</view>
