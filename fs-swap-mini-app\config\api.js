const request = require('../utils/request') //引入封装好的js文件
module.exports = {
    systemInfo() {
        return request.get('/common/system_info')
    },
    // 获取轮播广告列表
    getBannerList() {
        return request.get('/banner/list')
    },
    // 登录
    loginByPhone(data) {
        return request.post('/user/login_by_phone', data)
    },
    loginByWechat(data) {
        return request.post('/user/login_by_wechat', data)
    },
    loginUserInfo() {
        return request.get('/user/user_info')
    },
    userSilverInfo() {
        return request.get('/user/silver_info')
    },

    checkLogin() {
        return request.get('/user/check_login')
    },
    updateUserInfo(data) {
        return request.patch('/user/user_info', data)
    },
    // 获取用户联系方式
    getUserContacts() {
        return request.get('/user/contacts')
    },
    // 更新用户联系方式
    updateUserContact(data) {
        return request.post('/user/contact', data)
    },
    // 更新联系方式可见性
    updateContactVisibility(data) {
        return request.put('/user/contact/visibility', data)
    },
    // 银两记录
    getSilverDetail(data) {
        return request.get('/user/silver_detail', data)
    },
    watchAd() {
        return request.get('/user/watch_ad')
    },
     // 地理编码
     getAddress(data) {
      return request.post('/common/geocoder/address',data)
    },
    getResidentialList(data) {
        return request.get('/common/residential_area_list', data)
    },
    // 获取最近小区
    getNearestArea(data) {
        return request.get('/user_home/nearest', data)
    },
    // 选择小区
    selectUserHome(data) {
        return request.post('/user_home/select', data)
    },
    // 绑定小区
    bindResidential(data) {
        return request.post('/user_home/bind', data)
    },
    // 小区认证
    certificationResidential() {
        return request.post('/user_home/certification')
    },
    // 发布闲置商品
    addProduct(data) {
        return request.post('/product/product_add', data)
    },
    // 更新商品
    updateProduct(data) {
        return request.put(`/product/update/${data.id}`, data)
    },
    // 获取商品列表
    getProductList(data) {
        return request.get('/product/product_list', data)
    },
    // 获取商品详情
    getProductDetail(id) {
        return request.get(`/product/product_info/${id}`)
    },
    // 编辑商品时获取商品详情
    getProductDetailForEdit(id) {
        return request.get(`/product/product_info_edit/${id}`)
    },
    // 获取我发布的商品列表
    getMyProducts(data) {
        return request.get('/product/my_products', data)
    },
    // 更新商品状态（上架/下架）
    updateProductStatus(id, status) {
        return request.put(`/product/update_status/${id}`, { status })
    },
    // 删除商品
    deleteProduct(id) {
        return request.delete(`/product/delete/${id}`)
    },

    // 订单相关API
    // 创建订单
    createOrder(data) {
        return request.post('/order/create', data)
    },
    // 获取订单详情
    getOrderDetail(orderId) {
        return request.get(`/order/detail/${orderId}`)
    },
    // 获取我的订单列表
    getMyOrders(data) {
        return request.get('/order/my_orders', data)
    },
    // 确认收货
    confirmOrder(orderId) {
        return request.post(`/order/confirm/${orderId}`)
    },
    // 取消订单
    cancelOrder(orderId) {
        return request.post(`/order/cancel/${orderId}`)
    },
    // 确认联系
    contactOrder(orderId) {
        return request.post(`/order/contact/${orderId}`)
    },
    // 终止订单
    terminationOrder(orderId) {
        return request.post(`/order/termination/${orderId}`)
    },
    // 申请退款
    applyRefund(data) {
        // 构建查询字符串
        const queryString = `orderId=${data.orderId}&reason=${encodeURIComponent(data.reason)}`;
        return request.post('/order/refund?' + queryString)
    },
    // 同意退款
    agreeRefund(orderId) {
        return request.post(`/order/agree_refund/${orderId}`)
    },
    // 拒绝退款
    rejectRefund(data) {
        // 构建查询字符串
        const queryString = `orderId=${data.orderId}&reason=${encodeURIComponent(data.reason)}`;
        return request.post('/order/reject_refund?' + queryString)
    },
    // 获取订单用户联系方式
    getOrderUserContact(orderId) {
        return request.get(`/order/contact/${orderId}`)
    },

    // 收藏相关API
    // 获取收藏列表
    getCollectionList() {
        return request.get('/collection/list')
    },
    // 添加或取消收藏
    toggleCollection(businessId) {
        return request.post('/collection/toggle', { businessId })
    },
    // 检查是否已收藏
    checkCollection(businessId) {
        return request.get('/collection/check', { businessId })
    },

    // 获取用户统计数据
    getUserStats() {
        return request.get('/user/stats')
    },

    // 获取用户当前小区信息
    getCurrentUserResidential() {
        return request.get('/user_home/current_user_residential_area')
    },

    // 关注相关API
    // 获取关注列表
    getFollowList(data) {
        return request.get('/follow/list', data)
    },
    // 获取粉丝列表
    getFansList(data) {
        return request.get('/follow/fans', data)
    },
    // 关注或取消关注用户
    toggleFollow(followUserId) {
        return request.post('/follow/toggle', { followUserId })
    },
    // 检查是否已关注
    checkFollow(followUserId) {
        return request.get('/follow/check', { followUserId })
    },
    // 获取关注和粉丝数量
    getFollowCount(userId) {
        return request.get('/follow/count', { userId })
    },

    // 获取用户公开信息
    getUserPublicInfo(userId) {
        return request.get(`/user/user_public_info/${userId}`)
    },

    // 修改个性签名
    changeSlogan(slogan) {
        return request.post('/user/change_slogan?slogan=' + slogan)
    },

    // 活动相关API
    // 获取活动列表
    getActivityList(data) {
        return request.get('/activity/list', data)
    },
    // 获取活动详情
    getActivityDetail(id) {
        return request.get(`/activity/${id}`)
    },
    // 发布活动
    addActivity(data) {
        return request.post('/activity', data)
    },
    // 更新活动
    updateActivity(data) {
        return request.put('/activity', data)
    },
    // 取消活动
    cancelActivity(id) {
        return request.put(`/activity/cancel/${id}`)
    },
    // 删除活动
    deleteActivity(id) {
        return request.delete(`/activity/${id}`)
    },
    // 获取我发布的活动
    getMyActivities(data) {
        return request.get('/activity/my', data)
    },
    // 获取我参与的活动
    getJoinedActivities(data) {
        return request.get('/activity/joined', data)
    },
    // 获取指定用户发布的活动
    getUserActivities(userId, data) {
        return request.get(`/activity/user/${userId}`, data)
    },

    // 活动报名相关API
    // 报名活动
    signupActivity(data) {
        return request.post('/activity/signup', data)
    },
    // 取消报名
    cancelActivitySignup(id) {
        return request.put(`/activity/signup/cancel/${id}`)
    },
    // 审核报名
    auditActivitySignup(id, status) {
        return request.put(`/activity/signup/audit/${id}/${status}`)
    },
    // 获取活动报名列表
    getActivitySignups(activityId, data) {
        return request.get(`/activity/signup/list/${activityId}`, data)
    },
    // 获取我的报名列表
    getMySignups(data) {
        return request.get('/activity/signup/my', data)
    },
    // 获取活动报名者联系方式
    getActivitySignupContact(signupId) {
        return request.get(`/activity/signup/contact/${signupId}`)
    },

    // 活动评论相关API
    // 获取活动评论列表
    getActivityComments(activityId, data) {
        return request.get(`/activity/comment/list/${activityId}`, data)
    },
    // 添加评论
    addActivityComment(data) {
        return request.post('/activity/comment', data)
    },
    // 删除评论
    deleteActivityComment(id) {
        return request.delete(`/activity/comment/${id}`)
    },

    // 社区服务相关API
    // 获取社区服务常用电话列表
    getCommunityPhoneList(data) {
        return request.get('/community-service/phone/list', data)
    },
    // 获取社区服务周边推荐列表
    getCommunityNearbyList(data) {
        return request.get('/community-service/nearby/list', data)
    },
    // 获取服务详情
    getCommunityNearbyDetail(id) {
        return request.get(`/community-service/nearby/detail/${id}`)
    },
    // 记录拨打电话行为
    recordPhoneCall(id) {
        return request.post(`/community-service/phone/call/${id}`)
    },
    // 记录导航行为
    recordNavigate(id) {
        return request.post(`/community-service/nearby/navigate/${id}`)
    },
    // 获取推荐服务列表
    getRecommendedNearby(limit = 10) {
        return request.get('/community-service/nearby/recommended', { limit })
    },
    // 获取热门服务列表
    getPopularNearby(data) {
        return request.get('/community-service/nearby/popular', data)
    },

    // 社区服务提交相关API
    // 提交常用电话
    submitCommunityPhone(data) {
        return request.post('/community-submit/phone/submit', data)
    },
    // 提交周边推荐
    submitCommunityNearby(data) {
        return request.post('/community-submit/nearby/submit', data)
    },
    // 获取我提交的常用电话列表
    getMySubmittedPhones(data) {
        return request.get('/community-submit/phone/my-submitted', data)
    },
    // 获取我提交的周边推荐列表
    getMySubmittedNearby(data) {
        return request.get('/community-submit/nearby/my-submitted', data)
    },
    
    // 社区电话统计API
    getPhoneStats(data) {
        return request.get('/community-service/phone/stats', data)
    },

    // ============ 消息系统相关API ============
    // 获取消息列表
    getMessageList(data) {
        return request.get('/message/list', data)
    },
    // 获取未读消息数量
    getMessageUnreadCount() {
        return request.get('/message/unread-count')
    },
    // 标记消息已读
    markMessageAsRead(data) {
        return request.post('/message/mark-read', data)
    },
    // 批量标记消息已读
    markAllMessagesAsRead(data) {
        return request.post('/message/mark-all-read', data)
    },
    // 删除消息
    deleteMessages(data) {
        return request.post('/message/delete', data)
    },
    // 搜索消息
    searchMessages(data) {
        return request.get('/message/search', data)
    },

    // ============ 用户聊天相关API ============
    // 获取会话列表
    getConversationList(data) {
        return request.get('/message/conversations', data)
    },
    // 获取会话消息
    getConversationMessages(conversationId, data) {
        return request.get(`/message/conversation/${conversationId}/messages`, data)
    },
    // 发送用户消息
    sendUserMessage(data) {
        return request.post('/message/send-user', data)
    },
    // 创建或获取会话
    createConversation(data) {
        return request.post('/message/conversation/create', data)
    },

    // ============ 消息设置相关API ============
    // 获取消息设置
    getMessageSettings() {
        return request.get('/message/settings')
    },
    // 更新消息设置
    updateMessageSettings(data) {
        return request.post('/message/settings', data)
    },
    // 设置消息免打扰
    setMessageMute(data) {
        return request.post('/message/mute', data)
    },

    // ============ 管理员消息接口（后期扩展） ============
    // 发送系统通知
    sendSystemNotification(data) {
        return request.post('/message/send-system', data)
    },
    // 获取系统消息发送记录
    getSystemMessageRecords(data) {
        return request.get('/admin/message/send-records', data)
    },
    // 获取消息统计
    getMessageStatistics(data) {
        return request.get('/message/statistics', data)
    },

    // 任务相关API
    // 获取任务列表
    getTaskList(data) {
        return request.get('/task/list', data)
    },
    // 领取任务奖励
    claimTaskReward(taskId) {
        return request.post(`/task/claim/${taskId}`)
    },
    // 一键领取所有奖励
    claimAllTaskRewards() {
        return request.post('/task/claim-all')
    },

    // 碳豆排行榜相关API
    // 获取月度排行榜
    getMonthlyRanking(data) {
        return request.get('/ranking/monthly', data)
    },
    // 获取完整排行榜（支持更多参数）
    getRankingList(data) {
        return request.get('/ranking/list', data)
    },
    // 获取上期榜单
    getPreviousRanking(data) {
        return request.get('/ranking/previous', data)
    },
    // 获取用户自己的排名信息
    getMyRanking() {
        return request.get('/ranking/my-ranking')
    },
    // 获取用户奖励历史
    getRankingRewardHistory() {
        return request.get('/ranking/rewards/history')
    },

    // 意见反馈相关接口
    // 提交反馈
    submitFeedback(data) {
        return request.post('/feedback', data)
    },
    // 获取我的反馈列表
    getMyFeedbackList(data) {
        return request.get('/feedback/my', data)
    },

    // 邻里互助相关接口
    // 获取互助列表
    getCommunityHelpList(data) {
        return request.get('/community-help/list', data)
    },
    // 获取互助详情
    getCommunityHelpDetail(id) {
        return request.get(`/community-help/detail/${id}`)
    },
    // 发布互助
    publishCommunityHelp(data) {
        return request.post('/community-help/publish', data)
    },
    // 更新互助
    updateCommunityHelp(data) {
        return request.put('/community-help/update', data)
    },
    // 联系发布者
    contactCommunityHelp(id) {
        return request.post(`/order/create/help?id=${id}`)
    },
    // 获取我发布的互助
    getMyCommunityHelp(data) {
        return request.get('/community-help/my-published', data)
    },
    // 更新互助状态
    updateCommunityHelpStatus(id, status) {
        return request.put(`/community-help/update-status/${id}`, { status })
    },
    // 删除互助
    deleteCommunityHelp(id) {
        return request.delete(`/community-help/delete/${id}`)
    },
    // 获取邻里互助统计数据
    getCommunityHelpStats(data) {
        return request.get('/community-help/stats', data)
    }
}
