<view class="order-detail-container">

  <!-- 加载中 -->
  <view class="loading-container" wx:if="{{loading}}">
    <van-loading size="24px" color="#1989fa">加载中...</van-loading>
  </view>

  <!-- 联系方式弹窗组件 -->
  <contact-modal
    show="{{showContactModal}}"
    targetNickname="{{targetNickname}}"
    contacts="{{contacts}}"
    fileUrl="{{fileUrl}}"
    bind:close="closeContactModal"
  ></contact-modal>

  <!-- 申请退款弹窗 -->
  <view class="refund-modal" wx:if="{{showRefundModal}}">
    <view class="refund-modal-mask" bindtap="closeRefundModal"></view>
    <view class="refund-modal-content">
      <view class="refund-modal-header">
        <text class="refund-modal-title">申请退款</text>
        <view class="refund-modal-close" bindtap="closeRefundModal">×</view>
      </view>
      <view class="refund-modal-body">
        <view class="refund-product-info">
          <image class="refund-product-image" src="{{orderDetail.productMainImage}}" mode="aspectFill"></image>
          <view class="refund-product-detail">
            <view class="refund-product-title">{{orderDetail.productDescription}}</view>
            <!-- 只有非社区互助类型的订单才显示价格 -->
            <view class="refund-product-price" wx:if="{{orderDetail.orderType !== 'COMMUNITY_HELP'}}">¥{{orderDetail.productPrice}}</view>
          </view>
        </view>

        <view class="refund-reason-title">请选择退款原因</view>
        <view class="refund-reason-list">
          <view
            class="refund-reason-item {{selectedReasonIndex === index ? 'active' : ''}}"
            wx:for="{{refundReasonOptions}}"
            wx:key="index"
            data-index="{{index}}"
            bindtap="onSelectRefundReason"
          >
            <text>{{item}}</text>
            <van-icon wx:if="{{selectedReasonIndex === index}}" name="success" color="#07c160" size="16px" />
          </view>
        </view>

        <view class="refund-custom-reason">
          <textarea
            class="refund-custom-input"
            placeholder="或输入其他退款原因..."
            value="{{customReason}}"
            bindinput="onInputCustomReason"
            maxlength="200"
          ></textarea>
          <view class="refund-custom-count">{{customReason.length}}/200</view>
        </view>
      </view>
      <view class="refund-modal-footer">
        <button class="refund-modal-btn cancel" bindtap="closeRefundModal">取消</button>
        <button class="refund-modal-btn confirm" bindtap="submitRefund" disabled="{{submittingRefund}}">
          <van-loading size="16px" color="#ffffff" wx:if="{{submittingRefund}}"></van-loading>
          <text wx:else>提交</text>
        </button>
      </view>
    </view>
  </view>

  <!-- 订单详情内容 -->
  <view class="order-content" wx:if="{{!loading}}">
    <!-- 订单状态 -->
    <view class="order-status-section">
      <view class="status-icon">
        <van-icon name="{{orderDetail.orderStatus === '2' ? 'checked' : (orderDetail.orderStatus === 3 || orderDetail.orderStatus === '5' ? 'close' : 'clock-o')}}" size="32px" color="{{orderDetail.orderStatus === '2' ? '#07c160' : (orderDetail.orderStatus === '3' || orderDetail.orderStatus === '5' ? '#ee0a24' : '#ff6b00')}}" />
      </view>
      <view class="status-info">
        <view class="status-text">{{statusMap[orderDetail.orderStatus]}}</view>
        <view class="status-desc" wx:if="{{orderDetail.orderStatus === '1'}}">等待买家联系沟通线下交易细节</view>
        <view class="status-desc" wx:elif="{{orderDetail.orderStatus === '2'}}">等待买家/卖家确认交易完成</view>
        <view class="status-desc" wx:elif="{{orderDetail.orderStatus === '3'}}">订单已完成</view>
        <view class="status-desc" wx:elif="{{orderDetail.orderStatus === '4'}}">订单已取消</view>
        <view class="status-desc" wx:elif="{{orderDetail.orderStatus === '8'}}">订单已经终止</view>
      </view>
    </view>

    <!-- 商品信息 -->
    <view class="order-section">
      <view class="section-title">商品信息</view>
      <view class="product-card" bindtap="onViewProduct">
        <image class="product-image" src="{{orderDetail.productMainImage}}" mode="aspectFill"></image>
        <view class="product-info">
          <view class="product-title">{{orderDetail.productDescription}}</view>
          <!-- 只有非社区互助类型的订单才显示价格 -->
          <view class="product-price" wx:if="{{orderDetail.orderType !== 'COMMUNITY_HELP'}}">
            <text wx:if="{{orderDetail.productPrice == 0}}">免费送</text>
            <text wx:else>¥{{orderDetail.productPrice}}</text>
          </view>
        </view>
        <van-icon name="arrow" size="16px" color="#cccccc" />
      </view>
    </view>

    <!-- 订单信息 -->
    <view class="order-section">
      <view class="section-title">订单信息</view>
      <view class="info-item">
        <text class="info-label">订单编号</text>
        <view class="info-value-with-copy">
          <text class="info-value">{{orderDetail.orderNo}}</text>
          <view class="copy-icon" bindtap="copyOrderNo">
            <van-icon name="description-o" size="16px" />
          </view>
        </view>
      </view>
      <view class="info-item">
        <text class="info-label">创建时间</text>
        <text class="info-value">{{orderDetail.createTime}}</text>
      </view>
      <view class="info-item" wx:if="{{orderDetail.completeTime}}">
        <text class="info-label">完成时间</text>
        <text class="info-value">{{orderDetail.completeTime}}</text>
      </view>
      <!-- <view class="info-item">
        <text class="info-label">支付方式</text>
        <text class="info-value">碳豆支付</text>
      </view>
      <view class="info-item">
        <text class="info-label">支付碳豆</text>
        <text class="info-value">{{orderDetail.points}}</text>
      </view> -->
    </view>

    <!-- 买家信息 - 仅卖家可见 -->
    <view class="order-section" wx:if="{{userRole === 'seller'}}">
      <view class="section-title">买家信息</view>
      <view class="user-info">
        <image class="user-avatar" src="{{orderDetail.buyerAvatar || '/static/img/default_avatar.png'}}" mode="aspectFill" bindtap="onViewBuyerProfile" data-id="{{orderDetail.buyerId}}"></image>
        <view class="user-detail">
          <view class="user-name">{{orderDetail.buyerNickname || '匿名用户'}}</view>
        </view>
        <!-- <button class="contact-button" bindtap="onContactBuyer">联系买家</button> -->
      </view>
    </view>

    <!-- 卖家信息 - 仅买家可见 -->
    <view class="order-section" wx:if="{{userRole === 'buyer'}}">
      <view class="section-title">发布者</view>
      <view class="user-info">
        <image class="user-avatar" src="{{orderDetail.sellerAvatar || '/static/img/default_avatar.png'}}" mode="aspectFill" bindtap="onViewSellerProfile" data-id="{{orderDetail.sellerId}}"></image>
        <view class="user-detail">
          <view class="user-name">{{orderDetail.sellerNickname || '匿名用户'}}</view>
        </view>
        <button class="contact-button" bindtap="onContactSeller">联系TA</button>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-buttons">
      <!-- 买家操作按钮 -->
      <block wx:if="{{orderDetail.buyerId === (userInfo && userInfo.id)}}">
        <!-- 待联系卖家状态 -->
        <block wx:if="{{orderDetail.orderStatus === '1'}}">
          <button class="action-button cancel" catchtap="onCancelOrder">取消订单</button>
          <button class="action-button primary" catchtap="onContactOrder">确认已联系</button>
        </block>

        <!-- 待双方确认状态 -->
        <block wx:if="{{orderDetail.orderStatus === '2'}}">
          <button class="action-button danger" catchtap="onTerminateOrder">终止订单</button>
          <button class="action-button primary" catchtap="onFinishOrder">完成订单</button>
        </block>

        <!-- 已完成状态 -->
        <block wx:elif="{{orderDetail.orderStatus === '3'}}">
          <button class="action-button disabled">已完成</button>
        </block>

        <!-- 已终止状态 -->
        <block wx:elif="{{orderDetail.orderStatus === '8'}}">
          <button class="action-button disabled">已终止</button>
        </block>
      </block>

      <!-- 卖家操作按钮 -->
      <block wx:if="{{orderDetail.sellerId === (userInfo && userInfo.id)}}">
        <!-- 待双方确认状态 -->
        <block wx:if="{{orderDetail.orderStatus === '2'}}">
          <button class="action-button primary" catchtap="onFinishOrder">完成订单</button>
          <button class="action-button danger" catchtap="onTerminateOrder">终止订单</button>
        </block>

        <!-- 已完成状态 -->
        <block wx:elif="{{orderDetail.orderStatus === '3'}}">
          <button class="action-button disabled">已完成</button>
        </block>

        <!-- 已终止状态 -->
        <block wx:elif="{{orderDetail.orderStatus === '8'}}">
          <button class="action-button disabled">已终止</button>
        </block>
      </block>
    </view>
  </view>

  <!-- 操作确认弹框 -->
  <van-dialog
    use-slot
    title="操作确认"
    show="{{ showConfirmDialog }}"
    show-cancel-button
    confirm-button-text="确定"
    cancel-button-text="取消"
    bind:confirm="confirmAction"
    bind:cancel="cancelAction"
    confirm-button-color="#07c160"
  >
    <view class="dialog-content">
      <text>{{ confirmText }}</text>
    </view>
  </van-dialog>

</view>
