(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d2088c6"],{a4eb:function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",attrs:{model:e.queryParams,size:"small",inline:!0,"label-width":"68px"}},[a("el-form-item",{attrs:{label:"标题",prop:"title"}},[a("el-input",{attrs:{placeholder:"请输入标题",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handle<PERSON><PERSON>y(t)}},model:{value:e.queryParams.title,callback:function(t){e.$set(e.queryParams,"title",t)},expression:"queryParams.title"}})],1),a("el-form-item",{attrs:{label:"话题分类",prop:"category"}},[a("el-select",{attrs:{placeholder:"请选择话题分类",clearable:""},model:{value:e.queryParams.category,callback:function(t){e.$set(e.queryParams,"category",t)},expression:"queryParams.category"}},e._l(e.dict.type.community_help_topic,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",{attrs:{label:"发布类型",prop:"publishType"}},[a("el-select",{attrs:{placeholder:"请选择发布类型",clearable:""},model:{value:e.queryParams.publishType,callback:function(t){e.$set(e.queryParams,"publishType",t)},expression:"queryParams.publishType"}},e._l(e.dict.type.community_help_publish_type,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",{attrs:{label:"状态",prop:"status"}},[a("el-select",{attrs:{placeholder:"请选择状态",clearable:""},model:{value:e.queryParams.status,callback:function(t){e.$set(e.queryParams,"status",t)},expression:"queryParams.status"}},e._l(e.dict.type.community_help_status,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",{attrs:{label:"发布时间",prop:"createTime"}},[a("el-date-picker",{attrs:{clearable:"",type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期"},model:{value:e.daterangeCreateTime,callback:function(t){e.daterangeCreateTime=t},expression:"daterangeCreateTime"}})],1),a("el-form-item",{attrs:{label:"截止时间",prop:"endTime"}},[a("el-date-picker",{attrs:{clearable:"",type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期"},model:{value:e.daterangeEndTime,callback:function(t){e.daterangeEndTime=t},expression:"daterangeEndTime"}})],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),a("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1),a("el-row",{staticClass:"mb8",attrs:{gutter:10}},[a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["operation:communityHelp:add"],expression:"['operation:communityHelp:add']"}],attrs:{type:"primary",plain:"",icon:"el-icon-plus",size:"mini"},on:{click:e.handleAdd}},[e._v("新增")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["operation:communityHelp:edit"],expression:"['operation:communityHelp:edit']"}],attrs:{type:"success",plain:"",icon:"el-icon-edit",size:"mini",disabled:e.single},on:{click:e.handleUpdate}},[e._v("修改")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["operation:communityHelp:remove"],expression:"['operation:communityHelp:remove']"}],attrs:{type:"danger",plain:"",icon:"el-icon-delete",size:"mini",disabled:e.multiple},on:{click:e.handleDelete}},[e._v("删除")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["operation:communityHelp:export"],expression:"['operation:communityHelp:export']"}],attrs:{type:"warning",plain:"",icon:"el-icon-download",size:"mini"},on:{click:e.handleExport}},[e._v("导出")])],1),a("el-col",{attrs:{span:1.5}},[a("el-dropdown",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["operation:communityHelp:audit"],expression:"['operation:communityHelp:audit']"}],on:{command:e.handleBatchAudit}},[a("el-button",{attrs:{type:"info",plain:"",size:"mini",disabled:e.multiple}},[e._v(" 批量审核"),a("i",{staticClass:"el-icon-arrow-down el-icon--right"})]),a("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[a("el-dropdown-item",{attrs:{command:"1"}},[e._v("批量通过")]),a("el-dropdown-item",{attrs:{command:"3"}},[e._v("批量拒绝")])],1)],1)],1),a("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.communityHelpList},on:{"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"55",align:"center"}}),a("el-table-column",{attrs:{label:"ID",align:"center",prop:"id",width:"80"}}),a("el-table-column",{attrs:{label:"标题",align:"center",prop:"title","show-overflow-tooltip":!0}}),a("el-table-column",{attrs:{label:"话题分类",align:"center",prop:"category"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("dict-tag",{attrs:{options:e.dict.type.community_help_topic,value:t.row.category}})]}}])}),a("el-table-column",{attrs:{label:"发布类型",align:"center",prop:"publishType"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("dict-tag",{attrs:{options:e.dict.type.community_help_publish_type,value:t.row.publishType}})]}}])}),a("el-table-column",{attrs:{label:"发布者",align:"center",prop:"nickname"}}),a("el-table-column",{attrs:{label:"浏览次数",align:"center",prop:"viewCount"}}),a("el-table-column",{attrs:{label:"状态",align:"center",prop:"status"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("dict-tag",{attrs:{options:e.dict.type.community_help_status,value:t.row.status}})]}}])}),a("el-table-column",{attrs:{label:"发布时间",align:"center",prop:"createTime",width:"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(e.parseTime(t.row.createTime,"{y}-{m}-{d} {h}:{i}:{s}")))])]}}])}),a("el-table-column",{attrs:{label:"截止时间",align:"center",prop:"endTime",width:"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(e.parseTime(t.row.endTime,"{y}-{m}-{d} {h}:{i}:{s}")))])]}}])}),a("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{size:"mini",type:"text",icon:"el-icon-view"},on:{click:function(a){return e.handleDetail(t.row)}}},[e._v("详情")]),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["operation:communityHelp:edit"],expression:"['operation:communityHelp:edit']"}],attrs:{size:"mini",type:"text",icon:"el-icon-edit"},on:{click:function(a){return e.handleUpdate(t.row)}}},[e._v("修改")]),a("el-dropdown",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["operation:communityHelp:audit"],expression:"['operation:communityHelp:audit']"}],on:{command:function(a){return e.handleAudit(t.row,a)}}},[a("el-button",{attrs:{size:"mini",type:"text"}},[e._v(" 审核"),a("i",{staticClass:"el-icon-arrow-down el-icon--right"})]),a("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[a("el-dropdown-item",{attrs:{command:"1"}},[e._v("通过")]),a("el-dropdown-item",{attrs:{command:"3"}},[e._v("拒绝")])],1)],1),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["operation:communityHelp:remove"],expression:"['operation:communityHelp:remove']"}],attrs:{size:"mini",type:"text",icon:"el-icon-delete"},on:{click:function(a){return e.handleDelete(t.row)}}},[e._v("删除")])]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}}),a("el-dialog",{attrs:{title:e.title,visible:e.open,width:"600px","append-to-body":""},on:{"update:visible":function(t){e.open=t}}},[a("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"80px"}},[a("el-form-item",{attrs:{label:"标题",prop:"title"}},[a("el-input",{attrs:{placeholder:"请输入标题"},model:{value:e.form.title,callback:function(t){e.$set(e.form,"title",t)},expression:"form.title"}})],1),a("el-form-item",{attrs:{label:"话题分类",prop:"category"}},[a("el-select",{attrs:{placeholder:"请选择话题分类"},model:{value:e.form.category,callback:function(t){e.$set(e.form,"category",t)},expression:"form.category"}},e._l(e.dict.type.community_help_topic,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",{attrs:{label:"发布类型",prop:"publishType"}},[a("el-select",{attrs:{placeholder:"请选择发布类型"},model:{value:e.form.publishType,callback:function(t){e.$set(e.form,"publishType",t)},expression:"form.publishType"}},e._l(e.dict.type.community_help_publish_type,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",{attrs:{label:"详细内容",prop:"content"}},[a("el-input",{attrs:{type:"textarea",placeholder:"请输入详细内容",rows:4},model:{value:e.form.content,callback:function(t){e.$set(e.form,"content",t)},expression:"form.content"}})],1),a("el-form-item",{attrs:{label:"联系方式",prop:"contactInfo"}},[a("el-input",{attrs:{placeholder:"请输入联系方式"},model:{value:e.form.contactInfo,callback:function(t){e.$set(e.form,"contactInfo",t)},expression:"form.contactInfo"}})],1),a("el-form-item",{attrs:{label:"截止时间",prop:"endTime"}},[a("el-date-picker",{attrs:{type:"datetime",placeholder:"请选择截止时间","value-format":"yyyy-MM-dd HH:mm:ss"},model:{value:e.form.endTime,callback:function(t){e.$set(e.form,"endTime",t)},expression:"form.endTime"}})],1),a("el-form-item",{attrs:{label:"状态",prop:"status"}},[a("el-select",{attrs:{placeholder:"请选择状态"},model:{value:e.form.status,callback:function(t){e.$set(e.form,"status",t)},expression:"form.status"}},e._l(e.dict.type.community_help_status,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),a("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1),a("el-dialog",{attrs:{title:"邻里互助详情",visible:e.detailOpen,width:"800px","append-to-body":""},on:{"update:visible":function(t){e.detailOpen=t}}},[e.detailData?a("el-descriptions",{attrs:{column:2,border:""}},[a("el-descriptions-item",{attrs:{label:"标题"}},[e._v(e._s(e.detailData.title))]),a("el-descriptions-item",{attrs:{label:"话题分类"}},[a("dict-tag",{attrs:{options:e.dict.type.community_help_topic,value:e.detailData.category}})],1),a("el-descriptions-item",{attrs:{label:"发布类型"}},[a("dict-tag",{attrs:{options:e.dict.type.community_help_publish_type,value:e.detailData.publishType}})],1),a("el-descriptions-item",{attrs:{label:"发布者"}},[e._v(e._s(e.detailData.nickname))]),a("el-descriptions-item",{attrs:{label:"浏览次数"}},[e._v(e._s(e.detailData.viewCount))]),a("el-descriptions-item",{attrs:{label:"状态"}},[a("dict-tag",{attrs:{options:e.dict.type.community_help_status,value:e.detailData.status}})],1),a("el-descriptions-item",{attrs:{label:"发布时间"}},[e._v(e._s(e.parseTime(e.detailData.createTime)))]),a("el-descriptions-item",{attrs:{label:"截止时间"}},[e._v(e._s(e.parseTime(e.detailData.endTime)))]),a("el-descriptions-item",{attrs:{label:"详细内容",span:2}},[e._v(e._s(e.detailData.content))]),a("el-descriptions-item",{attrs:{label:"联系方式",span:2}},[e._v(e._s(e.detailData.contactInfo))]),e.detailData.auditRemark?a("el-descriptions-item",{attrs:{label:"审核备注",span:2}},[e._v(e._s(e.detailData.auditRemark))]):e._e()],1):e._e(),e.detailImages.length>0?a("div",{staticStyle:{"margin-top":"20px"}},[a("h4",[e._v("图片")]),e._l(e.detailImages,(function(t,i){return a("el-image",{key:i,staticStyle:{width:"100px",height:"100px","margin-right":"10px","margin-bottom":"10px"},attrs:{src:t,"preview-src-list":e.detailImages,fit:"cover"}})}))],2):e._e()],1),a("el-dialog",{attrs:{title:"审核邻里互助",visible:e.auditOpen,width:"500px","append-to-body":""},on:{"update:visible":function(t){e.auditOpen=t}}},[a("el-form",{ref:"auditForm",attrs:{model:e.auditForm,"label-width":"80px"}},[a("el-form-item",{attrs:{label:"审核结果"}},[a("el-radio-group",{model:{value:e.auditForm.status,callback:function(t){e.$set(e.auditForm,"status",t)},expression:"auditForm.status"}},[a("el-radio",{attrs:{label:"1"}},[e._v("通过")]),a("el-radio",{attrs:{label:"3"}},[e._v("拒绝")])],1)],1),a("el-form-item",{attrs:{label:"审核备注"}},[a("el-input",{attrs:{type:"textarea",placeholder:"请输入审核备注",rows:3},model:{value:e.auditForm.auditRemark,callback:function(t){e.$set(e.auditForm,"auditRemark",t)},expression:"auditForm.auditRemark"}})],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.submitAudit}},[e._v("确 定")]),a("el-button",{on:{click:function(t){e.auditOpen=!1}}},[e._v("取 消")])],1)],1)],1)},l=[],r=a("5530"),n=(a("4de4"),a("d81d"),a("b64b"),a("d3b7"),a("498a"),a("0643"),a("2382"),a("a573"),a("b775"));function o(e){return Object(n["a"])({url:"/operation/communityHelp/list",method:"get",params:e})}function s(e){return Object(n["a"])({url:"/operation/communityHelp/"+e,method:"get"})}function u(e){return Object(n["a"])({url:"/operation/communityHelp",method:"post",data:e})}function m(e){return Object(n["a"])({url:"/operation/communityHelp",method:"put",data:e})}function c(e){return Object(n["a"])({url:"/operation/communityHelp/"+e,method:"delete"})}function d(e,t,a){return Object(n["a"])({url:"/operation/communityHelp/audit/".concat(e),method:"put",params:{status:t,auditRemark:a}})}function p(e,t,a){return Object(n["a"])({url:"/operation/communityHelp/batchAudit",method:"put",params:{ids:e,status:t,auditRemark:a}})}var h={name:"CommunityHelp",dicts:["community_help_topic","community_help_publish_type","community_help_status"],data:function(){return{loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,communityHelpList:[],title:"",open:!1,detailOpen:!1,detailData:null,detailImages:[],auditOpen:!1,auditForm:{id:null,status:"1",auditRemark:""},daterangeCreateTime:[],daterangeEndTime:[],queryParams:{pageNum:1,pageSize:10,title:null,category:null,publishType:null,status:null,createTime:null,endTime:null},form:{},rules:{title:[{required:!0,message:"标题不能为空",trigger:"blur"}],content:[{required:!0,message:"详细内容不能为空",trigger:"blur"}],category:[{required:!0,message:"话题分类不能为空",trigger:"change"}],publishType:[{required:!0,message:"发布类型不能为空",trigger:"change"}],contactInfo:[{required:!0,message:"联系方式不能为空",trigger:"blur"}],endTime:[{required:!0,message:"截止时间不能为空",trigger:"change"}]}}},created:function(){this.getList()},methods:{getList:function(){var e=this;this.loading=!0,this.queryParams.params={},null!=this.daterangeCreateTime&&""!=this.daterangeCreateTime&&(this.queryParams.params["beginCreateTime"]=this.daterangeCreateTime[0],this.queryParams.params["endCreateTime"]=this.daterangeCreateTime[1]),null!=this.daterangeEndTime&&""!=this.daterangeEndTime&&(this.queryParams.params["beginEndTime"]=this.daterangeEndTime[0],this.queryParams.params["endEndTime"]=this.daterangeEndTime[1]),o(this.queryParams).then((function(t){e.communityHelpList=t.rows,e.total=t.total,e.loading=!1}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={id:null,title:null,content:null,images:null,category:null,publishType:null,contactInfo:null,viewCount:null,status:"1",communityId:null,residentialId:null,userId:null,endTime:null,deleted:null},this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.daterangeCreateTime=[],this.daterangeEndTime=[],this.resetForm("queryForm"),this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.id})),this.single=1!==e.length,this.multiple=!e.length},handleAdd:function(){this.reset(),this.open=!0,this.title="添加邻里互助"},handleUpdate:function(e){var t=this;this.reset();var a=e.id||this.ids;s(a).then((function(e){t.form=e.data,t.open=!0,t.title="修改邻里互助"}))},submitForm:function(){var e=this;this.$refs["form"].validate((function(t){t&&(null!=e.form.id?m(e.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.open=!1,e.getList()})):u(e.form).then((function(t){e.$modal.msgSuccess("新增成功"),e.open=!1,e.getList()})))}))},handleDelete:function(e){var t=this,a=e.id||this.ids;this.$modal.confirm('是否确认删除邻里互助编号为"'+a+'"的数据项？').then((function(){return c(a)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleExport:function(){this.download("operation/communityHelp/export",Object(r["a"])({},this.queryParams),"communityHelp_".concat((new Date).getTime(),".xlsx"))},handleDetail:function(e){var t=this;s(e.id).then((function(e){if(t.detailData=e.data,t.detailImages=[],t.detailData.images)try{t.detailImages=JSON.parse(t.detailData.images)}catch(a){t.detailImages=t.detailData.images.split(",").filter((function(e){return e.trim()}))}t.detailOpen=!0}))},handleAudit:function(e,t){this.auditForm={id:e.id,status:t,auditRemark:""},this.auditOpen=!0},handleBatchAudit:function(e){0!==this.ids.length?(this.auditForm={ids:this.ids,status:e,auditRemark:""},this.auditOpen=!0):this.$modal.msgError("请选择要审核的数据")},submitAudit:function(){var e=this;this.auditForm.ids?p(this.auditForm.ids,this.auditForm.status,this.auditForm.auditRemark).then((function(){e.$modal.msgSuccess("审核成功"),e.auditOpen=!1,e.getList()})):d(this.auditForm.id,this.auditForm.status,this.auditForm.auditRemark).then((function(){e.$modal.msgSuccess("审核成功"),e.auditOpen=!1,e.getList()}))}}},y=h,f=a("2877"),b=Object(f["a"])(y,i,l,!1,null,null,null);t["default"]=b.exports}}]);