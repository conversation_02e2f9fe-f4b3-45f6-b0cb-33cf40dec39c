(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-13730932"],{"18fb":function(e,t,a){"use strict";a("46eb")},"46eb":function(e,t,a){},6169:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",attrs:{model:e.queryParams,size:"small",inline:!0,"label-width":"68px"}},[a("el-form-item",{attrs:{label:"任务编码",prop:"taskCode"}},[a("el-input",{attrs:{placeholder:"请输入任务编码",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.taskCode,callback:function(t){e.$set(e.queryParams,"taskCode",t)},expression:"queryParams.taskCode"}})],1),a("el-form-item",{attrs:{label:"任务名称",prop:"taskName"}},[a("el-input",{attrs:{placeholder:"请输入任务名称",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.taskName,callback:function(t){e.$set(e.queryParams,"taskName",t)},expression:"queryParams.taskName"}})],1),a("el-form-item",{attrs:{label:"任务类型",prop:"taskType"}},[a("el-select",{attrs:{placeholder:"请选择任务类型",clearable:""},model:{value:e.queryParams.taskType,callback:function(t){e.$set(e.queryParams,"taskType",t)},expression:"queryParams.taskType"}},e._l(e.dict.type.task_type,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",{attrs:{label:"触发事件",prop:"triggerEvent"}},[a("el-select",{attrs:{placeholder:"请选择触发事件",clearable:""},model:{value:e.queryParams.triggerEvent,callback:function(t){e.$set(e.queryParams,"triggerEvent",t)},expression:"queryParams.triggerEvent"}},e._l(e.dict.type.task_event_type,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",{attrs:{label:"状态",prop:"status"}},[a("el-select",{attrs:{placeholder:"请选择状态",clearable:""},model:{value:e.queryParams.status,callback:function(t){e.$set(e.queryParams,"status",t)},expression:"queryParams.status"}},[a("el-option",{attrs:{label:"启用",value:"1"}}),a("el-option",{attrs:{label:"禁用",value:"0"}})],1)],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),a("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1),a("el-row",{staticClass:"mb8",attrs:{gutter:10}},[a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["operation:task-config:add"],expression:"['operation:task-config:add']"}],attrs:{type:"primary",plain:"",icon:"el-icon-plus",size:"mini"},on:{click:e.handleAdd}},[e._v("新增")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["operation:task-config:edit"],expression:"['operation:task-config:edit']"}],attrs:{type:"success",plain:"",icon:"el-icon-edit",size:"mini",disabled:e.single},on:{click:e.handleUpdate}},[e._v("修改")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["operation:task-config:remove"],expression:"['operation:task-config:remove']"}],attrs:{type:"danger",plain:"",icon:"el-icon-delete",size:"mini",disabled:e.multiple},on:{click:e.handleDelete}},[e._v("删除")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["operation:task-config:export"],expression:"['operation:task-config:export']"}],attrs:{type:"warning",plain:"",icon:"el-icon-download",size:"mini"},on:{click:e.handleExport}},[e._v("导出")])],1),a("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.taskConfigList},on:{"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"55",align:"center"}}),a("el-table-column",{attrs:{label:"任务编码",align:"center",prop:"taskCode",width:"150"}}),a("el-table-column",{attrs:{label:"任务名称",align:"center",prop:"taskName",width:"150"}}),a("el-table-column",{attrs:{label:"任务描述",align:"center",prop:"taskDesc","show-overflow-tooltip":!0}}),a("el-table-column",{attrs:{label:"任务类型",align:"center",prop:"taskType",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("dict-tag",{attrs:{options:e.dict.type.task_type,value:t.row.taskType}})]}}])}),a("el-table-column",{attrs:{label:"触发事件",align:"center",prop:"triggerEvent",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("dict-tag",{attrs:{options:e.dict.type.task_event_type,value:t.row.triggerEvent}})]}}])}),a("el-table-column",{attrs:{label:"目标次数",align:"center",prop:"targetCount",width:"100"}}),a("el-table-column",{attrs:{label:"奖励碳豆",align:"center",prop:"rewardSilver",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",{staticClass:"reward-silver"},[e._v(e._s(t.row.rewardSilver))])]}}])}),a("el-table-column",{attrs:{label:"界面路径",align:"center",prop:"page",width:"200"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(t.row.page||"-"))])]}}])}),a("el-table-column",{attrs:{label:"图标",align:"center",prop:"icon",width:"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.icon?a("el-image",{staticStyle:{width:"30px",height:"30px","border-radius":"4px"},attrs:{src:t.row.icon,"preview-src-list":[t.row.icon],fit:"cover"}}):a("span",[e._v("-")])]}}])}),a("el-table-column",{attrs:{label:"排序",align:"center",prop:"sortOrder",width:"80"}}),a("el-table-column",{attrs:{label:"状态",align:"center",prop:"status",width:"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-switch",{attrs:{"active-value":"1","inactive-value":"0"},on:{change:function(a){return e.handleStatusChange(t.row)}},model:{value:t.row.status,callback:function(a){e.$set(t.row,"status",a)},expression:"scope.row.status"}})]}}])}),a("el-table-column",{attrs:{label:"创建者",align:"center",prop:"createBy",width:"100"}}),a("el-table-column",{attrs:{label:"创建时间",align:"center",prop:"createTime",width:"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(e.parseTime(t.row.createTime,"{y}-{m}-{d} {h}:{i}:{s}")))])]}}])}),a("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width",width:"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["operation:task-config:query"],expression:"['operation:task-config:query']"}],attrs:{size:"mini",type:"text",icon:"el-icon-view"},on:{click:function(a){return e.handleView(t.row)}}},[e._v("详情")]),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["operation:task-config:edit"],expression:"['operation:task-config:edit']"}],attrs:{size:"mini",type:"text",icon:"el-icon-edit"},on:{click:function(a){return e.handleUpdate(t.row)}}},[e._v("修改")]),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["operation:task-config:remove"],expression:"['operation:task-config:remove']"}],attrs:{size:"mini",type:"text",icon:"el-icon-delete"},on:{click:function(a){return e.handleDelete(t.row)}}},[e._v("删除")])]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}}),a("el-dialog",{attrs:{title:e.title,visible:e.open,width:"600px","append-to-body":""},on:{"update:visible":function(t){e.open=t}}},[a("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"80px"}},[a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"任务编码",prop:"taskCode"}},[a("el-input",{attrs:{placeholder:"请输入任务编码",disabled:null!=e.form.id},model:{value:e.form.taskCode,callback:function(t){e.$set(e.form,"taskCode",t)},expression:"form.taskCode"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"任务名称",prop:"taskName"}},[a("el-input",{attrs:{placeholder:"请输入任务名称"},model:{value:e.form.taskName,callback:function(t){e.$set(e.form,"taskName",t)},expression:"form.taskName"}})],1)],1)],1),a("el-form-item",{attrs:{label:"任务描述",prop:"taskDesc"}},[a("el-input",{attrs:{type:"textarea",placeholder:"请输入任务描述"},model:{value:e.form.taskDesc,callback:function(t){e.$set(e.form,"taskDesc",t)},expression:"form.taskDesc"}})],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"任务类型",prop:"taskType"}},[a("el-select",{attrs:{placeholder:"请选择任务类型"},model:{value:e.form.taskType,callback:function(t){e.$set(e.form,"taskType",t)},expression:"form.taskType"}},e._l(e.dict.type.task_type,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"触发事件",prop:"triggerEvent"}},[a("el-select",{attrs:{placeholder:"请选择触发事件"},model:{value:e.form.triggerEvent,callback:function(t){e.$set(e.form,"triggerEvent",t)},expression:"form.triggerEvent"}},e._l(e.dict.type.task_event_type,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"目标次数",prop:"targetCount"}},[a("el-input-number",{staticStyle:{width:"100%"},attrs:{min:1,max:9999,"controls-position":"right"},model:{value:e.form.targetCount,callback:function(t){e.$set(e.form,"targetCount",t)},expression:"form.targetCount"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"奖励碳豆",prop:"rewardSilver"}},[a("el-input-number",{staticStyle:{width:"100%"},attrs:{min:0,max:9999,"controls-position":"right"},model:{value:e.form.rewardSilver,callback:function(t){e.$set(e.form,"rewardSilver",t)},expression:"form.rewardSilver"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"界面路径",prop:"page"}},[a("el-input",{attrs:{placeholder:"请输入界面路径"},model:{value:e.form.page,callback:function(t){e.$set(e.form,"page",t)},expression:"form.page"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"排序",prop:"sortOrder"}},[a("el-input-number",{staticStyle:{width:"100%"},attrs:{min:0,max:9999,"controls-position":"right"},model:{value:e.form.sortOrder,callback:function(t){e.$set(e.form,"sortOrder",t)},expression:"form.sortOrder"}})],1)],1)],1),a("el-form-item",{attrs:{label:"状态",prop:"status"}},[a("el-radio-group",{model:{value:e.form.status,callback:function(t){e.$set(e.form,"status",t)},expression:"form.status"}},[a("el-radio",{attrs:{label:"1"}},[e._v("启用")]),a("el-radio",{attrs:{label:"0"}},[e._v("禁用")])],1)],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),a("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1),a("el-dialog",{attrs:{title:"任务配置详情",visible:e.viewOpen,width:"600px","append-to-body":""},on:{"update:visible":function(t){e.viewOpen=t}}},[a("el-descriptions",{attrs:{column:2,border:""}},[a("el-descriptions-item",{attrs:{label:"任务编码"}},[e._v(e._s(e.viewForm.taskCode))]),a("el-descriptions-item",{attrs:{label:"任务名称"}},[e._v(e._s(e.viewForm.taskName))]),a("el-descriptions-item",{attrs:{label:"任务描述",span:2}},[e._v(e._s(e.viewForm.taskDesc))]),a("el-descriptions-item",{attrs:{label:"任务类型"}},[a("dict-tag",{attrs:{options:e.dict.type.task_type,value:e.viewForm.taskType}})],1),a("el-descriptions-item",{attrs:{label:"触发事件"}},[a("dict-tag",{attrs:{options:e.dict.type.task_event_type,value:e.viewForm.triggerEvent}})],1),a("el-descriptions-item",{attrs:{label:"目标次数"}},[e._v(e._s(e.viewForm.targetCount))]),a("el-descriptions-item",{attrs:{label:"奖励碳豆"}},[a("span",{staticClass:"reward-silver"},[e._v(e._s(e.viewForm.rewardSilver))])]),a("el-descriptions-item",{attrs:{label:"界面路径",span:2}},[a("span",[e._v(e._s(e.viewForm.page||"-"))])]),a("el-descriptions-item",{attrs:{label:"图标",span:2}},[e.viewForm.icon?a("el-image",{staticStyle:{width:"60px",height:"60px","border-radius":"8px"},attrs:{src:e.viewForm.icon,"preview-src-list":[e.viewForm.icon],fit:"cover"}}):a("span",[e._v("-")])],1),a("el-descriptions-item",{attrs:{label:"排序"}},[e._v(e._s(e.viewForm.sortOrder))]),a("el-descriptions-item",{attrs:{label:"状态"}},[a("el-tag",{attrs:{type:"1"===e.viewForm.status?"success":"danger"}},[e._v(" "+e._s("1"===e.viewForm.status?"启用":"禁用")+" ")])],1),a("el-descriptions-item",{attrs:{label:"创建者"}},[e._v(e._s(e.viewForm.createBy))]),a("el-descriptions-item",{attrs:{label:"创建时间"}},[e._v(e._s(e.parseTime(e.viewForm.createTime)))]),a("el-descriptions-item",{attrs:{label:"更新者"}},[e._v(e._s(e.viewForm.updateBy))]),a("el-descriptions-item",{attrs:{label:"更新时间"}},[e._v(e._s(e.parseTime(e.viewForm.updateTime)))])],1)],1)],1)},s=[],i=a("5530"),l=(a("d81d"),a("d3b7"),a("0643"),a("a573"),a("b775"));function o(e){return Object(l["a"])({url:"/operation/task-config/list",method:"get",params:e})}function n(e){return Object(l["a"])({url:"/operation/task-config/"+e,method:"get"})}function c(e){return Object(l["a"])({url:"/operation/task-config",method:"post",data:e})}function u(e){return Object(l["a"])({url:"/operation/task-config",method:"put",data:e})}function p(e){return Object(l["a"])({url:"/operation/task-config/"+e,method:"delete"})}var m={name:"TaskConfig",dicts:["task_type","task_event_type"],data:function(){return{loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,taskConfigList:[],title:"",open:!1,viewOpen:!1,queryParams:{pageNum:1,pageSize:10,taskCode:null,taskName:null,taskType:null,triggerEvent:null,status:null},form:{},viewForm:{},rules:{taskCode:[{required:!0,message:"任务编码不能为空",trigger:"blur"},{min:2,max:50,message:"任务编码长度必须介于 2 和 50 之间",trigger:"blur"}],taskName:[{required:!0,message:"任务名称不能为空",trigger:"blur"},{min:2,max:100,message:"任务名称长度必须介于 2 和 100 之间",trigger:"blur"}],taskType:[{required:!0,message:"任务类型不能为空",trigger:"change"}],triggerEvent:[{required:!0,message:"触发事件不能为空",trigger:"change"}],targetCount:[{required:!0,message:"目标次数不能为空",trigger:"blur"},{type:"number",min:1,message:"目标次数必须大于0",trigger:"blur"}],rewardSilver:[{required:!0,message:"奖励碳豆数不能为空",trigger:"blur"},{type:"number",min:0,message:"奖励碳豆数不能小于0",trigger:"blur"}]}}},created:function(){this.getList()},methods:{getList:function(){var e=this;this.loading=!0,o(this.queryParams).then((function(t){e.taskConfigList=t.rows,e.total=t.total,e.loading=!1}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={id:null,taskCode:null,taskName:null,taskDesc:null,taskType:null,triggerEvent:null,targetCount:1,rewardSilver:0,icon:null,sortOrder:0,status:"1"},this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.id})),this.single=1!==e.length,this.multiple=!e.length},handleAdd:function(){this.reset(),this.open=!0,this.title="添加任务配置"},handleUpdate:function(e){var t=this;this.reset();var a=e.id||this.ids;n(a).then((function(e){t.form=e.data,t.open=!0,t.title="修改任务配置"}))},handleView:function(e){var t=this;n(e.id).then((function(e){t.viewForm=e.data,t.viewOpen=!0}))},submitForm:function(){var e=this;this.$refs["form"].validate((function(t){t&&(null!=e.form.id?u(e.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.open=!1,e.getList()})):c(e.form).then((function(t){e.$modal.msgSuccess("新增成功"),e.open=!1,e.getList()})))}))},handleDelete:function(e){var t=this,a=e.id||this.ids;this.$modal.confirm('是否确认删除任务配置编号为"'+a+'"的数据项？').then((function(){return p(a)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleStatusChange:function(e){var t=this,a="1"===e.status?"启用":"停用";this.$modal.confirm('确认要"'+a+'""'+e.taskName+'"任务吗？').then((function(){return u(e)})).then((function(){t.$modal.msgSuccess(a+"成功")})).catch((function(){e.status="0"===e.status?"1":"0"}))},handleExport:function(){this.download("operation/task-config/export",Object(i["a"])({},this.queryParams),"task_config_".concat((new Date).getTime(),".xlsx"))}}},d=m,f=(a("18fb"),a("2877")),g=Object(f["a"])(d,r,s,!1,null,"642732a6",null);t["default"]=g.exports}}]);