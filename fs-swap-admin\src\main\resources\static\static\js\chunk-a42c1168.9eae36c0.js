(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-a42c1168"],{2137:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",attrs:{model:e.queryParams,size:"small",inline:!0,"label-width":"68px"}},[a("el-form-item",{attrs:{label:"用户ID",prop:"userId"}},[a("el-input",{attrs:{placeholder:"请输入用户ID",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.userId,callback:function(t){e.$set(e.queryParams,"userId",t)},expression:"queryParams.userId"}})],1),a("el-form-item",{attrs:{label:"用户名",prop:"userName"}},[a("el-input",{attrs:{placeholder:"请输入用户名",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.userName,callback:function(t){e.$set(e.queryParams,"userName",t)},expression:"queryParams.userName"}})],1),a("el-form-item",{attrs:{label:"任务编码",prop:"taskCode"}},[a("el-input",{attrs:{placeholder:"请输入任务编码",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.taskCode,callback:function(t){e.$set(e.queryParams,"taskCode",t)},expression:"queryParams.taskCode"}})],1),a("el-form-item",{attrs:{label:"任务类型",prop:"taskType"}},[a("el-select",{attrs:{placeholder:"请选择任务类型",clearable:""},model:{value:e.queryParams.taskType,callback:function(t){e.$set(e.queryParams,"taskType",t)},expression:"queryParams.taskType"}},e._l(e.dict.type.task_type,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",{attrs:{label:"任务状态",prop:"status"}},[a("el-select",{attrs:{placeholder:"请选择任务状态",clearable:""},model:{value:e.queryParams.status,callback:function(t){e.$set(e.queryParams,"status",t)},expression:"queryParams.status"}},e._l(e.dict.type.task_status,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",{attrs:{label:"完成时间",prop:"dateRange"}},[a("el-date-picker",{staticStyle:{width:"240px"},attrs:{"value-format":"yyyy-MM-dd",type:"daterange","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期"},model:{value:e.dateRange,callback:function(t){e.dateRange=t},expression:"dateRange"}})],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),a("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1),a("el-row",{staticClass:"mb8",attrs:{gutter:10}},[a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["operation:user-task:export"],expression:"['operation:user-task:export']"}],attrs:{type:"warning",plain:"",icon:"el-icon-download",size:"mini"},on:{click:e.handleExport}},[e._v("导出")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["operation:user-task:refresh"],expression:"['operation:user-task:refresh']"}],attrs:{type:"info",plain:"",icon:"el-icon-refresh",size:"mini"},on:{click:e.handleRefreshTask}},[e._v("刷新任务")])],1),a("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.userTaskList},on:{"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"55",align:"center"}}),a("el-table-column",{attrs:{label:"用户ID",align:"center",prop:"userId",width:"80"}}),a("el-table-column",{attrs:{label:"用户名",align:"center",prop:"userName",width:"120"}}),a("el-table-column",{attrs:{label:"任务编码",align:"center",prop:"taskCode",width:"150"}}),a("el-table-column",{attrs:{label:"任务名称",align:"center",prop:"taskName",width:"150"}}),a("el-table-column",{attrs:{label:"任务类型",align:"center",prop:"taskType",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("dict-tag",{attrs:{options:e.dict.type.task_type,value:t.row.taskType}})]}}])}),a("el-table-column",{attrs:{label:"目标次数",align:"center",prop:"targetCount",width:"100"}}),a("el-table-column",{attrs:{label:"当前进度",align:"center",prop:"currentCount",width:"100"},scopedSlots:e._u([{key:"default",fn:function(e){return[a("el-progress",{attrs:{percentage:(e.row.currentCount/e.row.targetCount*100).toFixed(0),"stroke-width":15,"text-inside":!0,format:function(t){return e.row.currentCount+"/"+e.row.targetCount}}})]}}])}),a("el-table-column",{attrs:{label:"奖励碳豆",align:"center",prop:"rewardSilver",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",{staticClass:"reward-silver"},[e._v(e._s(t.row.rewardSilver))])]}}])}),a("el-table-column",{attrs:{label:"状态",align:"center",prop:"status",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("dict-tag",{attrs:{options:e.dict.type.task_status,value:t.row.status}})]}}])}),a("el-table-column",{attrs:{label:"完成时间",align:"center",prop:"completedTime",width:"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(e.parseTime(t.row.completedTime,"{y}-{m}-{d} {h}:{i}:{s}")))])]}}])}),a("el-table-column",{attrs:{label:"领取时间",align:"center",prop:"claimedTime",width:"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(e.parseTime(t.row.claimedTime,"{y}-{m}-{d} {h}:{i}:{s}")))])]}}])}),a("el-table-column",{attrs:{label:"过期时间",align:"center",prop:"expiredTime",width:"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(e.parseTime(t.row.expiredTime,"{y}-{m}-{d} {h}:{i}:{s}")))])]}}])}),a("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["operation:user-task:query"],expression:"['operation:user-task:query']"}],attrs:{size:"mini",type:"text",icon:"el-icon-view"},on:{click:function(a){return e.handleView(t.row)}}},[e._v("详情")]),"2"===t.row.status?a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["operation:user-task:claim"],expression:"['operation:user-task:claim']"}],attrs:{size:"mini",type:"text",icon:"el-icon-coin"},on:{click:function(a){return e.handleClaim(t.row)}}},[e._v("领取")]):e._e()]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}}),a("el-dialog",{attrs:{title:"用户任务详情",visible:e.viewOpen,width:"600px","append-to-body":""},on:{"update:visible":function(t){e.viewOpen=t}}},[a("el-descriptions",{attrs:{column:2,border:""}},[a("el-descriptions-item",{attrs:{label:"用户ID"}},[e._v(e._s(e.viewForm.userId))]),a("el-descriptions-item",{attrs:{label:"用户名"}},[e._v(e._s(e.viewForm.userName))]),a("el-descriptions-item",{attrs:{label:"任务编码"}},[e._v(e._s(e.viewForm.taskCode))]),a("el-descriptions-item",{attrs:{label:"任务名称"}},[e._v(e._s(e.viewForm.taskName))]),a("el-descriptions-item",{attrs:{label:"任务类型"}},[a("dict-tag",{attrs:{options:e.dict.type.task_type,value:e.viewForm.taskType}})],1),a("el-descriptions-item",{attrs:{label:"触发事件"}},[a("dict-tag",{attrs:{options:e.dict.type.task_event_type,value:e.viewForm.triggerEvent}})],1),a("el-descriptions-item",{attrs:{label:"目标次数"}},[e._v(e._s(e.viewForm.targetCount))]),a("el-descriptions-item",{attrs:{label:"当前进度"}},[e._v(e._s(e.viewForm.currentCount))]),a("el-descriptions-item",{attrs:{label:"奖励碳豆"}},[a("span",{staticClass:"reward-silver"},[e._v(e._s(e.viewForm.rewardSilver))])]),a("el-descriptions-item",{attrs:{label:"状态"}},[a("dict-tag",{attrs:{options:e.dict.type.task_status,value:e.viewForm.status}})],1),a("el-descriptions-item",{attrs:{label:"创建时间"}},[e._v(e._s(e.parseTime(e.viewForm.createTime)))]),a("el-descriptions-item",{attrs:{label:"完成时间"}},[e._v(e._s(e.parseTime(e.viewForm.completedTime)))]),a("el-descriptions-item",{attrs:{label:"领取时间"}},[e._v(e._s(e.parseTime(e.viewForm.claimedTime)))]),a("el-descriptions-item",{attrs:{label:"过期时间"}},[e._v(e._s(e.parseTime(e.viewForm.expiredTime)))])],1)],1),a("el-dialog",{attrs:{title:"刷新任务",visible:e.refreshOpen,width:"500px","append-to-body":""},on:{"update:visible":function(t){e.refreshOpen=t}}},[a("el-form",{ref:"refreshForm",attrs:{model:e.refreshForm,rules:e.refreshRules,"label-width":"100px"}},[a("el-form-item",{attrs:{label:"任务类型",prop:"taskType"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择要刷新的任务类型"},model:{value:e.refreshForm.taskType,callback:function(t){e.$set(e.refreshForm,"taskType",t)},expression:"refreshForm.taskType"}},[a("el-option",{attrs:{label:"全部类型",value:""}}),e._l(e.dict.type.task_type,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})}))],2)],1),a("el-form-item",{attrs:{label:"用户范围",prop:"userScope"}},[a("el-radio-group",{model:{value:e.refreshForm.userScope,callback:function(t){e.$set(e.refreshForm,"userScope",t)},expression:"refreshForm.userScope"}},[a("el-radio",{attrs:{label:"all"}},[e._v("所有用户")]),a("el-radio",{attrs:{label:"specific"}},[e._v("指定用户")])],1)],1),"specific"===e.refreshForm.userScope?a("el-form-item",{attrs:{label:"用户ID",prop:"userId"}},[a("el-input",{attrs:{placeholder:"请输入用户ID"},model:{value:e.refreshForm.userId,callback:function(t){e.$set(e.refreshForm,"userId",t)},expression:"refreshForm.userId"}})],1):e._e()],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.submitRefresh}},[e._v("确 定")]),a("el-button",{on:{click:function(t){e.refreshOpen=!1}}},[e._v("取 消")])],1)],1)],1)},s=[],l=a("5530"),i=(a("d81d"),a("d3b7"),a("0643"),a("a573"),a("b775"));function n(e){return Object(i["a"])({url:"/operation/user-task/list",method:"get",params:e})}function o(e){return Object(i["a"])({url:"/operation/user-task/"+e,method:"get"})}function u(e,t){return Object(i["a"])({url:"/operation/user-task/claim",method:"post",data:{userId:e,taskCode:t}})}function c(e){return Object(i["a"])({url:"/operation/user-task/refresh",method:"post",data:e})}var p={name:"UserTask",dicts:["task_type","task_status","task_event_type"],data:function(){return{loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,userTaskList:[],viewOpen:!1,refreshOpen:!1,dateRange:[],queryParams:{pageNum:1,pageSize:10,userId:null,userName:null,taskCode:null,taskType:null,status:null,beginTime:null,endTime:null},viewForm:{},refreshForm:{taskType:"",userScope:"all",userId:null},refreshRules:{userScope:[{required:!0,message:"用户范围不能为空",trigger:"change"}],userId:[{required:!0,message:"用户ID不能为空",trigger:"blur"}]}}},watch:{dateRange:function(e){null===e?(this.queryParams.beginTime=null,this.queryParams.endTime=null):(this.queryParams.beginTime=e[0],this.queryParams.endTime=e[1])}},created:function(){this.getList()},methods:{getList:function(){var e=this;this.loading=!0,n(this.queryParams).then((function(t){e.userTaskList=t.rows,e.total=t.total,e.loading=!1}))},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.dateRange=[],this.resetForm("queryForm"),this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.id})),this.single=1!==e.length,this.multiple=!e.length},handleView:function(e){var t=this;o(e.id).then((function(e){t.viewForm=e.data,t.viewOpen=!0}))},handleClaim:function(e){var t=this;this.$modal.confirm('确认要领取"'+e.taskName+'"的任务奖励吗？').then((function(){return u(e.userId,e.taskCode)})).then((function(){t.getList(),t.$modal.msgSuccess("领取成功")})).catch((function(){}))},handleRefreshTask:function(){this.refreshForm={taskType:"",userScope:"all",userId:null},this.refreshOpen=!0},submitRefresh:function(){var e=this;this.$refs["refreshForm"].validate((function(t){if(t){var a={taskType:e.refreshForm.taskType||null,userId:"specific"===e.refreshForm.userScope?e.refreshForm.userId:null};c(a).then((function(t){e.$modal.msgSuccess("刷新成功"),e.refreshOpen=!1,e.getList()}))}}))},handleExport:function(){this.download("operation/user-task/export",Object(l["a"])({},this.queryParams),"user_task_".concat((new Date).getTime(),".xlsx"))}}},d=p,m=(a("87a4"),a("2877")),h=Object(m["a"])(d,r,s,!1,null,"00d04226",null);t["default"]=h.exports},"5f18":function(e,t,a){},"87a4":function(e,t,a){"use strict";a("5f18")}}]);