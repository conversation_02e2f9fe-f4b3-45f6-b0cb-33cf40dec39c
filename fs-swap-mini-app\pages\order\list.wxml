<view class="order-list-container">

  <!-- 标签页 -->
  <van-tabs active="{{ activeTab }}" bind:change="onTabChange" sticky animated swipeable>
    <van-tab title="全部"></van-tab>
    <van-tab title="待联系"></van-tab>
    <van-tab title="待确认"></van-tab>
    <van-tab title="已完成"></van-tab>
    <van-tab title="已取消"></van-tab>
    <van-tab title="已终止"></van-tab>
  </van-tabs>

  <!-- 订单列表 -->
  <view class="order-list">
    <block wx:if="{{orderList.length > 0}}">
      <view class="order-item" wx:for="{{orderList}}" wx:key="orderId" bindtap="onOrderDetail" data-id="{{item.orderId}}">
        <!-- 订单头部 -->
        <view class="order-header">
          <view class="order-no">订单号: {{item.orderNo}}</view>
          <view class="order-status">{{statusMap[item.orderStatus]}}</view>
        </view>

        <!-- 商品信息 -->
        <view class="product-info">
          <image class="product-image" src="{{item.productImageUrl || '/static/img/default_product.png'}}" mode="aspectFill" lazy-load="{{true}}"></image>
          <view class="product-detail">
            <view class="product-title">{{item.productDescription}}</view>
            <!-- 只有非社区互助类型的订单才显示价格 -->
            <view class="product-price" wx:if="{{item.orderType !== 'COMMUNITY_HELP'}}">
              <text wx:if="{{item.productPrice == 0}}">免费送</text>
              <text wx:else>¥{{item.productPrice}}</text>
            </view>
          </view>
        </view>

        <!-- 订单底部 -->
        <view class="order-footer">
          <view class="order-time">{{item.createTime}}</view>
          <view class="order-actions">
            <!-- 买家操作按钮 -->
            <block wx:if="{{roleType === 'buyer'}}">
              <!-- 待联系卖家状态 -->
              <block wx:if="{{item.orderStatus === '1'}}">
                <button class="action-button cancel" catchtap="onCancelOrder" data-id="{{item.orderId}}">取消订单</button>
                <button class="action-button primary" catchtap="onContactOrder" data-id="{{item.orderId}}">确认已联系</button>
              </block>

              <!-- 待双方确认状态 -->
              <block wx:if="{{item.orderStatus === '2'}}">
                <button class="action-button danger" catchtap="onTerminateOrder" data-id="{{item.orderId}}">终止订单</button>
                <button class="action-button primary" catchtap="onFinishOrder" data-id="{{item.orderId}}">完成订单</button>
              </block>

              <block wx:elif="{{item.orderStatus === '3'}}">
                <button class="action-button disabled">已完成</button>
              </block>

              <block wx:elif="{{item.orderStatus === '4'}}">
                <button class="action-button disabled">已取消</button>
              </block>

              <block wx:elif="{{item.orderStatus === '8'}}">
                <button class="action-button disabled">已终止</button>
              </block>
            </block>

            <!-- 卖家操作按钮 -->
            <block wx:if="{{roleType === 'seller'}}">
              <!-- 待双方确认状态 -->
              <block wx:if="{{item.orderStatus === '2'}}">
                <button class="action-button danger" catchtap="onTerminateOrder" data-id="{{item.orderId}}">终止订单</button>
                <button class="action-button primary" catchtap="onFinishOrder" data-id="{{item.orderId}}">完成订单</button>
              </block>
              <block wx:elif="{{item.orderStatus === '3'}}">
                <button class="action-button disabled">已完成</button>
              </block>
              <block wx:elif="{{item.orderStatus === '8'}}">
                <button class="action-button disabled">已终止</button>
              </block>
            </block>
          </view>
        </view>
      </view>
    </block>

    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{orderList.length === 0 && !loading}}">
      <image class="empty-icon" src="/static/img/empty_order.png" mode="aspectFit"></image>
      <text class="empty-text">暂无订单</text>
    </view>

    <!-- 加载状态 -->
    <view class="loading-state" wx:if="{{loading && orderList.length === 0}}">
      <van-loading size="24px" color="#1989fa">加载中...</van-loading>
    </view>

    <!-- 加载更多 -->
    <view class="load-more" wx:if="{{orderList.length > 0}}">
      <text wx:if="{{hasMore && !loading}}">上拉加载更多</text>
      <van-loading size="20px" wx:if="{{loading}}">加载中...</van-loading>
      <text wx:if="{{!hasMore && orderList.length > 0}}">没有更多了</text>
    </view>
  </view>

  <!-- 操作确认弹框 -->
  <van-dialog
    use-slot
    title="操作确认"
    show="{{ showConfirmDialog }}"
    show-cancel-button
    confirm-button-text="确定"
    cancel-button-text="取消"
    bind:confirm="confirmAction"
    bind:cancel="cancelAction"
    confirm-button-color="#07c160"
  >
    <view class="dialog-content">
      <text>{{ confirmText }}</text>
    </view>
  </van-dialog>

</view>
