<view class="container">
  <view class="residential-select-container">
    <view class="residential-select-header">
      <view class="residential-select-title">{{residential ? '确认选择小区' : '请选择您的小区'}}</view>
      <view class="residential-select-subtitle">{{residential ? '确认选择后即可使用应用功能' : '选择小区后才能浏览内容'}}</view>
    </view>

    <!-- 加载状态 -->
    <view class="loading-container" wx:if="{{loading}}">
      <view class="loading-spinner"></view>
      <view class="loading-text">正在获取附近小区...</view>
    </view>

    <!-- 地图区域 -->
    <view class="map-container" wx:if="{{!loading && residential && markers.length > 0}}">
      <map
        id="residentialMap"
        class="map"
        longitude="{{markers[0].longitude}}"
        latitude="{{markers[0].latitude}}"
        markers="{{markers}}"
        scale="16"
        show-location
        show-compass
        enable-3D
      ></map>
    </view>

    <!-- 地图加载失败时的备用显示 -->
    <view class="map-fallback" wx:if="{{!loading && residential && (!markers || markers.length === 0)}}">
      <van-icon name="location-o" size="80rpx" color="#4361ee" />
      <view class="map-fallback-text">小区位置信息</view>
      <view class="map-fallback-subtext">地图暂时无法显示，但可以正常选择小区</view>
    </view>

    <!-- 错误提示 -->
    <view class="error-container" wx:if="{{error}}">
      <van-icon name="warning-o" size="40rpx" color="#ff4d4f" />
      <view class="error-text">{{errorMessage}}</view>
    </view>

    <!-- 小区信息 -->
    <view class="residential-info" wx:if="{{!loading && residential}}">
      <view class="residential-name">{{residential.name}}</view>
      <view class="residential-address">
        <van-icon name="location-o" size="30rpx" color="#4361ee" class="address-icon" />
        <view class="address-text">{{residential.address}}</view>
      </view>
      <!-- 距离信息 -->
      <view class="residential-distance" wx:if="{{distanceText}}">
        <van-icon name="guide-o" size="24rpx" color="#999" class="distance-icon" />
        <view class="distance-text">距离您约 {{distanceText}}</view>
      </view>
    </view>

    <!-- 按钮区域 -->
    <view class="residential-select-footer">
      <button class="btn-cancel {{loading ? 'btn-cancel-disabled' : ''}}" bindtap="onCancel">{{residential ? '重新选择' : '选择其他小区'}}</button>
      <button class="btn-confirm {{loading || !residential ? 'btn-confirm-disabled' : ''}}" bindtap="onConfirm">确认选择</button>
    </view>
  </view>
</view> 