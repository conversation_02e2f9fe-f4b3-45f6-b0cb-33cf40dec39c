.container {
  min-height: 100vh;
  background-color: #f5f7fa;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40rpx 0;
}

.residential-select-container {
  width: 85%;
  max-width: 640rpx;
  background-color: #fff;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 16rpx 40rpx rgba(0, 0, 0, 0.2);
  margin: 0 auto;
}

.residential-select-header {
  position: relative;
  padding: 36rpx 30rpx;
  text-align: center;
  border-bottom: 1rpx solid #f0f0f0;
  background-color: #ffffff;
  min-height: 120rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.residential-select-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 8rpx;
  line-height: 1.4;
  display: block;
  word-break: break-all;
  white-space: normal;
}

.residential-select-subtitle {
  font-size: 26rpx;
  color: #666666;
  line-height: 1.4;
  display: block;
  word-break: break-all;
  white-space: normal;
}

/* 加载状态样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 0;
  min-height: 360rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid rgba(67, 97, 238, 0.1);
  border-left-color: #4361ee;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.loading-text {
  font-size: 28rpx;
  color: #666666;
  display: block;
}

/* 错误提示样式 */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 30rpx;
  min-height: 200rpx;
  background-color: #fff1f0;
}

.error-text {
  font-size: 28rpx;
  color: #ff4d4f;
  margin-top: 16rpx;
  text-align: center;
  line-height: 1.5;
  display: block;
}

.map-container {
  width: 100%;
  height: 360rpx;
  overflow: hidden;
}

.map {
  width: 100%;
  height: 100%;
}

/* 地图备用显示样式 */
.map-fallback {
  width: 100%;
  height: 360rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #f9fafc;
  border: 2rpx dashed #e0e6f0;
}

.map-fallback-text {
  font-size: 32rpx;
  color: #333333;
  margin-top: 20rpx;
  font-weight: 500;
  display: block;
}

.map-fallback-subtext {
  font-size: 26rpx;
  color: #666666;
  margin-top: 12rpx;
  text-align: center;
  line-height: 1.4;
  display: block;
}

.residential-info {
  padding: 36rpx;
  border-bottom: 1rpx solid #f0f0f0;
  background-color: #f9fafc;
}

.residential-name {
  font-size: 34rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 20rpx;
  display: block;
}

.residential-address {
  display: flex;
  align-items: flex-start;
  font-size: 28rpx;
  color: #666666;
  line-height: 1.5;
}

.address-icon {
  margin-right: 10rpx;
  color: #4361ee;
  flex-shrink: 0;
}

.address-text {
  flex: 1;
  display: block;
}

/* 距离信息样式 */
.residential-distance {
  display: flex;
  align-items: center;
  font-size: 26rpx;
  color: #999999;
  margin-top: 16rpx;
}

.distance-icon {
  margin-right: 8rpx;
  flex-shrink: 0;
}

.distance-text {
  color: #999999;
  display: block;
}

.residential-select-footer {
  display: flex;
  padding: 36rpx;
  gap: 24rpx;
}

.btn-cancel, .btn-confirm {
  flex: 1;
  height: 88rpx;
  line-height: 88rpx;
  text-align: center;
  border-radius: 44rpx;
  font-size: 30rpx;
  font-weight: 500;
  transition: all 0.2s ease;
}

.btn-cancel {
  background-color: #f5f7fa;
  color: #666;
  border: 1rpx solid #e5e7eb;
}

.btn-confirm {
  background: linear-gradient(to right, #4361ee, #4cc9f0);
  color: #fff;
  box-shadow: 0 8rpx 20rpx rgba(67, 97, 238, 0.25);
}

.btn-cancel:active, .btn-confirm:active {
  opacity: 0.8;
}

/* 禁用按钮样式 */
.btn-cancel-disabled, .btn-confirm-disabled {
  opacity: 0.5;
  pointer-events: none;
} 