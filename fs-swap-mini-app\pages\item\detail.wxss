.product-detail-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  min-height: 100vh;
  padding-bottom: 120rpx; /* 为底部操作区留出空间 */
}

/* 状态栏占位 */
.status-bar-placeholder {
  width: 100%;
}

/* 自定义导航栏 */
.custom-nav-bar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: #ffffff;
  z-index: 9999;
  width: 100%;
  box-sizing: border-box;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.08);
}

.custom-nav-content {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0 30rpx;
  box-sizing: border-box;
}

.nav-left {
  flex: 1;
  overflow: hidden;
  display: flex;
  align-items: center;
  height: 100%;
}

/* 返回按钮 */
.back-button {
  width: 32px;
  height: 32px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  margin-right: 8px;
  transition: all 0.2s;
}

.back-button:active {
  opacity: 0.6;
}

.button-hover {
  opacity: 0.8;
  transform: scale(0.98);
}

/* 导航栏卖家信息 */
.nav-seller-info {
  display: flex;
  align-items: center;
  margin-left: 0;
  max-width: calc(100% - 40px);
  border-radius: 0;
  padding: 0;
  transition: all 0.3s;
  position: relative;
}

.nav-seller-info:active {
  opacity: 0.7;
}

.seller-hover {
  opacity: 0.8;
  transform: scale(0.98);
}

.nav-seller-avatar {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  margin-right: 8px;
  border: none;
}

.seller-info-text {
  display: flex;
  flex-direction: column;
  justify-content: center;
  margin-right: 6px;
  flex: 1;
}

.nav-seller-name {
  font-size: 14px;
  color: #333333;
  font-weight: 500;
  max-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.nav-seller-location {
  font-size: 12px;
  color: #999999;
  max-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.nav-seller-tag {
  font-size: 11px;
  color: #ffffff;
  background-color: #07c160;
  padding: 1px 6px;
  border-radius: 2px;
  white-space: nowrap;
  box-shadow: none;
  margin-right: 4px;
}

.nav-seller-arrow {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 2px;
}

/* 轮播图区域 */
.product-images {
  width: 100%;
  height: 750rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  will-change: transform;
}

.swiper-item {
  overflow: hidden;
  transform: translate3d(0, 0, 0);
  will-change: transform;
  transition: none;
  width: 100% !important;
  display: flex;
  justify-content: center;
  align-items: center;
}

.product-image {
  width: 100%;
  height: 100%;
  will-change: transform;
  transform: translate3d(0, 0, 0);
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
  display: block;
}

.product-image:active {
  transform: scale(0.98);
}

/* 图片计数器 */
.image-counter {
  position: absolute;
  right: 30rpx;
  bottom: 30rpx;
  background: rgba(0, 0, 0, 0.5);
  color: #ffffff;
  font-size: 24rpx;
  padding: 6rpx 20rpx;
  border-radius: 24rpx;
  z-index: 90;
  backdrop-filter: blur(5px);
}

/* 轮播容器 */
.swiper-container {
  position: relative;
  width: 100%;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 内容包装器 */
.content-wrapper {
  position: relative;
  margin-top: 0;
  border-radius: 0;
  background-color: #ffffff;
  overflow: hidden;
  box-shadow: 0 -10rpx 20rpx rgba(0, 0, 0, 0.05);
  padding: 30rpx;
  border-top: 1rpx solid rgba(255, 255, 255, 0.5);
}

/* 价格和简略信息一行布局 */
.price-info-row {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20rpx;
  width: 100%;
}

/* 价格区域 */
.price-container {
  display: flex;
  align-items: baseline;
}

.price-symbol {
  font-size: 36rpx;
  color: #ff4d4f;
  font-weight: bold;
}

.price-value {
  font-size: 60rpx;
  color: #ff4d4f;
  font-weight: bold;
  margin-left: 4rpx;
  line-height: 1;
  font-family: Arial, sans-serif;
}

.price-free {
  font-size: 36rpx;
  color: #ff4d4f;
  font-weight: bold;
  line-height: 1;
  font-family: Arial, sans-serif;
}

.shipping-tag {
  font-size: 24rpx;
  color: #ff6723;
  margin-left: 10rpx;
  align-self: center;
  font-weight: 500;
}

/* 右侧信息区域 */
.right-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  justify-content: center;
  height: 100%;
}

.usage-tag {
  padding: 4rpx 12rpx;
  font-size: 36rpx;
  font-weight: 800;
  background-color: transparent;
  color: #000000;
  border: none;
  margin-bottom: 10rpx;
  box-shadow: none;
  letter-spacing: 2rpx;
  line-height: 1.2;
  display: inline-block;
  align-items: center;
  justify-content: center;
  font-style: italic;
  position: relative;
  z-index: 1;
}

.usage-tag::after {
  content: '';
  position: absolute;
  left: 0;
  bottom: 0;
  width: 90%;
  height: 33%;
  background-color: #ffde00;
  z-index: -1;
  transform: skewX(-12deg);
  transform-origin: bottom left;
}

.brief-stats {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  justify-content: center;
}

.view-count {
  color: #888888;
  font-size: 24rpx;
  opacity: 0.8;
  letter-spacing: 1rpx;
  line-height: 1;
  display: flex;
  align-items: center;
}

/* 商品简略信息 */
.product-brief-info {
  display: flex;
  font-size: 26rpx;
  color: #999999;
  margin-bottom: 20rpx;
  align-items: center;
}

.brief-item {
  margin-right: 12rpx;
}

.brief-separator {
  width: 1px;
  height: 24rpx;
  background-color: #e5e5e5;
  margin: 0 12rpx;
}

.brief-item.shipping {
  color: #ff6723;
}

.brief-item.views, .brief-item.wants {
  color: #999999;
}

/* 商品描述 */
.product-description {
  font-size: 28rpx;
  color: #444444;
  line-height: 1.8;
  margin-bottom: 20rpx;
  text-align: justify;
}

/* 商品型号信息 */
.model-info {
  font-size: 28rpx;
  color: #444444;
  line-height: 1.8;
  margin-top: 10rpx;
}

/* 商品状态信息 */
.status-info {
  font-size: 28rpx;
  color: #444444;
  line-height: 1.8;
  margin-top: 10rpx;
}

/* 优惠信息 */
.offer-info {
  margin-top: 20rpx;
}

.offer-item {
  font-size: 28rpx;
  color: #444444;
  line-height: 1.8;
}

/* 转让原因 */
.reason-info {
  display: flex;
  flex-direction: column;
  font-size: 28rpx;
  color: #444444;
  line-height: 1.8;
  margin-top: 20rpx;
}

/* 商品信息 */
.product-info {
  padding: 30rpx;
  background-color: #ffffff;
  border-bottom: 1rpx solid #f2f2f2;
}

.product-meta {
  display: flex;
  font-size: 24rpx;
  color: #999999;
}

.meta-item {
  display: flex;
  align-items: center;
  margin-right: 30rpx;
}

.meta-item van-icon {
  margin-right: 8rpx;
}

.meta-item.location {
  color: #1890ff;
}

/* 卖家信息卡片 */
.seller-card {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  background-color: #ffffff;
  border-bottom: 1rpx solid #f2f2f2;
  margin-top: 20rpx;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
  margin-left: 24rpx;
  margin-right: 24rpx;
  transition: all 0.3s;
  position: relative;
  overflow: hidden;
}

.seller-card::after {
  content: '';
  position: absolute;
  right: 0;
  top: 0;
  height: 100%;
  width: 24rpx;
  background: linear-gradient(to left, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0));
  pointer-events: none;
}

.card-hover {
  background-color: #f9f9f9;
  transform: translateY(-4rpx);
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.1);
}

.seller-info-brief {
  display: flex;
  align-items: center;
}

.seller-credit, .seller-activity, .seller-goods {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0 30rpx;
}

.seller-credit:not(:last-child), .seller-activity:not(:last-child) {
  border-right: 1rpx solid #eeeeee;
}

.seller-credit text, .seller-activity text, .seller-goods text {
  font-size: 24rpx;
  color: #999999;
  margin-bottom: 8rpx;
}

.credit-score {
  font-size: 30rpx;
  color: #ff4d4f;
  font-weight: bold;
}

.activity-tag {
  font-size: 26rpx;
  color: #52c41a;
  font-weight: 500;
}

.goods-count {
  font-size: 26rpx;
  color: #1890ff;
  font-weight: 500;
}

/* 通用区域样式 */
.product-section {
  margin: 24rpx 24rpx 0;
  padding: 30rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.product-section:active {
  background-color: #fcfcfc;
  transform: translateY(-2rpx);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #222222;
  margin-bottom: 24rpx;
  display: flex;
  align-items: center;
}

.title-icon {
  width: 8rpx;
  height: 36rpx;
  background: linear-gradient(to bottom, #ff4d4f, #ff7a29);
  border-radius: 4rpx;
  margin-right: 16rpx;
}

/* 商品详情 */
.detail-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-gap: 36rpx 20rpx;
}

.detail-item {
  display: flex;
  flex-direction: column;
}

.detail-label {
  font-size: 26rpx;
  color: #999999;
  margin-bottom: 10rpx;
}

.detail-value {
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
}

/* 底部操作区 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 110rpx;
  background-color: rgba(255, 255, 255, 0.95);
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.08);
  z-index: 99;
  padding: 0 24rpx;
  border-top: 1rpx solid #f5f5f5;
  backdrop-filter: blur(20px);
}

.action-group {
  display: flex;
  align-items: center;
}

.action-group.left {
  width: 50%;
}

.action-group.right {
  width: 50%;
  justify-content: flex-end;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 0 20rpx;
  height: 100%;
  position: relative;
}

.action-item:active {
  opacity: 0.8;
}

.action-hover {
  opacity: 0.7;
  transform: scale(0.95);
}

.action-item text {
  font-size: 20rpx;
  color: #999999;
  margin-top: 6rpx;
}

/* 分享按钮样式 */
.share-button {
  position: relative;
  overflow: hidden;
  animation: pulse 2s infinite;
  background: none;
  border: none;
  padding: 0;
  margin: 0;
  line-height: normal;
  border-radius: 0;
  font-size: inherit;
  text-align: center;
  color: inherit;
  box-sizing: border-box;
}

/* 移除button的默认边框 */
.share-button::after {
  border: none;
}

.share-text {
  color: #1989fa !important;
  font-weight: 500;
}

.action-button {
  height: 80rpx;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 20rpx;
  font-size: 28rpx;
  font-weight: 500;
  padding: 0 36rpx;
  transition: all 0.2s;
  width: 180rpx;
}

.action-button:active {
  transform: scale(0.97);
}

.action-button van-icon {
  margin-right: 10rpx;
}

.action-button.buy {
  background-color: #ff4d4f;
  color: #ffffff;
  width: 180rpx;
  min-width: 180rpx;
  box-shadow: 0 6rpx 16rpx rgba(255, 77, 79, 0.35);
  font-size: 28rpx;
  font-weight: 500;
}

.action-button.buy:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(255, 77, 79, 0.2);
}

.action-button.edit {
  background-color: #1989fa;
  color: #ffffff;
  min-width: 140rpx;
  width: 140rpx;
  box-shadow: 0 6rpx 16rpx rgba(25, 137, 250, 0.35);
  font-size: 28rpx;
  font-weight: 500;
}

.action-button.edit:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(25, 137, 250, 0.2);
}

.action-button.offline {
  background-color: #ff6b35;
  color: #ffffff;
  min-width: 140rpx;
  width: 140rpx;
  box-shadow: 0 6rpx 16rpx rgba(255, 107, 53, 0.35);
  font-size: 28rpx;
  font-weight: 500;
}

.action-button.offline:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(255, 107, 53, 0.2);
}

/* 重新上架按钮样式 */
.action-button.online {
  background-color: #13c2c2;
  color: #ffffff;
  min-width: 140rpx;
  width: 140rpx;
  box-shadow: 0 6rpx 16rpx rgba(19, 194, 194, 0.35);
  font-size: 28rpx;
  font-weight: 500;
}

.action-button.online:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(19, 194, 194, 0.2);
}

/* 按钮组样式 */
.action-button-group {
  display: flex;
  align-items: center;
  gap: 16rpx;
  justify-content: flex-end;
  width: 100%;
}

.action-button.chat {
  background-color: #52c41a;
  color: #ffffff;
  width: 160rpx;
  min-width: 160rpx;
  box-shadow: 0 6rpx 16rpx rgba(82, 196, 26, 0.35);
  font-size: 28rpx;
  font-weight: 500;
}

.action-button.chat:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(82, 196, 26, 0.2);
}

.card-action {
  display: flex;
  align-items: center;
  padding: 8rpx 16rpx;
  background-color: rgba(0, 0, 0, 0.02);
  border-radius: 20rpx;
  transition: all 0.3s;
}

.card-action text {
  font-size: 24rpx;
  color: #999999;
  margin-right: 6rpx;
}

/* 购买弹窗样式 */
.buy-dialog-content {
  padding: 30rpx;
}

.buy-dialog-item {
  display: flex;
  margin-bottom: 20rpx;
  align-items: center;
}

.buy-dialog-label {
  font-size: 28rpx;
  color: #666666;
  width: 120rpx;
}

.buy-dialog-value {
  font-size: 28rpx;
  color: #333333;
  flex: 1;
  font-weight: 500;
}

.buy-dialog-notice {
  margin-top: 10rpx;
  margin-bottom: 20rpx;
  font-size: 22rpx;
  color: #ff4d4f;
  text-align: left;
  padding-left: 0;
}



/* 下架确认弹窗样式 */
.offline-dialog-content {
  padding: 30rpx;
  text-align: center;
  font-size: 28rpx;
  color: #333333;
  line-height: 1.6;
}

/* 重新上架确认弹窗样式 */
.online-dialog-content {
  padding: 30rpx;
  text-align: center;
  font-size: 28rpx;
  color: #333333;
  line-height: 1.6;
}

/* 分享按钮脉冲动画 */
@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.original-price {
  text-decoration: line-through;
  color: #999999;
  margin-right: 8rpx;
  font-size: 24rpx;
}

/* 购买结果弹窗样式 */
.buy-result-content {
  padding: 48rpx 32rpx 40rpx;
  text-align: center;
  background-color: #ffffff;
}

.success-icon {
  margin-bottom: 48rpx;
  display: flex;
  justify-content: center;
  animation: success-bounce 0.6s ease-out;
}

@keyframes success-bounce {
  0% {
    transform: scale(0.3);
    opacity: 0;
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.result-message {
  margin-bottom: 48rpx;
  padding: 0 16rpx;
}

.message-text {
  font-size: 30rpx;
  color: #333333;
  line-height: 1.6;
  display: block;
  text-align: center;
  font-weight: 400;
  letter-spacing: 0.5rpx;
  word-break: break-all;
  white-space: pre-wrap;
}

.contact-tip {
  background: linear-gradient(135deg, #f0f4ff 0%, #e8f2ff 100%);
  border-radius: 24rpx;
  padding: 32rpx 24rpx;
  margin: 0 16rpx;
  border: 1rpx solid #d6e4ff;
  box-shadow: 0 8rpx 24rpx rgba(25, 137, 250, 0.12);
  position: relative;
  overflow: hidden;
}

.contact-tip::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4rpx;
  background: linear-gradient(90deg, #1989fa 0%, #40a9ff 100%);
}

.tip-item {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  margin: 0;
  padding: 0;
}

.tip-text {
  font-size: 28rpx;
  color: #1989fa;
  line-height: 1.4;
  font-weight: 500;
  text-align: center;
}

/* 购买结果弹窗中的联系方式样式 */
.contact-section {
  margin-top: 32rpx;
  padding-top: 24rpx;
  border-top: 1px solid #f5f5f5;
}

.contact-button-container {
  display: flex;
  justify-content: center;
  margin-top: 16rpx;
}

.contact-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  padding: 16rpx 32rpx;
  background-color: #1989fa;
  color: #ffffff;
  border: none;
  border-radius: 8rpx;
  font-size: 26rpx;
  transition: background-color 0.2s;
}

.contact-button:active {
  background-color: #1976d2;
}

.contact-button::after {
  border: none;
}
