(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-3012529b"],{"0f6f":function(e,t,a){"use strict";a("3350")},"2c65":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",attrs:{model:e.queryParams,size:"small",inline:!0,"label-width":"80px"}},[a("el-form-item",{attrs:{label:"名称",prop:"name"}},[a("el-input",{attrs:{placeholder:"请输入名称",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.name,callback:function(t){e.$set(e.queryParams,"name",t)},expression:"queryParams.name"}})],1),a("area-chain-selector",{ref:"queryAreaSelector",attrs:{regionId:e.queryParams.regionId,communityId:e.queryParams.communityId,residentialId:e.queryParams.residentialId,labels:{region:"行政区域",community:"社区",residential:"小区"},showCommunity:!0,showResidential:!0,inlineLayout:!0},on:{"update:regionId":function(t){return e.queryParams.regionId=t},"update:communityId":function(t){return e.queryParams.communityId=t},"update:residentialId":function(t){return e.queryParams.residentialId=t},"community-change":e.handleQuery,"region-change":e.handleRegionChange}}),a("el-form-item",{attrs:{label:"地址",prop:"address"}},[a("el-input",{attrs:{placeholder:"请输入地址",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.address,callback:function(t){e.$set(e.queryParams,"address",t)},expression:"queryParams.address"}})],1),a("el-form-item",{attrs:{label:"分类",prop:"category"}},[a("el-select",{attrs:{placeholder:"请选择分类",clearable:""},model:{value:e.queryParams.category,callback:function(t){e.$set(e.queryParams,"category",t)},expression:"queryParams.category"}},e._l(e.dict.type.community_service_category,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",{attrs:{label:"标签",prop:"tags"}},[a("el-select",{attrs:{placeholder:"请选择标签",clearable:""},model:{value:e.queryParams.tags,callback:function(t){e.$set(e.queryParams,"tags",t)},expression:"queryParams.tags"}},e._l(e.dict.type.community_service_tag,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",{attrs:{label:"推荐状态",prop:"isRecommended"}},[a("el-select",{attrs:{placeholder:"请选择推荐状态",clearable:""},model:{value:e.queryParams.isRecommended,callback:function(t){e.$set(e.queryParams,"isRecommended",t)},expression:"queryParams.isRecommended"}},e._l(e.dict.type.sys_yes_no_num,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",{attrs:{label:"审核状态",prop:"auditStatus"}},[a("el-select",{attrs:{placeholder:"请选择审核状态",clearable:""},model:{value:e.queryParams.auditStatus,callback:function(t){e.$set(e.queryParams,"auditStatus",t)},expression:"queryParams.auditStatus"}},e._l(e.dict.type.common_audit_status,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",{attrs:{label:"状态",prop:"status"}},[a("el-select",{attrs:{placeholder:"请选择状态",clearable:""},model:{value:e.queryParams.status,callback:function(t){e.$set(e.queryParams,"status",t)},expression:"queryParams.status"}},e._l(e.dict.type.business_normal_status,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),a("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1),a("el-row",{staticClass:"mb8",attrs:{gutter:10}},[a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["operation:nearby:add"],expression:"['operation:nearby:add']"}],attrs:{type:"primary",plain:"",icon:"el-icon-plus",size:"mini"},on:{click:e.handleAdd}},[e._v("新增")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["operation:nearby:edit"],expression:"['operation:nearby:edit']"}],attrs:{type:"success",plain:"",icon:"el-icon-edit",size:"mini",disabled:e.single},on:{click:e.handleUpdate}},[e._v("修改")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["operation:nearby:audit"],expression:"['operation:nearby:audit']"}],attrs:{type:"info",plain:"",icon:"el-icon-check",size:"mini",disabled:e.multiple},on:{click:e.handleAudit}},[e._v("审核")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["operation:nearby:remove"],expression:"['operation:nearby:remove']"}],attrs:{type:"danger",plain:"",icon:"el-icon-delete",size:"mini",disabled:e.multiple},on:{click:e.handleDelete}},[e._v("删除")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["operation:nearby:export"],expression:"['operation:nearby:export']"}],attrs:{type:"warning",plain:"",icon:"el-icon-download",size:"mini"},on:{click:e.handleExport}},[e._v("导出")])],1),a("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.nearbyList},on:{"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"55",align:"center"}}),a("el-table-column",{attrs:{label:"编号",align:"center",prop:"id",width:"80"}}),a("el-table-column",{attrs:{label:"名称",align:"center",prop:"name",width:"150"}}),a("el-table-column",{attrs:{label:"地址",align:"center",prop:"address",width:"200","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{label:"分类",align:"center",prop:"category",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("dict-tag",{attrs:{options:e.dict.type.community_service_category,value:t.row.category}})]}}])}),a("el-table-column",{attrs:{label:"标签",align:"center",prop:"tags",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("dict-tag",{attrs:{options:e.dict.type.community_service_tag,value:t.row.tags}})]}}])}),a("el-table-column",{attrs:{label:"图片",align:"center",prop:"images",width:"200"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",{staticClass:"image-list"},[t.row.images?e._l(t.row.images.split(","),(function(t,r){return a("image-preview",{key:r,staticStyle:{margin:"2px"},attrs:{src:e.filePrefix+t,width:50,height:50}})})):e._e()],2)]}}])}),a("el-table-column",{attrs:{label:"联系电话",align:"center",prop:"contactPhone",width:"130"}}),a("el-table-column",{attrs:{label:"营业时间",align:"center",prop:"businessHours",width:"150","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{label:"行政区域",align:"center",prop:"regionId",formatter:e.regionFormatter,width:"120"}}),a("el-table-column",{attrs:{label:"社区",align:"center",prop:"communityId",formatter:e.communityFormatter,width:"120"}}),a("el-table-column",{attrs:{label:"小区",align:"center",prop:"residentialId",formatter:e.residentialFormatter,width:"120"}}),a("el-table-column",{attrs:{label:"推荐",align:"center",prop:"isRecommended",width:"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("dict-tag",{attrs:{options:e.dict.type.sys_yes_no_num,value:t.row.isRecommended}})]}}])}),a("el-table-column",{attrs:{label:"官方发布",align:"center",prop:"isOfficial",width:"90"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("dict-tag",{attrs:{options:e.dict.type.sys_yes_no_num,value:t.row.isOfficial}})]}}])}),a("el-table-column",{attrs:{label:"审核状态",align:"center",prop:"auditStatus",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("dict-tag",{attrs:{options:e.dict.type.common_audit_status,value:t.row.auditStatus}})]}}])}),a("el-table-column",{attrs:{label:"审核时间",align:"center",prop:"auditTime",width:"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(e.parseTime(t.row.auditTime,"{y}-{m}-{d}")))])]}}])}),a("el-table-column",{attrs:{label:"状态",align:"center",prop:"status",width:"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("dict-tag",{attrs:{options:e.dict.type.business_normal_status,value:t.row.status}})]}}])}),a("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width",width:"200"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["operation:nearby:edit"],expression:"['operation:nearby:edit']"}],attrs:{size:"mini",type:"text",icon:"el-icon-edit"},on:{click:function(a){return e.handleUpdate(t.row)}}},[e._v("修改")]),0==t.row.auditStatus?a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["operation:nearby:audit"],expression:"['operation:nearby:audit']"}],staticClass:"audit-btn-pending",attrs:{size:"mini",type:"text",icon:"el-icon-check"},on:{click:function(a){return e.handleAudit(t.row)}}},[e._v("审核")]):e._e(),1==t.row.auditStatus?a("el-tooltip",{attrs:{content:"已审核通过",placement:"top"}},[a("el-button",{staticClass:"audit-btn-approved",attrs:{size:"mini",type:"text",icon:"el-icon-circle-check",disabled:""}},[e._v("已通过")])],1):e._e(),2==t.row.auditStatus?a("el-tooltip",{attrs:{content:t.row.auditRemark||"已审核拒绝",placement:"top"}},[a("el-button",{staticClass:"audit-btn-rejected",attrs:{size:"mini",type:"text",icon:"el-icon-circle-close",disabled:""}},[e._v("已拒绝")])],1):e._e(),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["operation:nearby:remove"],expression:"['operation:nearby:remove']"}],attrs:{size:"mini",type:"text",icon:"el-icon-delete"},on:{click:function(a){return e.handleDelete(t.row)}}},[e._v("删除")])]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}}),a("el-dialog",{attrs:{title:e.title,visible:e.open,width:"600px","append-to-body":""},on:{"update:visible":function(t){e.open=t}}},[a("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"100px"}},[a("el-form-item",{attrs:{label:"名称",prop:"name"}},[a("el-input",{attrs:{placeholder:"请输入名称"},model:{value:e.form.name,callback:function(t){e.$set(e.form,"name",t)},expression:"form.name"}})],1),a("el-form-item",{attrs:{label:"详细地址",prop:"address"}},[a("el-input",{attrs:{type:"textarea",placeholder:"请输入详细地址"},model:{value:e.form.address,callback:function(t){e.$set(e.form,"address",t)},expression:"form.address"}})],1),a("area-chain-selector",{ref:"areaSelector",attrs:{regionId:e.form.regionId,communityId:e.form.communityId,residentialId:e.form.residentialId,isLoading:e.isLoadingFormData,showCommunity:!0,showResidential:!0},on:{"update:regionId":function(t){return e.form.regionId=t},"update:communityId":function(t){return e.form.communityId=t},"update:residentialId":function(t){return e.form.residentialId=t}}}),a("el-form-item",{attrs:{label:"位置坐标",prop:"location"}},[a("el-input",{attrs:{placeholder:"格式：经度,纬度 (如：116.397128,39.916527)"},model:{value:e.form.location,callback:function(t){e.$set(e.form,"location",t)},expression:"form.location"}})],1),a("el-form-item",{attrs:{label:"分类",prop:"category"}},[a("el-select",{attrs:{placeholder:"请选择分类"},model:{value:e.form.category,callback:function(t){e.$set(e.form,"category",t)},expression:"form.category"}},e._l(e.dict.type.community_service_category,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",{attrs:{label:"图片",prop:"images"}},[a("image-upload",{attrs:{type:3},model:{value:e.form.images,callback:function(t){e.$set(e.form,"images",t)},expression:"form.images"}})],1),a("el-form-item",{attrs:{label:"服务描述",prop:"description"}},[a("el-input",{attrs:{type:"textarea",placeholder:"请输入服务描述"},model:{value:e.form.description,callback:function(t){e.$set(e.form,"description",t)},expression:"form.description"}})],1),a("el-form-item",{attrs:{label:"标签",prop:"tags"}},[a("el-select",{attrs:{placeholder:"请选择标签"},model:{value:e.form.tags,callback:function(t){e.$set(e.form,"tags",t)},expression:"form.tags"}},e._l(e.dict.type.community_service_tag,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",{attrs:{label:"联系电话",prop:"contactPhone"}},[a("el-input",{attrs:{placeholder:"请输入联系电话"},model:{value:e.form.contactPhone,callback:function(t){e.$set(e.form,"contactPhone",t)},expression:"form.contactPhone"}})],1),a("el-form-item",{attrs:{label:"营业时间",prop:"businessHours"}},[a("el-input",{attrs:{placeholder:"请输入营业时间"},model:{value:e.form.businessHours,callback:function(t){e.$set(e.form,"businessHours",t)},expression:"form.businessHours"}})],1),a("el-form-item",{attrs:{label:"是否推荐",prop:"isRecommended"}},[a("el-select",{attrs:{placeholder:"请选择是否推荐"},model:{value:e.form.isRecommended,callback:function(t){e.$set(e.form,"isRecommended",t)},expression:"form.isRecommended"}},e._l(e.dict.type.sys_yes_no_num,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:parseInt(e.value)}})})),1)],1),a("el-form-item",{attrs:{label:"是否官方发布",prop:"isOfficial"}},[a("el-select",{attrs:{placeholder:"请选择是否官方发布"},model:{value:e.form.isOfficial,callback:function(t){e.$set(e.form,"isOfficial",t)},expression:"form.isOfficial"}},e._l(e.dict.type.sys_yes_no_num,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:parseInt(e.value)}})})),1)],1),a("el-form-item",{attrs:{label:"状态",prop:"status"}},[a("el-radio-group",{model:{value:e.form.status,callback:function(t){e.$set(e.form,"status",t)},expression:"form.status"}},e._l(e.dict.type.business_normal_status,(function(t){return a("el-radio",{key:t.value,attrs:{label:parseInt(t.value)}},[e._v(e._s(t.label))])})),1)],1),a("el-form-item",{attrs:{label:"排序",prop:"sort"}},[a("el-input-number",{attrs:{min:0,placeholder:"数字越大排序越靠前"},model:{value:e.form.sort,callback:function(t){e.$set(e.form,"sort",t)},expression:"form.sort"}})],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),a("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1),a("el-dialog",{attrs:{title:"审核周边推荐",visible:e.auditOpen,width:"500px","append-to-body":""},on:{"update:visible":function(t){e.auditOpen=t}}},[a("el-form",{ref:"auditForm",attrs:{model:e.auditForm,rules:e.dynamicAuditRules,"label-width":"100px"}},[a("el-form-item",{attrs:{label:"审核状态",prop:"auditStatus"}},[a("el-radio-group",{model:{value:e.auditForm.auditStatus,callback:function(t){e.$set(e.auditForm,"auditStatus",t)},expression:"auditForm.auditStatus"}},[a("el-radio",{attrs:{label:1}},[e._v("通过")]),a("el-radio",{attrs:{label:2}},[e._v("拒绝")])],1)],1),2===e.auditForm.auditStatus?a("el-form-item",{attrs:{label:"审核备注",prop:"auditRemark"}},[a("el-input",{attrs:{type:"textarea",placeholder:"请输入拒绝原因"},model:{value:e.auditForm.auditRemark,callback:function(t){e.$set(e.auditForm,"auditRemark",t)},expression:"auditForm.auditRemark"}})],1):e._e()],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.submitAudit}},[e._v("确 定")]),a("el-button",{on:{click:e.cancelAudit}},[e._v("取 消")])],1)],1)],1)},n=[],i=a("5530"),o=a("c7eb"),l=a("1da1"),s=(a("d81d"),a("d3b7"),a("0643"),a("a573"),a("b775"));function u(e){return Object(s["a"])({url:"/operation/nearby/list",method:"get",params:e})}function c(e){return Object(s["a"])({url:"/operation/nearby/"+e,method:"get"})}function d(e){return Object(s["a"])({url:"/operation/nearby",method:"post",data:e})}function m(e){return Object(s["a"])({url:"/operation/nearby",method:"put",data:e})}function p(e){return Object(s["a"])({url:"/operation/nearby/"+e,method:"delete"})}function f(e){return Object(s["a"])({url:"/operation/nearby/audit",method:"put",data:e})}var b=a("939b"),h=a("7db5"),y={name:"Nearby",components:{AreaChainSelector:b["a"]},dicts:["community_service_category","sys_yes_no_num","common_audit_status","community_service_tag","business_normal_status"],data:function(){return{loading:!0,filePrefix:"https://ei-image.funnyswap.com/",ids:[],single:!0,multiple:!0,showSearch:!0,total:0,nearbyList:[],title:"",open:!1,isLoadingFormData:!1,auditOpen:!1,queryParams:{pageNum:1,pageSize:10,name:null,address:null,category:null,tags:null,isRecommended:null,auditStatus:null,status:null,regionId:null,communityId:null,residentialId:null},form:{},auditForm:{ids:[],auditStatus:1,auditRemark:""},rules:{name:[{required:!0,message:"名称不能为空",trigger:"blur"}],address:[{required:!0,message:"详细地址不能为空",trigger:"blur"}],category:[{required:!0,message:"分类不能为空",trigger:"change"}]},auditRules:{auditStatus:[{required:!0,message:"请选择审核状态",trigger:"change"}]}}},computed:{dynamicAuditRules:function(){var e={auditStatus:[{required:!0,message:"请选择审核状态",trigger:"change"}]};return 2===this.auditForm.auditStatus&&(e.auditRemark=[{required:!0,message:"请输入拒绝原因",trigger:"blur"},{min:5,message:"拒绝原因至少5个字符",trigger:"blur"}]),e}},created:function(){var e=this;return Object(l["a"])(Object(o["a"])().mark((function t(){return Object(o["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.initData();case 2:case"end":return t.stop()}}),t)})))()},methods:{initData:function(){var e=this;return Object(l["a"])(Object(o["a"])().mark((function t(){return Object(o["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:try{e.getList()}catch(a){e.$message.error("加载数据失败，请重试")}case 1:case"end":return t.stop()}}),t)})))()},initAreaSelector:function(e){var t=this;return Object(l["a"])(Object(o["a"])().mark((function a(){return Object(o["a"])().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.prev=0,a.next=3,t.$nextTick();case 3:if(!t.$refs.areaSelector){a.next=9;break}if(!e.regionId){a.next=9;break}return a.next=7,t.$refs.areaSelector.initForEdit({regionId:e.regionId,communityId:e.communityId,residentialId:e.residentialId});case 7:return a.next=9,t.$nextTick();case 9:return a.abrupt("return",!0);case 12:return a.prev=12,a.t0=a["catch"](0),a.abrupt("return",!1);case 15:case"end":return a.stop()}}),a,null,[[0,12]])})))()},getList:function(){var e=this;this.loading=!0,u(this.queryParams).then((function(t){e.nearbyList=t.rows,e.total=t.total,e.loading=!1}))},cancel:function(){this.open=!1,this.reset(),this.$refs.areaSelector&&this.$refs.areaSelector.clearAll(),this.isLoadingFormData=!1},reset:function(){this.form={id:null,name:null,address:null,location:null,category:null,images:null,description:null,tags:null,contactPhone:null,businessHours:null,viewCount:null,callCount:null,navigateCount:null,isRecommended:null,isOfficial:null,regionId:null,communityId:null,residentialId:null,submitUserId:null,auditStatus:null,auditUserId:null,auditTime:null,auditRemark:null,sort:null,status:1,createBy:null,createTime:null,updateBy:null,updateTime:null,deleted:null},this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.$refs.queryAreaSelector&&this.$refs.queryAreaSelector.clearAll(),this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.id})),this.single=1!==e.length,this.multiple=!e.length},handleAdd:function(){var e=this;return Object(l["a"])(Object(o["a"])().mark((function t(){var a;return Object(o["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.reset(),e.isLoadingFormData=!0,t.prev=2,a=e.$loading({lock:!0,text:"准备数据中...",spinner:"el-icon-loading",background:"rgba(0, 0, 0, 0.7)"}),e.open=!0,e.title="添加周边推荐",t.next=8,e.$nextTick();case 8:e.$refs.areaSelector&&e.$refs.areaSelector.clearAll(),a.close(),t.next=15;break;case 12:t.prev=12,t.t0=t["catch"](2),e.$message.error("准备数据失败，请重试");case 15:return t.prev=15,setTimeout((function(){e.isLoadingFormData=!1}),300),t.finish(15);case 18:case"end":return t.stop()}}),t,null,[[2,12,15,18]])})))()},handleUpdate:function(e){var t=this;return Object(l["a"])(Object(o["a"])().mark((function a(){var r,n,i;return Object(o["a"])().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return t.reset(),r=e.id||t.ids,a.prev=2,t.isLoadingFormData=!0,n=t.$loading({lock:!0,text:"加载数据中...",spinner:"el-icon-loading",background:"rgba(0, 0, 0, 0.7)"}),a.next=7,c(r);case 7:return i=a.sent,t.form=i.data,t.title="修改周边推荐",t.open=!0,a.next=13,t.$nextTick();case 13:return a.next=15,t.initAreaSelector({regionId:t.form.regionId,communityId:t.form.communityId,residentialId:t.form.residentialId});case 15:n.close(),a.next=22;break;case 18:a.prev=18,a.t0=a["catch"](2),t.$message.error("加载数据失败，请重试"),t.$loading&&t.$loading().close();case 22:return a.prev=22,t.isLoadingFormData=!1,a.finish(22);case 25:case"end":return a.stop()}}),a,null,[[2,18,22,25]])})))()},submitForm:function(){var e=this;this.$refs["form"].validate((function(t){t&&(null!=e.form.id?m(e.form).then((function(){e.$modal.msgSuccess("修改成功"),e.open=!1,e.getList()})):d(e.form).then((function(){e.$modal.msgSuccess("新增成功"),e.open=!1,e.getList()})))}))},handleAudit:function(e){this.auditForm.ids=e?[e.id]:this.ids,this.auditForm.auditStatus=1,this.auditForm.auditRemark="",this.auditOpen=!0},submitAudit:function(){var e=this;this.$refs["auditForm"].validate((function(t){t&&f(e.auditForm).then((function(){e.$modal.msgSuccess("审核成功"),e.auditOpen=!1,e.getList()}))}))},cancelAudit:function(){this.auditOpen=!1,this.auditForm={ids:[],auditStatus:1,auditRemark:""}},handleDelete:function(e){var t=this,a=e.id||this.ids;this.$modal.confirm('是否确认删除周边推荐编号为"'+a+'"的数据项？').then((function(){return p(a)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleExport:function(){this.download("operation/nearby/export",Object(i["a"])({},this.queryParams),"nearby_".concat((new Date).getTime(),".xlsx"))},handleRegionChange:function(){this.handleQuery()},regionFormatter:h["b"],communityFormatter:h["a"],residentialFormatter:h["c"]}},g=y,v=(a("0f6f"),a("2877")),w=Object(v["a"])(g,r,n,!1,null,"28375f9c",null);t["default"]=w.exports},3350:function(e,t,a){},"7db5":function(e,t,a){"use strict";a.d(t,"b",(function(){return n})),a.d(t,"a",(function(){return i})),a.d(t,"c",(function(){return o}));a("b0c0"),a("a9e3");var r=a("a04a");function n(e,t,a){if(!a)return"-";try{if(0===r["a"].getRegions().length)return"".concat(a);var n=r["a"].getRegionFullName(a);if(n)return n;var i=r["a"].getRegionById(a);return i?i.name:"".concat(a)}catch(o){return"".concat(a)}}function i(e,t,a){if(!a)return"-";try{if(0===r["a"].getCommunities().length)return"".concat(a);var n=r["a"].getCommunityById(a);return n?n.name:"".concat(a)}catch(i){return"".concat(a)}}function o(e,t,a){if(!a)return"-";try{if(0===r["a"].getResidentials().length)return"".concat(a);var n=r["a"].getResidentialById(a);return n?n.name:"".concat(a)}catch(i){return"".concat(a)}}}}]);