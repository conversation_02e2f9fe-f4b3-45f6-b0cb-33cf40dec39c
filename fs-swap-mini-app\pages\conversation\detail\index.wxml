<view class="chat-container">
  <!-- 消息列表 -->
  <scroll-view 
    class="message-list" 
    scroll-y 
    enhanced 
    show-scrollbar="{{false}}"
    scroll-into-view="{{scrollToView}}"
  >
    <view class="message-items">
      <view 
        class="message-item {{item.isMine ? 'mine' : 'other'}}"
        wx:for="{{messageList}}" 
        wx:key="id"
        data-message="{{item}}"
        bindlongpress="onMessageLongPress"
      >
        <!-- 时间分组 -->
        <view class="time-divider" wx:if="{{item.showTime}}">
          <text class="time-text">{{item.timeText}}</text>
        </view>
        
        <!-- 消息内容 -->
        <view class="message-wrapper">
          <!-- 别人的消息：头像在左，消息在右 -->
          <block wx:if="{{!item.isMine}}">
            <view class="avatar-wrapper">
              <image 
                src="{{item.avatar || '/static/img/default_avatar.png'}}" 
                class="avatar" 
                mode="aspectFill"
                binderror="onAvatarError"
                data-type="other"
                data-index="{{index}}"
              ></image>
            </view>
            
            <view class="message-content">
              <view class="message-bubble other {{item.sending ? 'sending' : ''}}">
                <text class="message-text">{{item.content}}</text>
              </view>
            </view>
          </block>
          
          <!-- 我的消息：消息在左，头像在右 -->
          <block wx:if="{{item.isMine}}">
            <view class="message-content mine">
              <view class="message-bubble mine {{item.sending ? 'sending' : ''}}">
                <text class="message-text">{{item.content}}</text>
              </view>
              
              <!-- 发送状态 -->
              <view class="send-status">
                <view class="loading" wx:if="{{item.sending}}">
                  <van-loading size="12px" color="#999999" />
                </view>
                <text class="status-text" wx:else>{{item.isRead ? '已读' : '已送达'}}</text>
              </view>
            </view>
            
            <view class="avatar-wrapper">
              <image 
                src="{{item.myAvatar || '/static/img/default_avatar.png'}}" 
                class="avatar" 
                mode="aspectFill"
                binderror="onAvatarError"
                data-type="mine"
                data-index="{{index}}"
              ></image>
            </view>
          </block>
        </view>
      </view>
    </view>
    
    <!-- 底部占位符 -->
    <view id="message-bottom" class="message-bottom"></view>
    
    <!-- 加载状态 -->
    <view class="loading-state" wx:if="{{isLoading}}">
      <van-loading size="16px" color="#95EC69" />
      <text class="loading-text">加载中...</text>
    </view>
  </scroll-view>

  <!-- 输入栏 -->
  <view class="input-bar">
    <view class="input-wrapper">
      <input 
        class="message-input" 
        type="text" 
        placeholder="输入消息..." 
        value="{{inputText}}"
        bindinput="onInputChange"
        confirm-type="send"
        bindconfirm="sendMessage"
      />
      <button 
        class="send-btn {{inputText ? 'active' : ''}}"
        bindtap="sendMessage"
        disabled="{{isSending}}"
      >
        发送
      </button>
    </view>
  </view>
</view> 