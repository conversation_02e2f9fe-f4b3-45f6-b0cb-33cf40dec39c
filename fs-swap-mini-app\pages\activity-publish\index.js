// pages/activity-publish/index.js
const api = require("../../config/api")
const util = require("../../utils/util")
const userUtils = require("../../utils/user")
const systemInfoService = require("../../services/systemInfo.js")
const app = getApp()

Page({
  data: {
    // 是否是编辑模式
    isEdit: false,
    // 编辑的活动ID
    activityId: null,

    // 常量配置
    MAX_DESCRIPTION_LENGTH: 2000,
    MAX_UPLOAD_COUNT: 9,

    // 表单数据
    formData: {
      title: '',
      description: '',
      images: '',
      address: '',
      location: '',
      category: '',
      categoryName: '',
      startTime: '',
      endTime: '',
      signupStartTime: '',
      signupEndTime: '',
      maxParticipants: '',
      isFree: 'N', // 默认免费
      feeAmount: '',
      feeDescription: '',
      status: '1' // 默认发布中
    },

    // 图片上传
    fileList: [],

    // 选择器相关
    showCategoryPicker: false,
    showStartTimePicker: false,
    showEndTimePicker: false,
    showSignupStartTimePicker: false,
    showSignupEndTimePicker: false,

    // 分类选项
    categories: [],

    // 时间选择器最小值
    minDate: new Date().getTime(),

    // 页面状态
    publishing: false,
    formValid: false
  },

  async onLoad(options) {
    // 获取分类数据
    await this.getCategories()

    // 检查是否是编辑模式
    if (options.id) {
      this.setData({
        isEdit: true,
        activityId: options.id
      });

      // 加载活动信息
      this.loadActivityInfo(options.id);
    } else {
      // 新建模式，确保status有默认值
      this.setData({
        'formData.status': '1' // 默认为发布中
      });
    }

    this.initFormValidation()
  },

  // 获取分类数据（使用统一的系统信息服务）
  async getCategories() {
    try {
      // 使用统一的系统信息服务获取格式化的活动分类数据
      const categories = await systemInfoService.getFormattedActivityCategories()

      // 转换数据格式以适配现有的UI组件
      const formattedCategories = categories
        .filter(item => item.id !== 'all') // 过滤掉"全部"选项，发布页面不需要
        .map(item => ({
          value: item.id,
          text: item.name
        }))

      this.setData({
        categories: formattedCategories
      })
    } catch (error) {
      console.error('加载活动分类数据失败:', error)

      // 设置空的分类数据
      this.setData({
        categories: []
      })

      // 显示错误提示
      wx.showToast({
        title: '分类加载失败，请重试',
        icon: 'none',
        duration: 2000
      })
    }
  },

  // 加载活动信息
  async loadActivityInfo(id) {
    try {
      wx.showLoading({
        title: '加载中...',
        mask: true
      });

      const res = await api.getActivityDetail(id);

      wx.hideLoading();

      if (res.code === 200) {
        const activity = res.data;

        // 检查是否是活动发布者
        const app = getApp();
        if (app.globalData.userInfo.id !== activity.userId) {
          wx.showToast({
            title: '您不是该活动的发布者',
            icon: 'none',
            success: () => {
              setTimeout(() => {
                wx.navigateBack();
              }, 1500);
            }
          });
          return;
        }

        // 设置表单数据
        this.setData({
          formData: {
            title: activity.title,
            description: activity.description,
            images: activity.images,
            address: activity.address,
            location: activity.location || '',
            category: activity.category,
            categoryName: activity.categoryName,
            startTime: activity.startTime,
            endTime: activity.endTime,
            signupStartTime: activity.signupStartTime,
            signupEndTime: activity.signupEndTime,
            maxParticipants: activity.maxParticipants || '',
            isFree: activity.isFree,
            feeAmount: activity.feeAmount || '',
            feeDescription: activity.feeDescription || '',
            status: activity.status
          }
        });

        // 使用系统信息服务处理图片列表
        let fileList = []
        if (activity.images) {
          const imageArray = activity.images.split(',')
          fileList = await Promise.all(imageArray.map(async (img, index) => ({
            url: await systemInfoService.processImageUrl(img.trim()),
            filePath: img.trim(),
            name: `image_${index + 1}.jpg`
          })))
        }

        this.setData({
          fileList
        });

        // 更新表单验证
        this.initFormValidation();
      } else {
        wx.showToast({
          title: res.msg || '加载失败',
          icon: 'none'
        });
      }
    } catch (error) {
      wx.hideLoading();
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      });
    }
  },

  // 初始化表单验证
  initFormValidation() {
    const {
      title,
      description,
      address,
      category,
      startTime,
      endTime,
      signupStartTime,
      signupEndTime,
      isFree,
      feeAmount,
      status
    } = this.data.formData;

    // 添加必填字段验证
    const isTitleValid = title && title.trim().length > 0;
    const isDescriptionValid = description && typeof description === 'string' &&
                              description.trim().length > 0 &&
                              description.length <= this.data.MAX_DESCRIPTION_LENGTH;
    const isAddressValid = address && address.trim().length > 0;
    const isCategoryValid = category !== '';
    const isTimeValid = startTime && endTime && signupStartTime && signupEndTime;
    const isStatusValid = status && (status === '0' || status === '1');

    // 如果是收费活动，验证费用
    const isFeeValid = isFree === 'N' || (isFree === 'Y' && parseFloat(feeAmount) > 0);

    // 验证图片上传
    const isImageValid = this.data.fileList.length > 0;

    const formValid = isTitleValid && isDescriptionValid && isAddressValid &&
                      isCategoryValid && isTimeValid && isFeeValid && isImageValid && isStatusValid;

    this.setData({
      formValid
    });
  },

  // 标题输入变化
  onTitleChange(e) {
    this.setData({
      'formData.title': e.detail
    });
    this.initFormValidation();
  },

  // 描述输入变化
  onDescriptionChange(e) {
    // 直接获取输入框的值
    const value = e.detail.value;

    // 确保值是字符串，如果是空值则设为空字符串
    let description = '';
    if (value !== undefined && value !== null) {
      description = value.toString();
    }

    this.setData({
      'formData.description': description
    });
    this.initFormValidation();
  },

  // 地址输入变化方法已移除，改为只能通过chooseLocation选择位置

  // 选择位置
  chooseLocation() {
    const residential = require('../../services/residential.js');
    
    wx.chooseLocation({
      success: (res) => {
        this.setData({
          'formData.address': res.address,
          'formData.location': residential.createPointLocation(res.longitude, res.latitude)
        });
        this.initFormValidation();
      },
      fail: (err) => {
        if (err.errMsg !== 'chooseLocation:fail cancel') {
          wx.showToast({
            title: '选择位置失败，请重试',
            icon: 'none'
          });
        }
      }
    });
  },

  // 最大参与人数输入变化
  onMaxParticipantsChange(e) {
    this.setData({
      'formData.maxParticipants': e.detail
    });
    this.initFormValidation();
  },

  // 费用输入变化
  onFeeAmountChange(e) {
    this.setData({
      'formData.feeAmount': e.detail
    });
    this.initFormValidation();
  },

  // 费用说明输入变化
  onFeeDescriptionChange(e) {
    this.setData({
      'formData.feeDescription': e.detail
    });
    this.initFormValidation();
  },

  // 是否免费切换
  onIsFreeChange(e) {
    this.setData({
      'formData.isFree': e.detail ? 'Y' : 'N'
    });
    this.initFormValidation();
  },

  // 状态切换
  onStatusChange(e) {
    this.setData({
      'formData.status': e.detail.value
    });
    this.initFormValidation();
  },

  // 显示分类选择器
  showCategoryPicker() {
    this.setData({
      showCategoryPicker: true
    });
  },

  // 隐藏分类选择器
  hideCategoryPicker() {
    this.setData({
      showCategoryPicker: false
    });
  },

  // 确认分类选择
  onCategoryConfirm(e) {
    const { value, index } = e.detail;
    this.setData({
      'formData.category': value,
      'formData.categoryName': this.data.categories[index].text,
      showCategoryPicker: false
    });
    this.initFormValidation();
  },

  // 显示开始时间选择器
  showStartTimePicker() {
    this.setData({
      showStartTimePicker: true
    });
  },

  // 隐藏开始时间选择器
  hideStartTimePicker() {
    this.setData({
      showStartTimePicker: false
    });
  },

  // 确认开始时间选择
  onStartTimeConfirm(e) {
    const startTime = new Date(e.detail);
    this.setData({
      'formData.startTime': util.formatTime(startTime),
      showStartTimePicker: false
    });
    this.initFormValidation();
  },

  // 显示结束时间选择器
  showEndTimePicker() {
    this.setData({
      showEndTimePicker: true
    });
  },

  // 隐藏结束时间选择器
  hideEndTimePicker() {
    this.setData({
      showEndTimePicker: false
    });
  },

  // 确认结束时间选择
  onEndTimeConfirm(e) {
    const endTime = new Date(e.detail);
    this.setData({
      'formData.endTime': util.formatTime(endTime),
      showEndTimePicker: false
    });
    this.initFormValidation();
  },

  // 显示报名开始时间选择器
  showSignupStartTimePicker() {
    this.setData({
      showSignupStartTimePicker: true
    });
  },

  // 隐藏报名开始时间选择器
  hideSignupStartTimePicker() {
    this.setData({
      showSignupStartTimePicker: false
    });
  },

  // 确认报名开始时间选择
  onSignupStartTimeConfirm(e) {
    const signupStartTime = new Date(e.detail);
    this.setData({
      'formData.signupStartTime': util.formatTime(signupStartTime),
      showSignupStartTimePicker: false
    });
    this.initFormValidation();
  },

  // 显示报名结束时间选择器
  showSignupEndTimePicker() {
    this.setData({
      showSignupEndTimePicker: true
    });
  },

  // 隐藏报名结束时间选择器
  hideSignupEndTimePicker() {
    this.setData({
      showSignupEndTimePicker: false
    });
  },

  // 确认报名结束时间选择
  onSignupEndTimeConfirm(e) {
    const signupEndTime = new Date(e.detail);
    this.setData({
      'formData.signupEndTime': util.formatTime(signupEndTime),
      showSignupEndTimePicker: false
    });
    this.initFormValidation();
  },

  // 上传图片后
  async afterRead(event) {
    const { file } = event.detail;
    // 转换为数组处理，支持单文件和多文件
    const files = Array.isArray(file) ? file : [file];

    // 检查文件总数量限制
    if (this.data.fileList.length + files.length > this.data.MAX_UPLOAD_COUNT) {
      wx.showToast({
        title: `最多上传${this.data.MAX_UPLOAD_COUNT}张图片`,
        icon: 'none'
      });
      return;
    }

    wx.showLoading({
      title: '上传中...',
      mask: true
    });

    try {
      // 使用 Promise.all 同时上传多个文件
      const uploadTasks = files.map(async (file) => {
        const uploadedFile = await util.uploadFile({
          filePath: file.url,
          type: '5'
        });

        if (!uploadedFile || !uploadedFile.filePath) {
          throw new Error('文件上传失败，未获取到文件路径');
        }

        return {
          url: file.url,
          name: '活动图片',
          isImage: true,
          filePath: uploadedFile.filePath
        };
      });

      const uploadedFiles = await Promise.all(uploadTasks);

      // 更新文件列表，添加所有上传成功的文件
      this.setData({
        fileList: [...this.data.fileList, ...uploadedFiles]
      });

      this.initFormValidation();
    } catch (error) {
      wx.showToast({
        title: error.message || '上传失败，请重试',
        icon: 'none'
      });
    } finally {
      wx.hideLoading();
    }
  },

  // 删除图片
  onDeleteFile(e) {
    const { index } = e.detail;
    if (index < 0 || index >= this.data.fileList.length) return;

    const fileList = this.data.fileList.filter((_, idx) => idx !== index);

    this.setData({
      fileList
    });
    this.initFormValidation();
  },

  // 处理表单提交
  async handleSubmit() {
    // 表单验证
    if (!this.data.formValid) {
      // 检查status是否有值
      if (!this.data.formData.status) {
        wx.showToast({
          title: '请选择发布状态',
          icon: 'none'
        });
        return;
      }

      wx.showToast({
        title: '请完善表单信息',
        icon: 'none'
      });
      return;
    }

    // 时间验证
    const startTime = new Date(this.data.formData.startTime);
    const endTime = new Date(this.data.formData.endTime);
    const signupStartTime = new Date(this.data.formData.signupStartTime);
    const signupEndTime = new Date(this.data.formData.signupEndTime);
    const now = new Date();

    if (startTime <= now) {
      wx.showToast({
        title: '活动开始时间必须大于当前时间',
        icon: 'none'
      });
      return;
    }

    if (endTime <= startTime) {
      wx.showToast({
        title: '活动结束时间必须大于开始时间',
        icon: 'none'
      });
      return;
    }

    if (signupEndTime <= signupStartTime) {
      wx.showToast({
        title: '报名结束时间必须大于开始时间',
        icon: 'none'
      });
      return;
    }

    if (signupEndTime >= endTime) {
      wx.showToast({
        title: '报名结束时间必须小于活动结束时间',
        icon: 'none'
      });
      return;
    }

    if (this.data.publishing) {
      return; // 防止重复提交
    }

    // 防抖检查 - 1秒内不允许重复点击
    const nowTime = Date.now();
    if (this.lastSubmitTime && (nowTime - this.lastSubmitTime) < 1000) {
      wx.showToast({
        title: '请勿频繁点击',
        icon: 'none',
        duration: 1000
      });
      return;
    }
    this.lastSubmitTime = nowTime;

    try {
      this.setData({
        publishing: true
      });

      wx.showLoading({
        title: this.data.isEdit ? '更新中...' : '发布中...'
      });

      // 检查是否有图片上传
      if (this.data.fileList.length === 0) {
        wx.showToast({
          title: '请至少上传一张图片',
          icon: 'none'
        });
        this.setData({
          publishing: false
        });
        wx.hideLoading();
        return;
      }

      // 处理图片路径
      const images = this.data.fileList.map(file => file.filePath).join(',');

      let res;
      // 确保description是字符串
      let description = this.data.formData.description;
      if (description === undefined || description === null) {
        description = '';
      } else if (typeof description === 'object') {
        description = description.toString();
      }

      // 确保category是字符串
      let category = this.data.formData.category;
      if (typeof category === 'object') {
        category = category.value || category.toString();
      }

      // 确保status有值
      let status = this.data.formData.status;
      if (!status) {
        status = '1'; // 默认为发布中
        }

      const activityData = {
        title: this.data.formData.title,
        description: description,
        images: images,
        address: this.data.formData.address,
        location: this.data.formData.location,
        category: category,
        startTime: this.data.formData.startTime,
        endTime: this.data.formData.endTime,
        signupStartTime: this.data.formData.signupStartTime,
        signupEndTime: this.data.formData.signupEndTime,
        maxParticipants: this.data.formData.maxParticipants || null,
        isFree: this.data.formData.isFree,
        feeAmount: this.data.formData.isFree === 'Y' ? this.data.formData.feeAmount : 0,
        feeDescription: this.data.formData.isFree === 'Y' ? this.data.formData.feeDescription : '',
        status: status
      };

      if (this.data.isEdit) {
        // 编辑模式，更新活动
        activityData.id = this.data.activityId;
        res = await api.updateActivity(activityData);
      } else {
        // 新增模式，添加活动
        res = await api.addActivity(activityData);
      }

      wx.hideLoading();

      if (res.code === 200) {
        wx.showToast({
          title: this.data.isEdit ? '更新成功' : '发布成功',
          success: () => {
            setTimeout(() => {
              wx.navigateBack();
            }, 1500);
          }
        });
      } else {
        throw new Error(res.msg || (this.data.isEdit ? '更新失败' : '发布失败'));
      }
    } catch (error) {
      wx.hideLoading();
      wx.showToast({
        title: error.message || '操作失败，请重试',
        icon: 'none'
      });
    } finally {
      this.setData({
        publishing: false
      });
    }
  }
})
