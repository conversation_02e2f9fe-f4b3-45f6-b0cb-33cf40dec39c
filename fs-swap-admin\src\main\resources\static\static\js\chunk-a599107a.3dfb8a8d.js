(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-a599107a"],{"7db5":function(e,t,a){"use strict";a.d(t,"b",(function(){return n})),a.d(t,"a",(function(){return l})),a.d(t,"c",(function(){return i}));a("b0c0"),a("a9e3");var r=a("a04a");function n(e,t,a){if(!a)return"-";try{if(0===r["a"].getRegions().length)return"".concat(a);var n=r["a"].getRegionFullName(a);if(n)return n;var l=r["a"].getRegionById(a);return l?l.name:"".concat(a)}catch(i){return"".concat(a)}}function l(e,t,a){if(!a)return"-";try{if(0===r["a"].getCommunities().length)return"".concat(a);var n=r["a"].getCommunityById(a);return n?n.name:"".concat(a)}catch(l){return"".concat(a)}}function i(e,t,a){if(!a)return"-";try{if(0===r["a"].getResidentials().length)return"".concat(a);var n=r["a"].getResidentialById(a);return n?n.name:"".concat(a)}catch(l){return"".concat(a)}}},d92c:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",attrs:{model:e.queryParams,size:"small",inline:!0,"label-width":"68px"}},[a("area-chain-selector",{ref:"queryAreaSelector",attrs:{regionId:e.queryParams.regionId,communityId:e.queryParams.communityId,residentialId:e.queryParams.residentialId,labels:{region:"行政区域",community:"社区",residential:"小区"},showCommunity:!0,showResidential:!0,inlineLayout:!0},on:{"update:regionId":function(t){return e.queryParams.regionId=t},"update:communityId":function(t){return e.queryParams.communityId=t},"update:residentialId":function(t){return e.queryParams.residentialId=t},"community-change":e.handleQuery,"region-change":e.handleRegionChange}}),a("el-form-item",{attrs:{label:"标题",prop:"title"}},[a("el-input",{attrs:{placeholder:"请输入标题",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.title,callback:function(t){e.$set(e.queryParams,"title",t)},expression:"queryParams.title"}})],1),a("el-form-item",{attrs:{label:"跳转类型",prop:"linkType"}},[a("el-select",{attrs:{placeholder:"请选择跳转类型",clearable:""},model:{value:e.queryParams.linkType,callback:function(t){e.$set(e.queryParams,"linkType",t)},expression:"queryParams.linkType"}},e._l(e.dict.type.banner_link_type,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",{attrs:{label:"跳转值",prop:"linkValue"}},[a("el-input",{attrs:{placeholder:"请输入跳转值",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.linkValue,callback:function(t){e.$set(e.queryParams,"linkValue",t)},expression:"queryParams.linkValue"}})],1),a("el-form-item",{attrs:{label:"排序",prop:"sort"}},[a("el-input",{attrs:{placeholder:"请输入排序",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.sort,callback:function(t){e.$set(e.queryParams,"sort",t)},expression:"queryParams.sort"}})],1),a("el-form-item",{attrs:{label:"状态",prop:"status"}},[a("el-select",{attrs:{placeholder:"请选择状态",clearable:""},model:{value:e.queryParams.status,callback:function(t){e.$set(e.queryParams,"status",t)},expression:"queryParams.status"}},e._l(e.dict.type.banner_status,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",{attrs:{label:"开始时间",prop:"startTime"}},[a("el-date-picker",{attrs:{clearable:"",type:"datetime","value-format":"yyyy-MM-dd HH:mm:ss",placeholder:"请选择展示开始时间"},model:{value:e.queryParams.startTime,callback:function(t){e.$set(e.queryParams,"startTime",t)},expression:"queryParams.startTime"}})],1),a("el-form-item",{attrs:{label:"结束时间",prop:"endTime"}},[a("el-date-picker",{attrs:{clearable:"",type:"datetime","value-format":"yyyy-MM-dd HH:mm:ss",placeholder:"请选择展示结束时间"},model:{value:e.queryParams.endTime,callback:function(t){e.$set(e.queryParams,"endTime",t)},expression:"queryParams.endTime"}})],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),a("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1),a("el-row",{staticClass:"mb8",attrs:{gutter:10}},[a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["operation:banner:add"],expression:"['operation:banner:add']"}],attrs:{type:"primary",plain:"",icon:"el-icon-plus",size:"mini"},on:{click:e.handleAdd}},[e._v("新增")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["operation:banner:edit"],expression:"['operation:banner:edit']"}],attrs:{type:"success",plain:"",icon:"el-icon-edit",size:"mini",disabled:e.single},on:{click:e.handleUpdate}},[e._v("修改")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["operation:banner:remove"],expression:"['operation:banner:remove']"}],attrs:{type:"danger",plain:"",icon:"el-icon-delete",size:"mini",disabled:e.multiple},on:{click:e.handleDelete}},[e._v("删除")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["operation:banner:export"],expression:"['operation:banner:export']"}],attrs:{type:"warning",plain:"",icon:"el-icon-download",size:"mini"},on:{click:e.handleExport}},[e._v("导出")])],1),a("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.bannerList},on:{"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"55",align:"center"}}),a("el-table-column",{attrs:{label:"id",align:"center",prop:"id"}}),a("el-table-column",{attrs:{label:"行政区域",align:"center",prop:"regionId",formatter:e.regionFormatter}}),a("el-table-column",{attrs:{label:"社区",align:"center",prop:"communityId",formatter:e.communityFormatter}}),a("el-table-column",{attrs:{label:"小区",align:"center",prop:"residentialId",formatter:e.residentialFormatter}}),a("el-table-column",{attrs:{label:"标题",align:"center",prop:"title"}}),a("el-table-column",{attrs:{label:"图片",align:"center",prop:"image",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("image-preview",{attrs:{src:e.filePrefix+t.row.image,width:50,height:50}})]}}])}),a("el-table-column",{attrs:{label:"跳转类型",align:"center",prop:"linkType"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("dict-tag",{attrs:{options:e.dict.type.banner_link_type,value:t.row.linkType}})]}}])}),a("el-table-column",{attrs:{label:"跳转值",align:"center",prop:"linkValue"}}),a("el-table-column",{attrs:{label:"排序",align:"center",prop:"sort"}}),a("el-table-column",{attrs:{label:"状态",align:"center",prop:"status"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("dict-tag",{attrs:{options:e.dict.type.banner_status,value:t.row.status}})]}}])}),a("el-table-column",{attrs:{label:"展示开始时间",align:"center",prop:"startTime",width:"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(e.parseTime(t.row.startTime,"{y}-{m}-{d} {h}:{i}:{s}")))])]}}])}),a("el-table-column",{attrs:{label:"展示结束时间",align:"center",prop:"endTime",width:"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(e.parseTime(t.row.endTime,"{y}-{m}-{d} {h}:{i}:{s}")))])]}}])}),a("el-table-column",{attrs:{label:"备注",align:"center",prop:"remark"}}),a("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["operation:banner:edit"],expression:"['operation:banner:edit']"}],attrs:{size:"mini",type:"text",icon:"el-icon-edit"},on:{click:function(a){return e.handleUpdate(t.row)}}},[e._v("修改")]),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["operation:banner:remove"],expression:"['operation:banner:remove']"}],attrs:{size:"mini",type:"text",icon:"el-icon-delete"},on:{click:function(a){return e.handleDelete(t.row)}}},[e._v("删除")])]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}}),a("el-dialog",{attrs:{title:e.title,visible:e.open,width:"800px","append-to-body":""},on:{"update:visible":function(t){e.open=t}}},[a("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"80px"}},[a("area-chain-selector",{ref:"areaSelector",attrs:{regionId:e.form.regionId,communityId:e.form.communityId,residentialId:e.form.residentialId,isLoading:e.isLoadingFormData,showCommunity:!0,showResidential:!0},on:{"update:regionId":function(t){return e.form.regionId=t},"update:communityId":function(t){return e.form.communityId=t},"update:residentialId":function(t){return e.form.residentialId=t}}}),a("el-form-item",{attrs:{label:"标题",prop:"title"}},[a("el-input",{attrs:{placeholder:"请输入标题"},model:{value:e.form.title,callback:function(t){e.$set(e.form,"title",t)},expression:"form.title"}})],1),a("el-form-item",{attrs:{label:"图片",prop:"image"}},[a("image-upload",{model:{value:e.form.image,callback:function(t){e.$set(e.form,"image",t)},expression:"form.image"}})],1),a("el-form-item",{attrs:{label:"跳转类型",prop:"linkType"}},[a("el-select",{attrs:{placeholder:"请选择跳转类型"},model:{value:e.form.linkType,callback:function(t){e.$set(e.form,"linkType",t)},expression:"form.linkType"}},e._l(e.dict.type.banner_link_type,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",{attrs:{label:"跳转值",prop:"linkValue"}},[a("el-input",{attrs:{placeholder:"请输入跳转值"},model:{value:e.form.linkValue,callback:function(t){e.$set(e.form,"linkValue",t)},expression:"form.linkValue"}})],1),a("el-form-item",{attrs:{label:"排序",prop:"sort"}},[a("el-input",{attrs:{placeholder:"请输入排序"},model:{value:e.form.sort,callback:function(t){e.$set(e.form,"sort",t)},expression:"form.sort"}})],1),a("el-form-item",{attrs:{label:"状态",prop:"status"}},[a("el-select",{attrs:{placeholder:"请选择状态"},model:{value:e.form.status,callback:function(t){e.$set(e.form,"status",t)},expression:"form.status"}},e._l(e.dict.type.banner_status,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",{attrs:{label:"展示开始时间",prop:"startTime"}},[a("el-date-picker",{attrs:{clearable:"",type:"datetime","value-format":"yyyy-MM-dd HH:mm:ss",placeholder:"请选择展示开始时间"},model:{value:e.form.startTime,callback:function(t){e.$set(e.form,"startTime",t)},expression:"form.startTime"}})],1),a("el-form-item",{attrs:{label:"展示结束时间",prop:"endTime"}},[a("el-date-picker",{attrs:{clearable:"",type:"datetime","value-format":"yyyy-MM-dd HH:mm:ss",placeholder:"请选择展示结束时间"},model:{value:e.form.endTime,callback:function(t){e.$set(e.form,"endTime",t)},expression:"form.endTime"}})],1),a("el-form-item",{attrs:{label:"备注",prop:"remark"}},[a("el-input",{attrs:{type:"textarea",placeholder:"请输入内容"},model:{value:e.form.remark,callback:function(t){e.$set(e.form,"remark",t)},expression:"form.remark"}})],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),a("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1)],1)},n=[],l=a("5530"),i=a("c7eb"),o=a("1da1"),s=(a("d81d"),a("d3b7"),a("0643"),a("a573"),a("b775"));function u(e){return Object(s["a"])({url:"/operation/banner/list",method:"get",params:e})}function c(e){return Object(s["a"])({url:"/operation/banner/"+e,method:"get"})}function m(e){return Object(s["a"])({url:"/operation/banner",method:"post",data:e})}function d(e){return Object(s["a"])({url:"/operation/banner",method:"put",data:e})}function p(e){return Object(s["a"])({url:"/operation/banner/"+e,method:"delete"})}var f=a("939b"),b=a("7db5"),h={name:"Banner",components:{AreaChainSelector:f["a"]},dicts:["banner_link_type","banner_status"],data:function(){return{loading:!0,filePrefix:"https://ei-image.funnyswap.com/",ids:[],single:!0,multiple:!0,showSearch:!0,total:0,bannerList:[],title:"",open:!1,isLoadingFormData:!1,queryParams:{pageNum:1,pageSize:10,regionId:null,communityId:null,residentialId:null,title:null,image:null,linkType:null,linkValue:null,sort:null,status:null,startTime:null,endTime:null},form:{},rules:{title:[{required:!0,message:"标题不能为空",trigger:"blur"}],image:[{required:!0,message:"图片不能为空",trigger:"blur"}],linkType:[{required:!0,message:"跳转类型不能为空",trigger:"change"}],status:[{required:!0,message:"状态不能为空",trigger:"change"}]}}},computed:{},watch:{},created:function(){var e=this;return Object(o["a"])(Object(i["a"])().mark((function t(){return Object(i["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.initData();case 2:case"end":return t.stop()}}),t)})))()},methods:{initData:function(){var e=this;return Object(o["a"])(Object(i["a"])().mark((function t(){return Object(i["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:try{e.getList()}catch(a){e.$message.error("加载数据失败，请重试")}case 1:case"end":return t.stop()}}),t)})))()},initAreaSelector:function(e){var t=this;return Object(o["a"])(Object(i["a"])().mark((function a(){return Object(i["a"])().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.prev=0,a.next=3,t.$nextTick();case 3:if(!t.$refs.areaSelector){a.next=9;break}if(!e.regionId){a.next=9;break}return a.next=7,t.$refs.areaSelector.initForEdit({regionId:e.regionId,communityId:e.communityId,residentialId:e.residentialId});case 7:return a.next=9,t.$nextTick();case 9:return a.abrupt("return",!0);case 12:return a.prev=12,a.t0=a["catch"](0),a.abrupt("return",!1);case 15:case"end":return a.stop()}}),a,null,[[0,12]])})))()},getList:function(){var e=this;return Object(o["a"])(Object(i["a"])().mark((function t(){var a;return Object(i["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.loading=!0,t.prev=1,t.next=4,u(e.queryParams);case 4:a=t.sent,e.bannerList=a.rows,e.total=a.total,t.next=12;break;case 9:t.prev=9,t.t0=t["catch"](1),e.$message.error("加载数据失败，请重试");case 12:return t.prev=12,e.loading=!1,t.finish(12);case 15:case"end":return t.stop()}}),t,null,[[1,9,12,15]])})))()},cancel:function(){this.open=!1,this.reset(),this.$refs.areaSelector&&this.$refs.areaSelector.clearAll(),this.isLoadingFormData=!1},reset:function(){this.form={id:null,regionId:null,communityId:null,residentialId:null,title:null,image:null,linkType:null,linkValue:null,sort:null,status:null,startTime:null,endTime:null,createBy:null,createTime:null,updateBy:null,updateTime:null,remark:null},this.resetForm("form"),this.$refs.areaSelector&&this.$refs.areaSelector.clearAll(),this.isLoadingFormData=!1},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.id})),this.single=1!==e.length,this.multiple=!e.length},handleAdd:function(){var e=this;return Object(o["a"])(Object(i["a"])().mark((function t(){var a;return Object(i["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.reset(),e.isLoadingFormData=!0,t.prev=2,a=e.$loading({lock:!0,text:"准备数据中...",spinner:"el-icon-loading",background:"rgba(0, 0, 0, 0.7)"}),e.open=!0,e.title="添加轮播广告",t.next=8,e.$nextTick();case 8:e.$refs.areaSelector&&e.$refs.areaSelector.clearAll(),a.close(),t.next=15;break;case 12:t.prev=12,t.t0=t["catch"](2),e.$message.error("准备数据失败，请重试");case 15:return t.prev=15,setTimeout((function(){e.isLoadingFormData=!1}),300),t.finish(15);case 18:case"end":return t.stop()}}),t,null,[[2,12,15,18]])})))()},handleUpdate:function(e){var t=this;return Object(o["a"])(Object(i["a"])().mark((function a(){var r,n,l;return Object(i["a"])().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return t.reset(),r=e.id||t.ids,a.prev=2,t.isLoadingFormData=!0,n=t.$loading({lock:!0,text:"加载数据中...",spinner:"el-icon-loading",background:"rgba(0, 0, 0, 0.7)"}),a.next=7,c(r);case 7:return l=a.sent,t.form=l.data,t.title="修改轮播广告",t.open=!0,a.next=13,t.$nextTick();case 13:return a.next=15,t.initAreaSelector({regionId:t.form.regionId,communityId:t.form.communityId,residentialId:t.form.residentialId});case 15:n.close(),a.next=22;break;case 18:a.prev=18,a.t0=a["catch"](2),t.$message.error("加载数据失败，请重试"),t.$loading&&t.$loading().close();case 22:return a.prev=22,t.isLoadingFormData=!1,a.finish(22);case 25:case"end":return a.stop()}}),a,null,[[2,18,22,25]])})))()},submitForm:function(){var e=this;this.$refs["form"].validate((function(t){t&&(null!=e.form.id?d(e.form).then((function(){e.$modal.msgSuccess("修改成功"),e.open=!1,e.getList()})):m(e.form).then((function(){e.$modal.msgSuccess("新增成功"),e.open=!1,e.getList()})))}))},handleDelete:function(e){var t=this,a=e.id||this.ids;this.$modal.confirm('是否确认删除轮播广告编号为"'+a+'"的数据项？').then((function(){return p(a)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleExport:function(){this.download("operation/banner/export",Object(l["a"])({},this.queryParams),"banner_".concat((new Date).getTime(),".xlsx"))},handleRegionChange:function(){this.handleQuery()},regionFormatter:b["b"],communityFormatter:b["a"],residentialFormatter:b["c"]}},y=h,g=a("2877"),v=Object(g["a"])(y,r,n,!1,null,null,null);t["default"]=v.exports}}]);