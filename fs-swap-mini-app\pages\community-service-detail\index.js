const util = require('../../utils/util.js')
const api = require('../../config/api.js')
const systemInfoService = require('../../services/systemInfo.js')
const app = getApp()

Page({
  data: {
    // 服务详情数据
    serviceDetail: null,
    serviceId: null,
    
    // 页面状态
    loading: true,
    error: null,
    
    // 图片轮播相关
    currentImageIndex: 0,
    
    // 地图标记
    mapMarkers: [],
    
    // 分类映射
    categoryNames: {
      'business': '商户服务',
      'entertainment': '休闲娱乐',
      'facility': '公共设施'
    },
    
    // 审核状态映射（动态加载）
    auditStatusMap: {},
    
    // 审核状态文本
    auditStatusText: ''
  },

  async onLoad(options) {
    // 加载审核状态映射
    await this.loadAuditStatusMap();
    
    if (options.id) {
      this.setData({
        serviceId: options.id
      })
      this.loadServiceDetail()
    } else {
      this.setData({
        loading: false,
        error: '缺少服务ID参数'
      })
    }
  },

  onShow: function () {
    // 页面显示时可以刷新数据
  },

  // 加载服务详情
  loadServiceDetail: function () {
    this.setData({ 
      loading: true, 
      error: null 
    })
    
    api.getCommunityNearbyDetail(this.data.serviceId).then(res => {
      if (res.code === 200 && res.data) {
        const serviceDetail = res.data
        
        // 处理审核状态文本
        const statusInfo = this.data.auditStatusMap[serviceDetail.auditStatus];
        const auditStatusText = statusInfo ? statusInfo.text || statusInfo : '';
        
        // 生成地图标记
        const mapMarkers = []
        let latitude = serviceDetail.latitude
        let longitude = serviceDetail.longitude
        
        // 如果没有经纬度字段，尝试解析location字段
        if (!latitude || !longitude) {
          const residentialService = require('../../services/residential.js')
          const coords = residentialService.parseLocation(serviceDetail.location)
          if (coords) {
            latitude = coords.latitude
            longitude = coords.longitude
          }
        }
        
        if (latitude && longitude) {
          mapMarkers.push({
            id: serviceDetail.id,
            latitude: latitude,
            longitude: longitude,
            iconPath: this.getCategoryMarkerIcon(serviceDetail.category),
            width: 18,
            height: 22,
            title: serviceDetail.name,
            callout: {
              content: `📍 ${serviceDetail.name}`,
              color: '#ffffff',
              fontSize: 9,
              borderRadius: 3,
              bgColor: 'rgba(59, 127, 255, 0.85)',
              padding: 3,
              display: 'ALWAYS',
              borderWidth: 1,
              borderColor: 'rgba(59, 127, 255, 0.9)',
              anchorY: 0,
              anchorX: 0
            }
          })
        }
        
        this.setData({
          serviceDetail: serviceDetail,
          auditStatusText: auditStatusText,
          mapMarkers: mapMarkers,
          loading: false
        })
        
        // 设置页面标题
        wx.setNavigationBarTitle({
          title: serviceDetail.name || '服务详情'
        })
        
      } else {
        throw new Error(res.msg || '获取服务详情失败')
      }
    }).catch(error => {
      console.error('加载服务详情失败:', error)
      this.setData({
        loading: false,
        error: error.message || '加载失败，请重试'
      })
      
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      })
    })
  },

  // 加载审核状态映射
  async loadAuditStatusMap() {
    try {
      const auditStatusMap = await systemInfoService.getAuditStatusMap();
      this.setData({ auditStatusMap });
    } catch (error) {
      console.error('加载审核状态映射失败:', error);
      // 使用默认映射作为降级
      this.setData({
        auditStatusMap: {
          0: { text: '待审核', color: '#ff9500' },
          1: { text: '已通过', color: '#07c160' },
          2: { text: '已拒绝', color: '#ee0a24' }
        }
      });
    }
  },

  // 获取分类图标
  getCategoryIcon: function (category) {
    const iconMap = {
      'business': 'shop-o',
      'entertainment': 'smile-o',
      'facility': 'location-o'
    }
    return iconMap[category] || 'location-o'
  },

  // 获取分类名称
  getCategoryName: function (category) {
    return this.data.categoryNames[category] || '其他服务'
  },

  // 获取分类地图标记图标
  getCategoryMarkerIcon: function (category) {
    const iconMap = {
      'business': '/static/img/marker-business.png',
      'entertainment': '/static/img/marker-entertainment.png',
      'facility': '/static/img/marker-facility.png'
    }
    return iconMap[category] || '/static/img/marker-default.png'
  },

  // 格式化距离
  formatDistance: function (distance) {
    if (!distance) return '未知'
    
    if (distance < 1000) {
      return Math.round(distance) + 'm'
    } else {
      return (distance / 1000).toFixed(2) + 'km'
    }
  },

  // 格式化时间 - 使用统一的工具方法
  formatTime: function (dateStr) {
    if (!dateStr) return ''
    
    try {
      return util.formatTimeUnified(dateStr, { type: 'mixed' });
    } catch (error) {
      console.warn('formatTime error:', error, 'input:', dateStr);
      return dateStr
    }
  },

  // 图片预览
  onImagePreview: function (event) {
    const url = event.currentTarget.dataset.url
    const urls = event.currentTarget.dataset.urls
    
    wx.previewImage({
      current: url,
      urls: urls
    })
  },

  // 轮播图变化
  onSwiperChange: function (event) {
    this.setData({
      currentImageIndex: event.detail.current
    })
  },

  // 拨打电话
  onCallService: function () {
    const serviceDetail = this.data.serviceDetail
    if (!serviceDetail || !serviceDetail.contactPhone) {
      wx.showToast({
        title: '暂无联系电话',
        icon: 'none'
      })
      return
    }

    wx.showModal({
      title: '拨打电话',
      content: `确定要拨打 ${serviceDetail.name} 的电话 ${serviceDetail.contactPhone} 吗？`,
      confirmText: '拨打',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          wx.makePhoneCall({
            phoneNumber: serviceDetail.contactPhone,
            success: () => {
              // 记录拨打电话行为
              this.recordCallAction()
            }
          })
        }
      }
    })
  },

  // 导航到服务位置
  onNavigateToService: function () {
    const serviceDetail = this.data.serviceDetail
    if (!serviceDetail) {
      wx.showToast({
        title: '暂无服务信息',
        icon: 'none'
      })
      return
    }

    // 优先使用经纬度字段，如果没有则解析location字段
    let latitude = serviceDetail.latitude
    let longitude = serviceDetail.longitude
    
    if (!latitude || !longitude) {
      // 使用residentialService解析location字段
      const residentialService = require('../../services/residential.js')
      const coords = residentialService.parseLocation(serviceDetail.location)
      if (!coords) {
        wx.showToast({
          title: '暂无位置信息',
          icon: 'none'
        })
        return
      }
      latitude = coords.latitude
      longitude = coords.longitude
    }

    // 直接跳转到地图，不显示确认弹框
    wx.openLocation({
      latitude: latitude,
      longitude: longitude,
      name: serviceDetail.name,
      address: serviceDetail.address,
      scale: 18,
      success: () => {
        // 记录导航行为
        this.recordNavigateAction()
      }
    })
  },

  // 点击小地图
  onMapTap: function () {
    const serviceDetail = this.data.serviceDetail
    if (!serviceDetail) {
      return
    }

    // 优先使用经纬度字段，如果没有则解析location字段
    let latitude = serviceDetail.latitude
    let longitude = serviceDetail.longitude
    
    if (!latitude || !longitude) {
      // 使用residentialService解析location字段
      const residentialService = require('../../services/residential.js')
      const coords = residentialService.parseLocation(serviceDetail.location)
      if (!coords) {
        return
      }
      latitude = coords.latitude
      longitude = coords.longitude
    }

    // 跳转到地图页面或打开位置
    wx.openLocation({
      latitude: latitude,
      longitude: longitude,
      name: serviceDetail.name,
      address: serviceDetail.address,
      scale: 18
    })
  },

  // 记录拨打电话行为
  recordCallAction: function () {
    api.recordCall(this.data.serviceId).then(res => {
      // 更新本地统计数据
      const serviceDetail = this.data.serviceDetail
      if (serviceDetail) {
        serviceDetail.callCount = (serviceDetail.callCount || 0) + 1
        this.setData({ serviceDetail })
      }
    }).catch(error => {
      console.error('记录拨打电话失败:', error)
    })
  },

  // 记录导航行为
  recordNavigateAction: function () {
    api.recordNavigate(this.data.serviceId).then(res => {
      // 更新本地统计数据
      const serviceDetail = this.data.serviceDetail
      if (serviceDetail) {
        serviceDetail.navigateCount = (serviceDetail.navigateCount || 0) + 1
        this.setData({ serviceDetail })
      }
    }).catch(error => {
      console.error('记录导航失败:', error)
    })
  },

  // 分享
  onShare: function () {
    const serviceDetail = this.data.serviceDetail
    if (!serviceDetail) return

    return {
      title: `${serviceDetail.name} - 社区服务`,
      path: `/pages/community-service-detail/index?id=${serviceDetail.id}`,
      imageUrl: serviceDetail.imageUrls && serviceDetail.imageUrls.length > 0 ? serviceDetail.imageUrls[0] : ''
    }
  },

  // 返回
  onNavigateBack: function () {
    wx.navigateBack({
      delta: 1,
      fail: () => {
        wx.navigateTo({
          url: '/pages/community-service/index'
        })
      }
    })
  },

  // 分享给朋友
  onShareAppMessage: function () {
    return this.onShare()
  },

  // 分享到朋友圈
  onShareTimeline: function () {
    const serviceDetail = this.data.serviceDetail
    if (!serviceDetail) return {}

    return {
      title: `${serviceDetail.name} - 社区服务`,
      imageUrl: serviceDetail.imageUrls && serviceDetail.imageUrls.length > 0 ? serviceDetail.imageUrls[0] : ''
    }
  }
}) 