(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-e67b3980"],{"7db5":function(e,t,a){"use strict";a.d(t,"b",(function(){return n})),a.d(t,"a",(function(){return o})),a.d(t,"c",(function(){return i}));a("b0c0"),a("a9e3");var r=a("a04a");function n(e,t,a){if(!a)return"-";try{if(0===r["a"].getRegions().length)return"".concat(a);var n=r["a"].getRegionFullName(a);if(n)return n;var o=r["a"].getRegionById(a);return o?o.name:"".concat(a)}catch(i){return"".concat(a)}}function o(e,t,a){if(!a)return"-";try{if(0===r["a"].getCommunities().length)return"".concat(a);var n=r["a"].getCommunityById(a);return n?n.name:"".concat(a)}catch(o){return"".concat(a)}}function i(e,t,a){if(!a)return"-";try{if(0===r["a"].getResidentials().length)return"".concat(a);var n=r["a"].getResidentialById(a);return n?n.name:"".concat(a)}catch(o){return"".concat(a)}}},ee9b:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",attrs:{model:e.queryParams,size:"small",inline:!0,"label-width":"80px"}},[a("el-form-item",{attrs:{label:"名称",prop:"name"}},[a("el-input",{attrs:{placeholder:"请输入名称",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.name,callback:function(t){e.$set(e.queryParams,"name",t)},expression:"queryParams.name"}})],1),a("area-chain-selector",{ref:"queryAreaSelector",attrs:{regionId:e.queryParams.regionId,communityId:e.queryParams.communityId,residentialId:e.queryParams.residentialId,labels:{region:"行政区域",community:"社区",residential:"小区"},showCommunity:!0,showResidential:!0,inlineLayout:!0},on:{"update:regionId":function(t){return e.queryParams.regionId=t},"update:communityId":function(t){return e.queryParams.communityId=t},"update:residentialId":function(t){return e.queryParams.residentialId=t},"community-change":e.handleQuery,"region-change":e.handleRegionChange}}),a("el-form-item",{attrs:{label:"电话号码",prop:"phoneNumber"}},[a("el-input",{attrs:{placeholder:"请输入电话号码",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.phoneNumber,callback:function(t){e.$set(e.queryParams,"phoneNumber",t)},expression:"queryParams.phoneNumber"}})],1),a("el-form-item",{attrs:{label:"分类",prop:"category"}},[a("el-select",{attrs:{placeholder:"请选择分类",clearable:""},model:{value:e.queryParams.category,callback:function(t){e.$set(e.queryParams,"category",t)},expression:"queryParams.category"}},e._l(e.dict.type.community_phone_category,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",{attrs:{label:"审核状态",prop:"auditStatus"}},[a("el-select",{attrs:{placeholder:"请选择审核状态",clearable:""},model:{value:e.queryParams.auditStatus,callback:function(t){e.$set(e.queryParams,"auditStatus",t)},expression:"queryParams.auditStatus"}},[a("el-option",{attrs:{label:"待审核",value:"0"}}),a("el-option",{attrs:{label:"已通过",value:"1"}}),a("el-option",{attrs:{label:"已拒绝",value:"2"}})],1)],1),a("el-form-item",{attrs:{label:"状态",prop:"status"}},[a("el-select",{attrs:{placeholder:"请选择状态",clearable:""},model:{value:e.queryParams.status,callback:function(t){e.$set(e.queryParams,"status",t)},expression:"queryParams.status"}},[a("el-option",{attrs:{label:"正常",value:"1"}}),a("el-option",{attrs:{label:"停用",value:"0"}})],1)],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),a("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1),a("el-row",{staticClass:"mb8",attrs:{gutter:10}},[a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["operation:phone:add"],expression:"['operation:phone:add']"}],attrs:{type:"primary",plain:"",icon:"el-icon-plus",size:"mini"},on:{click:e.handleAdd}},[e._v("新增")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["operation:phone:edit"],expression:"['operation:phone:edit']"}],attrs:{type:"success",plain:"",icon:"el-icon-edit",size:"mini",disabled:e.single},on:{click:e.handleUpdate}},[e._v("修改")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["operation:phone:remove"],expression:"['operation:phone:remove']"}],attrs:{type:"danger",plain:"",icon:"el-icon-delete",size:"mini",disabled:e.multiple},on:{click:e.handleDelete}},[e._v("删除")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["operation:phone:export"],expression:"['operation:phone:export']"}],attrs:{type:"warning",plain:"",icon:"el-icon-download",size:"mini"},on:{click:e.handleExport}},[e._v("导出")])],1),a("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.phoneList},on:{"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"55",align:"center"}}),a("el-table-column",{attrs:{label:"编号",align:"center",prop:"id",width:"80"}}),a("el-table-column",{attrs:{label:"名称",align:"center",prop:"name",width:"150"}}),a("el-table-column",{attrs:{label:"电话号码",align:"center",prop:"phoneNumber",width:"130"}}),a("el-table-column",{attrs:{label:"分类",align:"center",prop:"category",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("dict-tag",{attrs:{options:e.dict.type.community_phone_category,value:t.row.category}})]}}])}),a("el-table-column",{attrs:{label:"行政区域",align:"center",prop:"regionId",formatter:e.regionFormatter,width:"120"}}),a("el-table-column",{attrs:{label:"社区",align:"center",prop:"communityId",formatter:e.communityFormatter,width:"120"}}),a("el-table-column",{attrs:{label:"小区",align:"center",prop:"residentialId",formatter:e.residentialFormatter,width:"120"}}),a("el-table-column",{attrs:{label:"提交用户",align:"center",prop:"submitUserId",formatter:e.userFormatter,width:"120"}}),a("el-table-column",{attrs:{label:"审核状态",align:"center",prop:"auditStatus",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[0==t.row.auditStatus?a("el-tag",{attrs:{type:"warning"}},[e._v("待审核")]):1==t.row.auditStatus?a("el-tag",{attrs:{type:"success"}},[e._v("已通过")]):2==t.row.auditStatus?a("el-tag",{attrs:{type:"danger"}},[e._v("已拒绝")]):e._e()]}}])}),a("el-table-column",{attrs:{label:"状态",align:"center",prop:"status",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[1==t.row.status?a("el-tag",{attrs:{type:"success"}},[e._v("正常")]):a("el-tag",{attrs:{type:"info"}},[e._v("停用")])]}}])}),a("el-table-column",{attrs:{label:"拨打次数",align:"center",prop:"callCount",width:"100"}}),a("el-table-column",{attrs:{label:"创建时间",align:"center",prop:"createTime",width:"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(e.parseTime(t.row.createTime,"{y}-{m}-{d} {h}:{i}:{s}")))])]}}])}),a("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width",width:"200"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["operation:phone:edit"],expression:"['operation:phone:edit']"}],attrs:{size:"mini",type:"text",icon:"el-icon-edit"},on:{click:function(a){return e.handleUpdate(t.row)}}},[e._v("编辑")]),0==t.row.auditStatus?a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["operation:phone:audit"],expression:"['operation:phone:audit']"}],attrs:{size:"mini",type:"text",icon:"el-icon-check"},on:{click:function(a){return e.handleAudit(t.row,1)}}},[e._v("通过")]):e._e(),0==t.row.auditStatus?a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["operation:phone:audit"],expression:"['operation:phone:audit']"}],attrs:{size:"mini",type:"text",icon:"el-icon-close"},on:{click:function(a){return e.handleAudit(t.row,2)}}},[e._v("拒绝")]):e._e(),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["operation:phone:remove"],expression:"['operation:phone:remove']"}],attrs:{size:"mini",type:"text",icon:"el-icon-delete"},on:{click:function(a){return e.handleDelete(t.row)}}},[e._v("删除")])]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}}),a("el-dialog",{attrs:{title:e.title,visible:e.open,width:"600px","append-to-body":""},on:{"update:visible":function(t){e.open=t}}},[a("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"100px"}},[a("el-form-item",{attrs:{label:"名称",prop:"name"}},[a("el-input",{attrs:{placeholder:"请输入名称"},model:{value:e.form.name,callback:function(t){e.$set(e.form,"name",t)},expression:"form.name"}})],1),a("el-form-item",{attrs:{label:"电话号码",prop:"phoneNumber"}},[a("el-input",{attrs:{placeholder:"请输入电话号码"},model:{value:e.form.phoneNumber,callback:function(t){e.$set(e.form,"phoneNumber",t)},expression:"form.phoneNumber"}})],1),a("area-chain-selector",{ref:"areaSelector",attrs:{regionId:e.form.regionId,communityId:e.form.communityId,residentialId:e.form.residentialId,isLoading:e.isLoadingFormData,showCommunity:!0,showResidential:!0},on:{"update:regionId":function(t){return e.form.regionId=t},"update:communityId":function(t){return e.form.communityId=t},"update:residentialId":function(t){return e.form.residentialId=t}}}),a("el-form-item",{attrs:{label:"分类",prop:"category"}},[a("el-select",{attrs:{placeholder:"请选择分类"},model:{value:e.form.category,callback:function(t){e.$set(e.form,"category",t)},expression:"form.category"}},e._l(e.dict.type.community_phone_category,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",{attrs:{label:"描述",prop:"description"}},[a("el-input",{attrs:{type:"textarea",placeholder:"请输入描述"},model:{value:e.form.description,callback:function(t){e.$set(e.form,"description",t)},expression:"form.description"}})],1),a("el-form-item",{attrs:{label:"排序",prop:"sort"}},[a("el-input-number",{attrs:{"controls-position":"right",min:0},model:{value:e.form.sort,callback:function(t){e.$set(e.form,"sort",t)},expression:"form.sort"}})],1),a("el-form-item",{attrs:{label:"状态",prop:"status"}},[a("el-radio-group",{model:{value:e.form.status,callback:function(t){e.$set(e.form,"status",t)},expression:"form.status"}},[a("el-radio",{attrs:{label:1}},[e._v("正常")]),a("el-radio",{attrs:{label:0}},[e._v("停用")])],1)],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),a("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1),a("el-dialog",{attrs:{title:"审核",visible:e.auditOpen,width:"500px","append-to-body":""},on:{"update:visible":function(t){e.auditOpen=t}}},[a("el-form",{ref:"auditForm",attrs:{model:e.auditForm,rules:e.auditRules,"label-width":"100px"}},[a("el-form-item",{attrs:{label:"审核结果",prop:"auditStatus"}},[a("el-radio-group",{model:{value:e.auditForm.auditStatus,callback:function(t){e.$set(e.auditForm,"auditStatus",t)},expression:"auditForm.auditStatus"}},[a("el-radio",{attrs:{label:1}},[e._v("通过")]),a("el-radio",{attrs:{label:2}},[e._v("拒绝")])],1)],1),a("el-form-item",{attrs:{label:"审核备注",prop:"auditRemark"}},[a("el-input",{attrs:{type:"textarea",placeholder:"请输入审核备注"},model:{value:e.auditForm.auditRemark,callback:function(t){e.$set(e.auditForm,"auditRemark",t)},expression:"auditForm.auditRemark"}})],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.submitAudit}},[e._v("确 定")]),a("el-button",{on:{click:e.cancelAudit}},[e._v("取 消")])],1)],1)],1)},n=[],o=a("5530"),i=(a("d81d"),a("d3b7"),a("0643"),a("a573"),a("b775"));function l(e){return Object(i["a"])({url:"/operation/phone/list",method:"get",params:e})}function s(e){return Object(i["a"])({url:"/operation/phone/"+e,method:"get"})}function u(e){return Object(i["a"])({url:"/operation/phone",method:"post",data:e})}function c(e){return Object(i["a"])({url:"/operation/phone",method:"put",data:e})}function m(e){return Object(i["a"])({url:"/operation/phone/"+e,method:"delete"})}function d(e,t,a){return Object(i["a"])({url:"/operation/phone/audit/"+e,method:"put",params:{auditStatus:t,auditRemark:a}})}var p=a("939b"),h=a("7db5"),f={name:"Phone",dicts:["community_phone_category"],components:{AreaChainSelector:p["a"]},data:function(){return{loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,phoneList:[],userOptions:[],title:"",open:!1,auditOpen:!1,queryParams:{pageNum:1,pageSize:10,name:null,phoneNumber:null,category:null,regionId:null,communityId:null,residentialId:null,auditStatus:null,status:null},form:{},auditForm:{},rules:{name:[{required:!0,message:"名称不能为空",trigger:"blur"}],phoneNumber:[{required:!0,message:"电话号码不能为空",trigger:"blur"},{pattern:/^1[3-9]\d{9}$|^0\d{2,3}-?\d{7,8}$/,message:"请输入正确的电话号码",trigger:"blur"}],category:[{required:!0,message:"分类不能为空",trigger:"change"}]},auditRules:{auditStatus:[{required:!0,message:"请选择审核结果",trigger:"change"}]},isLoadingFormData:!1}},created:function(){this.getList()},methods:{getList:function(){var e=this;this.loading=!0,l(this.queryParams).then((function(t){e.phoneList=t.rows,e.total=t.total,e.loading=!1}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={id:null,name:null,phoneNumber:null,category:null,description:null,regionId:null,communityId:null,residentialId:null,sort:0,status:1},this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.$refs.queryAreaSelector.reset(),this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.id})),this.single=1!==e.length,this.multiple=!e.length},handleAdd:function(){this.reset(),this.open=!0,this.title="添加电话"},handleUpdate:function(e){var t=this;this.reset();var a=e.id||this.ids;s(a).then((function(e){t.form=e.data,t.open=!0,t.title="修改电话"}))},submitForm:function(){var e=this;this.$refs["form"].validate((function(t){t&&(null!=e.form.id?c(e.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.open=!1,e.getList()})):u(e.form).then((function(t){e.$modal.msgSuccess("新增成功"),e.open=!1,e.getList()})))}))},handleDelete:function(e){var t=this,a=e.id||this.ids;this.$modal.confirm('是否确认删除编号为"'+a+'"的数据项？').then((function(){return m(a)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleExport:function(){this.download("operation/phone/export",Object(o["a"])({},this.queryParams),"phone_".concat((new Date).getTime(),".xlsx"))},handleAudit:function(e,t){this.auditForm={id:e.id,auditStatus:t,auditRemark:""},this.auditOpen=!0},submitAudit:function(){var e=this;this.$refs["auditForm"].validate((function(t){t&&d(e.auditForm.id,e.auditForm.auditStatus,e.auditForm.auditRemark).then((function(t){e.$modal.msgSuccess("审核成功"),e.auditOpen=!1,e.getList()}))}))},cancelAudit:function(){this.auditOpen=!1,this.auditForm={}},regionFormatter:h["b"],communityFormatter:h["a"],residentialFormatter:h["c"],userFormatter:function(e){return e.submitUserId||"-"},handleRegionChange:function(){this.queryParams.communityId=null,this.queryParams.residentialId=null}}},g=f,b=a("2877"),y=Object(b["a"])(g,r,n,!1,null,null,null);t["default"]=y.exports}}]);