<!DOCTYPE html><html><head><meta charset=utf-8><meta http-equiv=X-UA-Compatible content="IE=edge,chrome=1"><meta name=renderer content=webkit><meta name=viewport content="width=device-width,initial-scale=1,maximum-scale=1,user-scalable=no"><link rel=icon href=/favicon.ico><title>管理系统</title><!--[if lt IE 11]><script>window.location.href='/html/ie.html';</script><![endif]--><style>html,
    body,
    #app {
      height: 100%;
      margin: 0px;
      padding: 0px;
    }
    .chromeframe {
      margin: 0.2em 0;
      background: #ccc;
      color: #000;
      padding: 0.2em 0;
    }

    #loader-wrapper {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 999999;
    }

    #loader {
      display: block;
      position: relative;
      left: 50%;
      top: 50%;
      width: 150px;
      height: 150px;
      margin: -75px 0 0 -75px;
      border-radius: 50%;
      border: 3px solid transparent;
      border-top-color: #FFF;
      -webkit-animation: spin 2s linear infinite;
      -ms-animation: spin 2s linear infinite;
      -moz-animation: spin 2s linear infinite;
      -o-animation: spin 2s linear infinite;
      animation: spin 2s linear infinite;
      z-index: 1001;
    }

    #loader:before {
      content: "";
      position: absolute;
      top: 5px;
      left: 5px;
      right: 5px;
      bottom: 5px;
      border-radius: 50%;
      border: 3px solid transparent;
      border-top-color: #FFF;
      -webkit-animation: spin 3s linear infinite;
      -moz-animation: spin 3s linear infinite;
      -o-animation: spin 3s linear infinite;
      -ms-animation: spin 3s linear infinite;
      animation: spin 3s linear infinite;
    }

    #loader:after {
      content: "";
      position: absolute;
      top: 15px;
      left: 15px;
      right: 15px;
      bottom: 15px;
      border-radius: 50%;
      border: 3px solid transparent;
      border-top-color: #FFF;
      -moz-animation: spin 1.5s linear infinite;
      -o-animation: spin 1.5s linear infinite;
      -ms-animation: spin 1.5s linear infinite;
      -webkit-animation: spin 1.5s linear infinite;
      animation: spin 1.5s linear infinite;
    }


    @-webkit-keyframes spin {
      0% {
        -webkit-transform: rotate(0deg);
        -ms-transform: rotate(0deg);
        transform: rotate(0deg);
      }
      100% {
        -webkit-transform: rotate(360deg);
        -ms-transform: rotate(360deg);
        transform: rotate(360deg);
      }
    }

    @keyframes spin {
      0% {
        -webkit-transform: rotate(0deg);
        -ms-transform: rotate(0deg);
        transform: rotate(0deg);
      }
      100% {
        -webkit-transform: rotate(360deg);
        -ms-transform: rotate(360deg);
        transform: rotate(360deg);
      }
    }


    #loader-wrapper .loader-section {
      position: fixed;
      top: 0;
      width: 51%;
      height: 100%;
      background: #7171C6;
      z-index: 1000;
      -webkit-transform: translateX(0);
      -ms-transform: translateX(0);
      transform: translateX(0);
    }

    #loader-wrapper .loader-section.section-left {
      left: 0;
    }

    #loader-wrapper .loader-section.section-right {
      right: 0;
    }


    .loaded #loader-wrapper .loader-section.section-left {
      -webkit-transform: translateX(-100%);
      -ms-transform: translateX(-100%);
      transform: translateX(-100%);
      -webkit-transition: all 0.7s 0.3s cubic-bezier(0.645, 0.045, 0.355, 1.000);
      transition: all 0.7s 0.3s cubic-bezier(0.645, 0.045, 0.355, 1.000);
    }

    .loaded #loader-wrapper .loader-section.section-right {
      -webkit-transform: translateX(100%);
      -ms-transform: translateX(100%);
      transform: translateX(100%);
      -webkit-transition: all 0.7s 0.3s cubic-bezier(0.645, 0.045, 0.355, 1.000);
      transition: all 0.7s 0.3s cubic-bezier(0.645, 0.045, 0.355, 1.000);
    }

    .loaded #loader {
      opacity: 0;
      -webkit-transition: all 0.3s ease-out;
      transition: all 0.3s ease-out;
    }

    .loaded #loader-wrapper {
      visibility: hidden;
      -webkit-transform: translateY(-100%);
      -ms-transform: translateY(-100%);
      transform: translateY(-100%);
      -webkit-transition: all 0.3s 1s ease-out;
      transition: all 0.3s 1s ease-out;
    }

    .no-js #loader-wrapper {
      display: none;
    }

    .no-js h1 {
      color: #222222;
    }

    #loader-wrapper .load_title {
      font-family: 'Open Sans';
      color: #FFF;
      font-size: 19px;
      width: 100%;
      text-align: center;
      z-index: 9999999999999;
      position: absolute;
      top: 60%;
      opacity: 1;
      line-height: 30px;
    }

    #loader-wrapper .load_title span {
      font-weight: normal;
      font-style: italic;
      font-size: 13px;
      color: #FFF;
      opacity: 0.5;
    }</style><link href=/static/css/chunk-libs.20020923.css rel=stylesheet><link href=/static/css/app.9b60af5e.css rel=stylesheet></head><body><div id=app><div id=loader-wrapper><div id=loader></div><div class="loader-section section-left"></div><div class="loader-section section-right"></div><div class=load_title>正在加载系统资源，请耐心等待</div></div></div><script>(function(c){function e(e){for(var d,u,a=e[0],k=e[1],t=e[2],r=0,b=[];r<a.length;r++)u=a[r],Object.prototype.hasOwnProperty.call(f,u)&&f[u]&&b.push(f[u][0]),f[u]=0;for(d in k)Object.prototype.hasOwnProperty.call(k,d)&&(c[d]=k[d]);o&&o(e);while(b.length)b.shift()();return h.push.apply(h,t||[]),n()}function n(){for(var c,e=0;e<h.length;e++){for(var n=h[e],d=!0,u=1;u<n.length;u++){var a=n[u];0!==f[a]&&(d=!1)}d&&(h.splice(e--,1),c=k(k.s=n[0]))}return c}var d={},u={runtime:0},f={runtime:0},h=[];function a(c){return k.p+"static/js/"+({}[c]||c)+"."+{"chunk-005cb0c7":"7a8194c3","chunk-00d8363c":"ef93429f","chunk-0238e9b0":"9a0c3e62","chunk-04621586":"61605535","chunk-07d2e1b3":"2ba2d6ba","chunk-25ccdf6b":"4d5a9ff7","chunk-2d0d38ff":"bfe9b56b","chunk-2d0de3b1":"23a2795c","chunk-2d212b99":"eadf8bea","chunk-3a083b9c":"bb870350","chunk-93d1cd2c":"7a36bf36","chunk-08fa3263":"84cfc2f3","chunk-23a944c4":"62f73a3f","chunk-3012529b":"d3eb8fe3","chunk-33805972":"a9f4db47","chunk-832e1072":"53c22c64","chunk-a599107a":"3dfb8a8d","chunk-e67b3980":"bb3ad269","chunk-ec569262":"736e1e47","chunk-09b7e834":"dfcc27dc","chunk-13730932":"4462cbab","chunk-1d7d97ec":"553344c8","chunk-1457f509":"46f48605","chunk-2d0ae5b5":"a6d27e4f","chunk-2d0e5451":"783276ad","chunk-2d21d4a1":"24c88a41","chunk-2d2302a4":"64afc280","chunk-68596c47":"8f7df79e","chunk-2b02de32":"0cb7713a","chunk-b193f0ee":"0c462319","chunk-210ca3e9":"8151b677","chunk-210ce324":"e438298f","chunk-2727631f":"3ce5b395","chunk-2d0b1626":"4d3d6eaf","chunk-2d0b2b28":"6267aaf1","chunk-5a9bfc5d":"83177d3e","chunk-2d0bce05":"ff5a42f8","chunk-2d0c5b17":"ec21ae66","chunk-2d0c8e18":"d6e41c9c","chunk-2d0d6e56":"73e8a642","chunk-2d0da2ea":"65ad5903","chunk-2d0e4a45":"1cd75bd1","chunk-2d0efd67":"48725c5e","chunk-2d0f012d":"ddeb06bb","chunk-2d2088c6":"711c7c62","chunk-2d20955d":"dd97051b","chunk-54319dd2":"a2a91234","chunk-31eae13f":"3dd39974","chunk-4df90d92":"a10947d9","chunk-0d5b0085":"84230dae","chunk-60006966":"30c2ddcf","chunk-f44454cc":"5b6a91ad","chunk-2d216f19":"cf47d4bb","chunk-2d21b8fa":"318101df","chunk-2d22252c":"d2952384","chunk-2d2304f6":"b4605185","chunk-2d230898":"68570dd4","chunk-32d2ce40":"6ae92414","chunk-35800316":"2f007bff","chunk-39413ce8":"8f3d47e3","chunk-3a08d90c":"64fc19a8","chunk-3b69bc00":"b6855086","chunk-46f2cf5c":"252197c1","chunk-4f55a4ac":"c56566d6","chunk-54575c72":"7a431a2f","chunk-53791da3":"9df39f2e","chunk-ed37dce4":"7680d355","chunk-582b2a7a":"3937f05a","chunk-5b83c289":"a41660f1","chunk-27d58c84":"42fc8ffa","chunk-620a5194":"f9e67dfc","chunk-6746b265":"ae26177b","chunk-68702101":"20f468f0","chunk-7e203972":"b136db0d","chunk-8579d4da":"26de00e0","chunk-8ee3fc10":"f65c3da5","chunk-a42c1168":"9eae36c0","chunk-d19c1a98":"0f88b3e0","chunk-da26725e":"b276e36d","chunk-e1a6d904":"8dfd2f10","chunk-e648d5fe":"ffd488f9"}[c]+".js"}function k(e){if(d[e])return d[e].exports;var n=d[e]={i:e,l:!1,exports:{}};return c[e].call(n.exports,n,n.exports,k),n.l=!0,n.exports}k.e=function(c){var e=[],n={"chunk-00d8363c":1,"chunk-07d2e1b3":1,"chunk-25ccdf6b":1,"chunk-93d1cd2c":1,"chunk-08fa3263":1,"chunk-3012529b":1,"chunk-33805972":1,"chunk-832e1072":1,"chunk-13730932":1,"chunk-68596c47":1,"chunk-b193f0ee":1,"chunk-5a9bfc5d":1,"chunk-4df90d92":1,"chunk-f44454cc":1,"chunk-35800316":1,"chunk-46f2cf5c":1,"chunk-4f55a4ac":1,"chunk-53791da3":1,"chunk-ed37dce4":1,"chunk-5b83c289":1,"chunk-620a5194":1,"chunk-6746b265":1,"chunk-a42c1168":1,"chunk-da26725e":1,"chunk-e648d5fe":1};u[c]?e.push(u[c]):0!==u[c]&&n[c]&&e.push(u[c]=new Promise((function(e,n){for(var d="static/css/"+({}[c]||c)+"."+{"chunk-005cb0c7":"31d6cfe0","chunk-00d8363c":"a00b75fd","chunk-0238e9b0":"31d6cfe0","chunk-04621586":"31d6cfe0","chunk-07d2e1b3":"84f98409","chunk-25ccdf6b":"ecea2c5f","chunk-2d0d38ff":"31d6cfe0","chunk-2d0de3b1":"31d6cfe0","chunk-2d212b99":"31d6cfe0","chunk-3a083b9c":"31d6cfe0","chunk-93d1cd2c":"3ee89ce5","chunk-08fa3263":"9d5fd8ea","chunk-23a944c4":"31d6cfe0","chunk-3012529b":"3fe50d8f","chunk-33805972":"ac9a21de","chunk-832e1072":"c7bbfcd6","chunk-a599107a":"31d6cfe0","chunk-e67b3980":"31d6cfe0","chunk-ec569262":"31d6cfe0","chunk-09b7e834":"31d6cfe0","chunk-13730932":"b9229cd2","chunk-1d7d97ec":"31d6cfe0","chunk-1457f509":"31d6cfe0","chunk-2d0ae5b5":"31d6cfe0","chunk-2d0e5451":"31d6cfe0","chunk-2d21d4a1":"31d6cfe0","chunk-2d2302a4":"31d6cfe0","chunk-68596c47":"3f7fa7da","chunk-2b02de32":"31d6cfe0","chunk-b193f0ee":"f877ff8e","chunk-210ca3e9":"31d6cfe0","chunk-210ce324":"31d6cfe0","chunk-2727631f":"31d6cfe0","chunk-2d0b1626":"31d6cfe0","chunk-2d0b2b28":"31d6cfe0","chunk-5a9bfc5d":"f59a1d86","chunk-2d0bce05":"31d6cfe0","chunk-2d0c5b17":"31d6cfe0","chunk-2d0c8e18":"31d6cfe0","chunk-2d0d6e56":"31d6cfe0","chunk-2d0da2ea":"31d6cfe0","chunk-2d0e4a45":"31d6cfe0","chunk-2d0efd67":"31d6cfe0","chunk-2d0f012d":"31d6cfe0","chunk-2d2088c6":"31d6cfe0","chunk-2d20955d":"31d6cfe0","chunk-54319dd2":"31d6cfe0","chunk-31eae13f":"31d6cfe0","chunk-4df90d92":"6dfe926d","chunk-0d5b0085":"31d6cfe0","chunk-60006966":"31d6cfe0","chunk-f44454cc":"c5292c00","chunk-2d216f19":"31d6cfe0","chunk-2d21b8fa":"31d6cfe0","chunk-2d22252c":"31d6cfe0","chunk-2d2304f6":"31d6cfe0","chunk-2d230898":"31d6cfe0","chunk-32d2ce40":"31d6cfe0","chunk-35800316":"db631dbc","chunk-39413ce8":"31d6cfe0","chunk-3a08d90c":"31d6cfe0","chunk-3b69bc00":"31d6cfe0","chunk-46f2cf5c":"17fbdb6b","chunk-4f55a4ac":"5a402cd2","chunk-54575c72":"31d6cfe0","chunk-53791da3":"78b49577","chunk-ed37dce4":"78b49577","chunk-582b2a7a":"31d6cfe0","chunk-5b83c289":"ce2a2394","chunk-27d58c84":"31d6cfe0","chunk-620a5194":"8e2789eb","chunk-6746b265":"3e10cd59","chunk-68702101":"31d6cfe0","chunk-7e203972":"31d6cfe0","chunk-8579d4da":"31d6cfe0","chunk-8ee3fc10":"31d6cfe0","chunk-a42c1168":"c15eb1aa","chunk-d19c1a98":"31d6cfe0","chunk-da26725e":"0351f658","chunk-e1a6d904":"31d6cfe0","chunk-e648d5fe":"bbc9fa95"}[c]+".css",f=k.p+d,h=document.getElementsByTagName("link"),a=0;a<h.length;a++){var t=h[a],r=t.getAttribute("data-href")||t.getAttribute("href");if("stylesheet"===t.rel&&(r===d||r===f))return e()}var b=document.getElementsByTagName("style");for(a=0;a<b.length;a++){t=b[a],r=t.getAttribute("data-href");if(r===d||r===f)return e()}var o=document.createElement("link");o.rel="stylesheet",o.type="text/css",o.onload=e,o.onerror=function(e){var d=e&&e.target&&e.target.src||f,h=new Error("Loading CSS chunk "+c+" failed.\n("+d+")");h.code="CSS_CHUNK_LOAD_FAILED",h.request=d,delete u[c],o.parentNode.removeChild(o),n(h)},o.href=f;var i=document.getElementsByTagName("head")[0];i.appendChild(o)})).then((function(){u[c]=0})));var d=f[c];if(0!==d)if(d)e.push(d[2]);else{var h=new Promise((function(e,n){d=f[c]=[e,n]}));e.push(d[2]=h);var t,r=document.createElement("script");r.charset="utf-8",r.timeout=120,k.nc&&r.setAttribute("nonce",k.nc),r.src=a(c);var b=new Error;t=function(e){r.onerror=r.onload=null,clearTimeout(o);var n=f[c];if(0!==n){if(n){var d=e&&("load"===e.type?"missing":e.type),u=e&&e.target&&e.target.src;b.message="Loading chunk "+c+" failed.\n("+d+": "+u+")",b.name="ChunkLoadError",b.type=d,b.request=u,n[1](b)}f[c]=void 0}};var o=setTimeout((function(){t({type:"timeout",target:r})}),12e4);r.onerror=r.onload=t,document.head.appendChild(r)}return Promise.all(e)},k.m=c,k.c=d,k.d=function(c,e,n){k.o(c,e)||Object.defineProperty(c,e,{enumerable:!0,get:n})},k.r=function(c){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(c,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(c,"__esModule",{value:!0})},k.t=function(c,e){if(1&e&&(c=k(c)),8&e)return c;if(4&e&&"object"===typeof c&&c&&c.__esModule)return c;var n=Object.create(null);if(k.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:c}),2&e&&"string"!=typeof c)for(var d in c)k.d(n,d,function(e){return c[e]}.bind(null,d));return n},k.n=function(c){var e=c&&c.__esModule?function(){return c["default"]}:function(){return c};return k.d(e,"a",e),e},k.o=function(c,e){return Object.prototype.hasOwnProperty.call(c,e)},k.p="/",k.oe=function(c){throw console.error(c),c};var t=window["webpackJsonp"]=window["webpackJsonp"]||[],r=t.push.bind(t);t.push=e,t=t.slice();for(var b=0;b<t.length;b++)e(t[b]);var o=r;n()})([]);</script><script src=/static/js/chunk-elementUI.ef7743f1.js></script><script src=/static/js/chunk-libs.dae67dc7.js></script><script src=/static/js/app.3b3a6896.js></script></body></html>