// pages/user/userProfile/userProfile.js
const app = getApp()
const api = require('../../../config/api')
const userUtils = require('../../../utils/user')
const util = require('../../../utils/util')
const systemInfoService = require('../../../services/systemInfo.js')

Page({
    data: {
        userId: null,
        userInfo: {},
        productList: [],
        activityList: [], // 用户发布的活动列表
        isFollowed: false,
        followCount: 0,
        fansCount: 0,
        productCount: 0,
        activityCount: 0, // 活动数量
        loading: false,
        hasLogin: false,
        activeTab: 'product', // 当前激活的标签页：'product'或'activity'
    },

    async onLoad(options) {
        if (options.userId) {
            this.setData({
                userId: parseInt(options.userId),
                hasLogin: app.globalData.hasLogin
            })

            // 加载用户信息
            this.loadUserInfo()

            // 加载用户商品列表
            this.loadUserProducts()

        } else {
            wx.showToast({
                title: '参数错误',
                icon: 'none'
            })
            setTimeout(() => {
                wx.navigateBack()
            }, 1500)
        }
    },

    // 加载用户信息
    async loadUserInfo() {
        this.setData({
            loading: true
        })

        try {
            const res = await api.getUserPublicInfo(this.data.userId)
            
            if (res.code === 200 && res.data) {
                const userData = res.data;

                // 使用系统信息服务处理用户头像
                userData.avatar = await systemInfoService.processImageUrl(userData.avatar);

                this.setData({
                    userInfo: userData,
                    isFollowed: userData.isFollowed,
                    followCount: userData.followCount,
                    fansCount: userData.fansCount,
                    loading: false
                })
            } else {
                throw new Error(res.msg || '获取用户信息失败')
            }
        } catch (err) {
            console.error('获取用户信息失败：', err)
            wx.showToast({
                title: '获取用户信息失败',
                icon: 'none'
            })
            this.setData({
                loading: false
            })
        }
    },

    // 加载用户商品列表
    async loadUserProducts() {
        try {
            const res = await api.getProductList({
                userId: this.data.userId,
                status: 1 // 在售状态
            })
            
            let productList = res.rows || []

            // 使用系统信息服务处理图片
            productList = await Promise.all(productList.map(async item => {
                // 处理商品图片 - 获取第一张图片
                if (item.images && typeof item.images === 'string') {
                    const imageResult = await systemInfoService.processImageUrls(item.images)
                    item.firstImage = imageResult.imageUrl
                }

                // 处理用户头像
                item.avatar = await systemInfoService.processImageUrl(item.avatar)

                // 确保有用户名
                if (!item.nickname) {
                    item.nickname = '趣换用户'
                }

                return item
            }))

            this.setData({
                productList: productList,
                productCount: res.total || 0
            })
        } catch (err) {
            console.error('获取用户商品列表失败：', err)
        }
    },

    // 关注或取消关注
    toggleFollow() {
        if (!this.data.hasLogin) {
            wx.showToast({
                title: '请先登录',
                icon: 'none'
            })
            return
        }

        api.toggleFollow(this.data.userId)
            .then(res => {
                wx.showToast({
                    title: res.msg,
                    icon: 'success'
                })

                // 更新关注状态
                this.setData({
                    isFollowed: !this.data.isFollowed
                })

                // 重新加载用户信息
                this.loadUserInfo()
            })
            .catch(err => {
                console.error('操作失败：', err)
                wx.showToast({
                    title: err.msg,
                    icon: 'none'
                })
            })
    },

    // 查看商品详情
    viewProduct(event) {
        const productId = event.currentTarget.dataset.id
        wx.navigateTo({
            url: `/pages/item/detail?id=${productId}`
        })
    },

    // 查看关注列表
    viewFollowList() {
        wx.navigateTo({
            url: `/pages/user/follow/follow?userId=${this.data.userId}`
        })
    },

    // 查看粉丝列表
    viewFansList() {
        wx.navigateTo({
            url: `/pages/user/follow/follow?userId=${this.data.userId}&tab=fans`
        })
    },

    // 加载用户活动列表
    async loadUserActivities() {
        try {
            const res = await api.getUserActivities(this.data.userId, {
                status: '1' // 只显示发布中的活动
            })
            
            let activityList = res.rows || []

            // 处理活动数据
            if (activityList.length > 0) {
                activityList = await Promise.all(activityList.map(async item => {
                    // 处理活动图片 - 使用系统信息服务获取第一张图片
                    if (item.images && typeof item.images === 'string') {
                        const imageResult = await systemInfoService.processImageUrls(item.images)
                        item.imageUrl = imageResult.imageUrl
                    }

                    // 格式化开始时间
                    if (item.startTime) {
                        const startTime = new Date(item.startTime)
                        item.formattedStartTime = `${startTime.getMonth() + 1}月${startTime.getDate()}日 ${startTime.getHours()}:${startTime.getMinutes().toString().padStart(2, '0')}`
                    }

                    // 处理地点信息
                    if (item.location) {
                        // 如果location是POINT格式，提取坐标
                        if (item.location.startsWith('POINT')) {
                            const match = item.location.match(/POINT\(([^ ]+) ([^)]+)\)/)
                            if (match) {
                                item.longitude = match[1]
                                item.latitude = match[2]
                            }
                        }

                        // 如果有locationName则使用，否则显示"查看地点"
                        if (!item.locationName) {
                            item.locationName = '查看地点'
                        }
                    }

                    // 确保状态名称正确
                    if (item.status === '1') {
                        item.statusName = item.statusName || '进行中'
                    } else if (item.status === '2') {
                        item.statusName = item.statusName || '已结束'
                    } else if (item.status === '0') {
                        item.statusName = item.statusName || '未开始'
                    }

                    // 处理参与人数
                    if (!item.participantCount && item.participantCount !== 0) {
                        // 如果后端没有返回参与人数，尝试从participants数组获取
                        if (item.participants && Array.isArray(item.participants)) {
                            item.participantCount = item.participants.length
                        } else {
                            item.participantCount = 0
                        }
                    }

                    return item
                }))
            }

            this.setData({
                activityList: activityList,
                activityCount: res.total || 0
            })
        } catch (err) {
            console.error('获取用户活动列表失败：', err)
        }
    },

    // 查看活动详情
    viewActivity(event) {
        const activityId = event.currentTarget.dataset.id
        wx.navigateTo({
            url: `/pages/activity/detail/index?id=${activityId}`
        })
    },

    // 切换标签页
    switchTab(event) {
        const tab = event.currentTarget.dataset.tab
        this.setData({
            activeTab: tab
        })

        // 如果切换到活动选项卡，并且活动列表为空，则加载活动列表
        if (tab === 'activity' && this.data.activityList.length === 0) {
            this.loadUserActivities()
        }
    },

    // 自定义分享内容
    onShareAppMessage() {
        const userInfo = this.data.userInfo
        return {
            title: `分享给你一个超有趣的用户,快来看看吧`,
            path: `/pages/user/userProfile/userProfile?userId=${this.data.userId}`
        }
    },

    // 分享到朋友圈
    onShareTimeline() {
        const userInfo = this.data.userInfo
        return {
            title: `分享给你一个超有趣的用户,快来看看吧`,
            query: `userId=${this.data.userId}`
        }
    }
})
