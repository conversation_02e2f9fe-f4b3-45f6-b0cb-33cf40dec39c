package com.fs.swap.common.enums;

/**
 * 字典类型
 */
public enum DictType implements BaseEnum {
    ACTIVITY_CATEGORY("activity_category"),
    
    // 社区服务相关字典类型
    COMMUNITY_SERVICE_CATEGORY("community_service_category"),
    COMMUNITY_SERVICE_TAG("community_service_tag"),
    COMMUNITY_SERVICE_AUDIT_STATUS("community_service_audit_status"),
    // 社区电话相关字典类型
    COMMUNITY_PHONE_CATEGORY("community_phone_category"),
    COMMUNITY_HELP_REQUIRE_CATEGORIES("community_help_require_categories"),
    COMMUNITY_HELP_SERVICE_CATEGORIES("community_help_service_categories"),
    COMMUNITY_HELP_PUBLISH_TYPE("community_help_publish_type"),
    // 排行榜奖励类型
    RANKING_REWARD_TYPE("ranking_reward_type"),
    SILVER_EVENT_TYPE("silver_event_type"),
    ;

    private final String type;

    DictType(String type) {
        this.type = type;
    }

    @Override
    public String val() {
        return type;
    }
}
