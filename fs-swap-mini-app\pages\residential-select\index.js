const residentialService = require('../../services/residential');
const localResidentialService = require('../../services/localResidential');
const app = getApp();

Page({
  data: {
    mapContext: null,
    markers: [],
    loading: false,
    error: false,
    errorMessage: '',
    distance: null,
    distanceText: '',
    currentLocation: null,
    residential: null,
    // 来源页面信息
    fromPage: '',
    fromParams: {}
  },

  onLoad(options) {
    console.log('小区选择页面加载，参数:', options);
    
    // 保存来源页面信息
    if (options.from) {
      this.setData({
        fromPage: decodeURIComponent(options.from),
        fromParams: options.fromParams ? JSON.parse(decodeURIComponent(options.fromParams)) : {}
      });
    }
    
    // 检查是否有传入的小区信息
    if (options.residentialId && options.residentialName) {
      // 有传入的小区信息，直接使用
      const residential = {
        id: parseInt(options.residentialId),
        name: decodeURIComponent(options.residentialName),
        address: options.residentialAddress ? decodeURIComponent(options.residentialAddress) : '',
        location: options.residentialLocation ? decodeURIComponent(options.residentialLocation) : ''
      };
      
      console.log('使用传入的小区信息:', residential);
      
      // 设置小区信息并更新地图
      this.setData({
        residential: residential
      }, () => {
        this.updateMapMarkers(residential);
        this.calculateResidentialDistance(residential);
        setTimeout(() => {
          this.initMap();
        }, 300);
      });
    } else {
      // 没有传入小区信息，获取附近小区
      this.getNearbyResidential();
    }
  },

  onShow() {
    // 如果已经有小区信息，更新地图
    if (this.data.residential) {
      this.updateMapMarkers(this.data.residential);
      this.calculateResidentialDistance(this.data.residential);
      setTimeout(() => {
        this.initMap();
      }, 300);
    }
  },

  // 获取附近小区
  async getNearbyResidential() {
    // 开始加载
    this.setData({
      loading: true,
      error: false,
      errorMessage: ''
    });

    try {
      // 使用服务层方法获取附近小区
      const nearbyResidential = await residentialService.getNearbyResidential();

      if (nearbyResidential) {
        // 设置小区信息并立即更新地图标记
        this.setData({
          residential: nearbyResidential
        }, () => {
          // 在setData回调中更新地图标记，确保数据已更新
          this.updateMapMarkers(nearbyResidential);
          this.calculateResidentialDistance(nearbyResidential);
          
          // 确保地图已经渲染后再设置中心点
          setTimeout(() => {
            this.initMap();
          }, 300);
        });
      } else {
        // 如果没有获取到附近小区，跳转到小区列表页面
        console.log('未找到附近小区，跳转到小区列表页面');
        this.navigateToResidentialList();
      }
    } catch (error) {
      console.error('获取附近小区失败:', error);
      this.setData({
        error: true,
        errorMessage: '获取附近小区失败，请手动选择'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  // 初始化地图
  initMap() {
    if (!this.data.mapContext) {
      this.data.mapContext = wx.createMapContext('residentialMap', this);
    }

    if (this.data.residential && this.data.markers.length > 0) {
      const marker = this.data.markers[0];
      this.data.mapContext.moveToLocation({
        longitude: marker.longitude,
        latitude: marker.latitude
      });
    }
  },

  // 更新地图标记
  updateMapMarkers(residential) {
    if (!residential || !residential.location) {
      console.log('小区信息或位置信息缺失:', residential);
      return;
    }

    // 使用服务层方法创建标记
    const marker = residentialService.createMarker(residential);
    console.log('创建的地图标记:', marker);

    if (marker) {
      this.setData({ 
        markers: [marker] 
      }, () => {
        console.log('地图标记已更新:', this.data.markers);
      });
    } else {
      console.log('创建地图标记失败');
    }
  },

  // 确认选择小区
  async onConfirm() {
    if (!this.data.residential) return;

    // 设置加载状态
    this.setData({ loading: true });

    try {
      if (app.globalData.hasLogin) {
        // 已登录用户：使用原有的服务器选择小区逻辑
        const success = await residentialService.selectResidential(this.data.residential);
        if (success) {
          this.handleSelectSuccess();
        }
      } else {
        // 未登录用户：保存到本地存储
        const success = localResidentialService.setLocalResidential(this.data.residential);
        if (success) {
          this.handleSelectSuccess();
        } else {
          wx.showToast({
            title: '小区选择失败',
            icon: 'none'
          });
        }
      }
    } catch (error) {
      wx.showToast({
        title: '小区选择失败',
        icon: 'none'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  // 处理选择成功
  handleSelectSuccess() {
    wx.showToast({
      title: '小区选择成功',
      icon: 'success',
      duration: 1500
    });

    // 通知app小区状态变更
    if (app && app.notifyResidentialChange) {
      app.notifyResidentialChange(this.data.residential);
    }

    // 直接跳回首页
    wx.switchTab({
      url: '/pages/index/index'
    });
  },

  // 选择其他小区
  onCancel() {
    // 始终跳转到小区列表页面，让用户重新选择
    this.navigateToResidentialList();
  },

  // 跳转到小区列表页面
  navigateToResidentialList() {
    // 构建跳转参数
    let url = '/pages/residential/index';
    const params = [];
    
    // 如果有来源页面信息，传递过去
    if (this.data.fromPage) {
      params.push(`from=${encodeURIComponent(this.data.fromPage)}`);
    }
    if (this.data.fromParams && Object.keys(this.data.fromParams).length > 0) {
      params.push(`fromParams=${encodeURIComponent(JSON.stringify(this.data.fromParams))}`);
    }
    
    if (params.length > 0) {
      url += '?' + params.join('&');
    }
    
    wx.navigateTo({
      url: url
    });
  },

  // 返回来源页面
  navigateBack() {
    if (this.data.fromPage) {
      let url = this.data.fromPage;
      if (this.data.fromParams && Object.keys(this.data.fromParams).length > 0) {
        const params = Object.keys(this.data.fromParams)
          .map(key => `${key}=${this.data.fromParams[key]}`)
          .join('&');
        url += (url.includes('?') ? '&' : '?') + params;
      }
      
      wx.redirectTo({
        url: url,
        fail: () => {
          // 如果跳转失败，返回首页
          wx.switchTab({
            url: '/pages/index/index'
          });
        }
      });
    } else {
      // 没有来源页面，返回首页
      wx.switchTab({
        url: '/pages/index/index'
      });
    }
  },

  // 计算小区距离
  async calculateResidentialDistance(residential) {
    if (!residential || !residential.location) return;

    try {
      // 获取当前位置
      let currentLocation = this.data.currentLocation;
      if (!currentLocation) {
        currentLocation = await residentialService.getCurrentLocation();
        this.setData({ currentLocation });
      }

      // 解析小区位置
      const residentialLocation = residentialService.parseLocation(residential.location);
      if (!residentialLocation) return;

      // 计算距离
      const distance = residentialService.calculateDistance(
        currentLocation.latitude,
        currentLocation.longitude,
        residentialLocation.latitude,
        residentialLocation.longitude
      );

      this.setData({
        distance: distance,
        distanceText: residentialService.formatDistance(distance)
      });
    } catch (error) {
      console.warn('计算距离失败:', error);
      // 计算失败时静默处理，不影响组件的其他功能
    }
  },

  // 测试地图功能（开发调试用）
  testMapFunction() {
    console.log('测试地图功能');
    console.log('当前数据状态:', {
      residential: this.data.residential,
      markers: this.data.markers,
      loading: this.data.loading,
      error: this.data.error
    });
    
    // 如果有小区信息，重新创建地图标记
    if (this.data.residential) {
      console.log('重新创建地图标记');
      this.updateMapMarkers(this.data.residential);
    }
  }
}); 