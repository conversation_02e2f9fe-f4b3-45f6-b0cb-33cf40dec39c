(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-ec569262"],{"7db5":function(e,t,a){"use strict";a.d(t,"b",(function(){return n})),a.d(t,"a",(function(){return l})),a.d(t,"c",(function(){return i}));a("b0c0"),a("a9e3");var r=a("a04a");function n(e,t,a){if(!a)return"-";try{if(0===r["a"].getRegions().length)return"".concat(a);var n=r["a"].getRegionFullName(a);if(n)return n;var l=r["a"].getRegionById(a);return l?l.name:"".concat(a)}catch(i){return"".concat(a)}}function l(e,t,a){if(!a)return"-";try{if(0===r["a"].getCommunities().length)return"".concat(a);var n=r["a"].getCommunityById(a);return n?n.name:"".concat(a)}catch(l){return"".concat(a)}}function i(e,t,a){if(!a)return"-";try{if(0===r["a"].getResidentials().length)return"".concat(a);var n=r["a"].getResidentialById(a);return n?n.name:"".concat(a)}catch(l){return"".concat(a)}}},a29b:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",attrs:{model:e.queryParams,size:"small",inline:!0,"label-width":"68px"}},[a("area-chain-selector",{ref:"queryAreaSelector",attrs:{regionId:e.queryParams.regionId,communityId:e.queryParams.communityId,labels:{region:"行政区域",community:"社区",residential:"小区"},placeholders:{region:"请选择所属行政区域",community:"请选择社区"},showCommunity:!0,showResidential:!1},on:{"update:regionId":function(t){return e.queryParams.regionId=t},"update:communityId":function(t){return e.queryParams.communityId=t},"community-change":e.handleQuery,"region-change":e.handleRegionChange}}),a("el-form-item",{attrs:{label:"小区名称",prop:"name"}},[a("el-input",{attrs:{placeholder:"请输入小区名称",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.name,callback:function(t){e.$set(e.queryParams,"name",t)},expression:"queryParams.name"}})],1),a("el-form-item",{attrs:{label:"别名",prop:"alias"}},[a("el-input",{attrs:{placeholder:"请输入别名",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.alias,callback:function(t){e.$set(e.queryParams,"alias",t)},expression:"queryParams.alias"}})],1),a("el-form-item",{attrs:{label:"详细地址",prop:"address"}},[a("el-input",{attrs:{placeholder:"请输入详细地址",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.address,callback:function(t){e.$set(e.queryParams,"address",t)},expression:"queryParams.address"}})],1),a("el-form-item",{attrs:{label:"坐标",prop:"location"}},[a("el-input",{attrs:{placeholder:"请输入坐标",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.location,callback:function(t){e.$set(e.queryParams,"location",t)},expression:"queryParams.location"}})],1),a("el-form-item",{attrs:{label:"状态",prop:"status"}},[a("el-select",{attrs:{placeholder:"请选择状态",clearable:""},model:{value:e.queryParams.status,callback:function(t){e.$set(e.queryParams,"status",t)},expression:"queryParams.status"}},[a("el-option",{attrs:{label:"正常",value:1}}),a("el-option",{attrs:{label:"停用",value:0}})],1)],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),a("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1),a("el-row",{staticClass:"mb8",attrs:{gutter:10}},[a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:area:add"],expression:"['system:area:add']"}],attrs:{type:"primary",plain:"",icon:"el-icon-plus",size:"mini"},on:{click:e.handleAdd}},[e._v("新增")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:area:edit"],expression:"['system:area:edit']"}],attrs:{type:"success",plain:"",icon:"el-icon-edit",size:"mini",disabled:e.single},on:{click:e.handleUpdate}},[e._v("修改")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:area:remove"],expression:"['system:area:remove']"}],attrs:{type:"danger",plain:"",icon:"el-icon-delete",size:"mini",disabled:e.multiple},on:{click:e.handleDelete}},[e._v("删除")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:area:export"],expression:"['system:area:export']"}],attrs:{type:"warning",plain:"",icon:"el-icon-download",size:"mini"},on:{click:e.handleExport}},[e._v("导出")])],1),a("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.areaList},on:{"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"55",align:"center"}}),a("el-table-column",{attrs:{label:"ID",align:"center",prop:"id"}}),a("el-table-column",{attrs:{label:"行政区域",align:"center",prop:"regionId",formatter:e.regionFormatter}}),a("el-table-column",{attrs:{label:"社区",align:"center",prop:"communityId",formatter:e.communityFormatter}}),a("el-table-column",{attrs:{label:"小区名称",align:"center",prop:"name"}}),a("el-table-column",{attrs:{label:"别名",align:"center",prop:"alias"}}),a("el-table-column",{attrs:{label:"详细地址",align:"center",prop:"address"}}),a("el-table-column",{attrs:{label:"坐标",align:"center",prop:"location"}}),a("el-table-column",{attrs:{label:"状态",align:"center",prop:"status"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-tag",{attrs:{type:1===t.row.status?"success":"info"}},[e._v(" "+e._s(1===t.row.status?"正常":"停用")+" ")])]}}])}),a("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:area:edit"],expression:"['system:area:edit']"}],attrs:{size:"mini",type:"text",icon:"el-icon-edit"},on:{click:function(a){return e.handleUpdate(t.row)}}},[e._v("修改")]),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:area:remove"],expression:"['system:area:remove']"}],attrs:{size:"mini",type:"text",icon:"el-icon-delete"},on:{click:function(a){return e.handleDelete(t.row)}}},[e._v("删除")])]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}}),a("el-dialog",{attrs:{title:e.title,visible:e.open,width:"500px","append-to-body":""},on:{"update:visible":function(t){e.open=t}}},[a("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"80px"}},[a("area-chain-selector",{ref:"formAreaSelector",attrs:{regionId:e.form.regionId,communityId:e.form.communityId,labels:{region:"行政区域",community:"社区",residential:"小区"},placeholders:{region:"请选择所属行政区域",community:"请选择社区"},showCommunity:!0,showResidential:!1,isLoading:!1},on:{"update:regionId":function(t){return e.form.regionId=t},"update:communityId":function(t){return e.form.communityId=t}}}),a("el-form-item",{attrs:{label:"小区名称",prop:"name"}},[a("el-input",{attrs:{placeholder:"请输入小区名称"},model:{value:e.form.name,callback:function(t){e.$set(e.form,"name",t)},expression:"form.name"}})],1),a("el-form-item",{attrs:{label:"别名",prop:"alias"}},[a("el-input",{attrs:{placeholder:"请输入别名"},model:{value:e.form.alias,callback:function(t){e.$set(e.form,"alias",t)},expression:"form.alias"}})],1),a("el-form-item",{attrs:{label:"详细地址",prop:"address"}},[a("el-input",{attrs:{placeholder:"请输入详细地址"},model:{value:e.form.address,callback:function(t){e.$set(e.form,"address",t)},expression:"form.address"}})],1),a("el-form-item",{attrs:{label:"坐标",prop:"location"}},[a("el-input",{attrs:{placeholder:"请输入坐标，格式: 经度,纬度 或 POINT(经度 纬度)"},model:{value:e.form.location,callback:function(t){e.$set(e.form,"location",t)},expression:"form.location"}}),a("span",{staticStyle:{"font-size":"12px",color:"#909399"}},[e._v("示例: 120.848042,31.269443 或 POINT(120.848042 31.269443)")])],1),a("el-form-item",{attrs:{label:"状态",prop:"status"}},[a("el-select",{attrs:{placeholder:"请选择状态"},model:{value:e.form.status,callback:function(t){e.$set(e.form,"status",t)},expression:"form.status"}},[a("el-option",{attrs:{label:"正常",value:1}}),a("el-option",{attrs:{label:"停用",value:0}})],1)],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),a("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1)],1)},n=[],l=a("5530"),i=(a("d81d"),a("d3b7"),a("0643"),a("a573"),a("0210")),o=a("939b"),s=a("7db5"),u={name:"Area",components:{AreaChainSelector:o["a"]},data:function(){return{loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,areaList:[],title:"",open:!1,queryParams:{pageNum:1,pageSize:10,communityId:null,regionId:null,name:null,alias:null,address:null,location:null,status:null,deleted:null},form:{},rules:{communityId:[{required:!0,message:"请选择所属社区",trigger:"change"}],regionId:[{required:!0,message:"请选择所属区域",trigger:"change"}],name:[{required:!0,message:"请输入小区名称",trigger:"blur"}],alias:[{required:!0,message:"请输入小区别名",trigger:"blur"}],address:[{required:!0,message:"请输入详细地址",trigger:"blur"}],location:[{required:!0,message:"请输入坐标",trigger:"blur"},{pattern:/^(-?\d+(\.\d+)?),\s*(-?\d+(\.\d+)?)|POINT\(\s*(-?\d+(\.\d+)?)\s+(-?\d+(\.\d+)?)\s*\)$/,message:'坐标格式不正确，请使用"经度,纬度"或"POINT(经度 纬度)"格式',trigger:"blur"}],status:[{required:!0,message:"请选择状态",trigger:"change"}]}}},created:function(){this.getList()},methods:{getList:function(){var e=this;this.loading=!0,Object(i["e"])(this.queryParams).then((function(t){e.areaList=t.rows,e.total=t.total,e.loading=!1}))},handleRegionChange:function(){this.queryParams.communityId=null,this.handleQuery()},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={id:null,communityId:null,regionId:null,name:null,alias:null,address:null,location:null,status:null,createTime:null,updateTime:null,createBy:null,updateBy:null,deleted:null},this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.id})),this.single=1!==e.length,this.multiple=!e.length},handleAdd:function(){this.reset(),this.open=!0,this.title="新增小区"},handleUpdate:function(e){var t=this;this.reset();var a=e.id||this.ids;Object(i["c"])(a).then((function(e){t.form=e.data,t.open=!0,t.title="编辑小区",t.$nextTick((function(){t.$refs.formAreaSelector&&t.$refs.formAreaSelector.initForEdit({regionId:t.form.regionId,communityId:t.form.communityId})}))}))},submitForm:function(){var e=this;this.$refs["form"].validate((function(t){t&&(null!=e.form.id?Object(i["f"])(e.form).then((function(){e.$modal.msgSuccess("保存成功"),e.open=!1,e.getList()})):Object(i["a"])(e.form).then((function(){e.$modal.msgSuccess("新增成功"),e.open=!1,e.getList()})))}))},handleDelete:function(e){var t=this,a=e.id||this.ids;this.$modal.confirm("确认要删除选中的小区吗？").then((function(){return Object(i["b"])(a)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleExport:function(){this.download("system/area/export",Object(l["a"])({},this.queryParams),"area_".concat((new Date).getTime(),".xlsx"))},regionFormatter:s["b"],communityFormatter:s["a"]}},c=u,m=a("2877"),d=Object(m["a"])(c,r,n,!1,null,null,null);t["default"]=d.exports}}]);