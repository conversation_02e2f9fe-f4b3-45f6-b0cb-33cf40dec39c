/* pages/idle-publish/index.wxss */
.container {
  background: linear-gradient(180deg, #f7f8fa 0%, #ffffff 100%);
  min-height: 100vh;
  padding: 16px;
  box-sizing: border-box;
}

.content-card, .trade-card {
  background: #fff;
  border-radius: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06);
  margin-bottom: 20px;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
}

.content-card:active, .trade-card:active {
  transform: scale(0.98);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.04);
}

.description-area {
  padding: 24px;
  border-bottom: 1px solid rgba(240, 240, 240, 0.8);
}

.description-area textarea {
  width: 100%;
  min-height: 140px;
  font-size: 15px;
  color: #333;
  line-height: 1.8;
  letter-spacing: 0.2px;
}

.upload-section {
  padding: 24px;
}

/* 修改上传组件样式以一行显示三张图片，支持横图和竖图 */
.van-uploader {
  width: 100%;
}

.van-uploader__wrapper {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -8rpx;
}

.van-uploader__upload,
.van-uploader__preview {
  width: calc(33.33% - 16rpx) !important;
  margin: 8rpx !important;
  box-sizing: border-box;
}

.van-uploader__upload,
.van-uploader__preview-image {
  height: 240rpx !important; /* 固定预览高度 */
  border-radius: 8rpx;
}

/* 确保图片在预览中正确显示，支持横图和竖图 */
.van-uploader__preview-image {
  width: 100% !important;
  height: 100% !important;
  object-fit: cover !important; /* 保持cover模式以填充预览区域 */
  background-color: #f5f5f5;
}

/* 设置预览容器固定高度 */
.van-uploader__preview {
  height: 240rpx !important;
  overflow: hidden;
}

/* 上传按钮样式优化 */
.van-uploader__upload {
  background: linear-gradient(135deg, #f8f9fa, #ffffff);
  border: 2rpx dashed #d9d9d9;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #999;
  font-size: 24rpx;
}

.van-uploader__upload:active {
  background: linear-gradient(135deg, #f0f0f0, #f8f9fa);
  border-color: #3B7FFF;
  color: #3B7FFF;
}

.price-section, .condition-section {
  padding: 12px 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.price-label, .condition-label {
  font-size: 15px;
  color: #333;
  font-weight: 500;
  letter-spacing: 0.2px;
}

.price-value, .condition-value {
  color: #666;
  font-size: 15px;
  display: flex;
  align-items: center;
  padding: 4px 8px;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.price-value:active, .condition-value:active {
  opacity: 0.8;
}

.currency {
  color: #ff4d4f;
  font-weight: 600;
  margin-right: 2px;
}

.arrow {
  margin-left: 8px;
  color: #ccc;
  font-size: 14px;
}

.divider {
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(0, 0, 0, 0.06), transparent);
  margin: 0 24px;
}

.submit-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 12px 16px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  box-shadow: 0 -2px 12px rgba(0, 0, 0, 0.04);
  border-top: 1px solid rgba(255, 255, 255, 0.8);
}

.publish-btn {
  width: 100%;
  background: linear-gradient(135deg, #34d399, #10b981);
  color: #fff;
  border: none;
  border-radius: 24px;
  font-size: 15px;
  font-weight: 500;
  padding: 10px 0;
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.25);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  letter-spacing: 0.3px;
  position: relative;
  overflow: hidden;
}

.publish-btn:active {
  transform: translateY(1px);
  box-shadow: 0 2px 6px rgba(16, 185, 129, 0.2);
}

.publish-btn[disabled] {
  background: linear-gradient(135deg, #e0e0e0, #d0d0d0);
  color: #999;
  box-shadow: none;
  transform: none;
}

.contact-tip {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 12px 16px 0 16px;
  padding: 8px 12px;
  background: rgba(255, 107, 107, 0.08);
  border-radius: 12px;
  border: 1px solid rgba(255, 107, 107, 0.15);
}

.contact-tip text {
  font-size: 12px;
  color: #ff6b6b;
  margin-left: 4px;
}

/* 价格输入弹窗样式 */
.price-input-popup {
  background: #f7f8fa;
}

.popup-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 32rpx;
  padding: 0 24rpx;
}

.popup-header .title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.popup-header .close-btn {
  padding: 8rpx;
  color: #999;
}

.price-input-container {
  padding: 0 24rpx;
  margin-bottom: 32rpx;
}

.input-wrapper {
  display: flex;
  align-items: center;
  padding: 24rpx;
  background: #f7f8fa;
  border-radius: 8rpx;
  margin-bottom: 16rpx;
}

.input-label {
  font-size: 28rpx;
  color: #333;
  margin-right: 24rpx;
}

.input-wrapper input {
  flex: 1;
  font-size: 32rpx;
  color: #333;
  text-align: right;
  padding: 0 16rpx;
}

.price-unit {
  font-size: 28rpx;
  color: #999;
  margin-left: 8rpx;
}

.price-tips {
  font-size: 24rpx;
  color: #999;
  text-align: right;
  padding: 0 24rpx;
}

.popup-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24rpx;
  border-top: 1px solid #f5f5f5;
  margin-top: 24rpx;
  padding-top: 24rpx;
}

.cancel-btn, .confirm-btn {
  flex: 1;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  border-radius: 8rpx;
  margin: 0 12rpx;
}

.cancel-btn {
  background: #f5f5f5;
  color: #666;
}

.confirm-btn {
  background: linear-gradient(135deg, #34d399, #10b981);
  color: #fff;
  font-weight: 500;
}

.cancel-btn:active, .confirm-btn:active {
  opacity: 0.8;
}

/* 修改价格显示样式 */
.price-section {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
  background: #fff;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
}

.price-section .label {
  font-size: 28rpx;
  color: #333;
}

.price-section .value {
  display: flex;
  align-items: center;
  color: #333;
}

.price-section .price-value {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
  background: none;
  padding: 0;
  border: none;
}

.price-section .unit {
  font-size: 24rpx;
  color: #999;
  margin-left: 4rpx;
}

.shipping-section {
  padding: 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.shipping-label {
  font-size: 16px;
  color: #333;
  font-weight: 600;
  letter-spacing: 0.3px;
}

.shipping-value {
  color: #666;
  font-size: 15px;
  display: flex;
  align-items: center;
  background: linear-gradient(145deg, #f7f8fa, #ffffff);
  padding: 10px 16px;
  border-radius: 12px;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.02);
  border: 1px solid rgba(240, 240, 240, 0.8);
  transition: all 0.3s ease;
}

.shipping-value:active {
  background: linear-gradient(145deg, #f0f0f0, #f7f8fa);
  transform: scale(0.98);
}

/* 成色选择器样式 */
.picker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #f5f5f5;
}

.picker-header text {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.close-btn {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: #999;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.close-btn:active {
  background: #f5f5f5;
}

.condition-list {
  padding: 8px 0;
  max-height: 60vh;
  overflow-y: auto;
}

.condition-item {
  padding: 14px 20px;
  font-size: 15px;
  color: #333;
  transition: all 0.2s ease;
  position: relative;
}

.condition-item:active {
  background: #f8f9fa;
}

.condition-item.active {
  color: #ff4d4f;
  font-weight: 500;
}

.condition-item.active::after {
  content: '';
  position: absolute;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: #ff4d4f;
}

.price-display {
  padding: 40rpx 32rpx;
  background: #fff;
  text-align: left;
  position: relative;
}

.price-display::after {
  content: '';
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  height: 1px;
  background: #eee;
  transform: scaleY(0.5);
}

.price-display .price-label {
  font-size: 28rpx;
  color: #999;
  display: block;
  margin-bottom: 20rpx;
}

.price-display .value {
  display: flex;
  align-items: center;
}

.price-display .currency {
  color: #ff4d4f;
  margin-right: 4px;
  font-weight: 500;
}

.price-display .price-value {
  font-size: 56rpx;
  color: #333;
  font-weight: 500;
}

.keyboard-container {
  padding: 0;
}

.keyboard-wrapper {
  display: flex;
  background: #fff;
  border-radius: 0;
  overflow: hidden;
}

.keyboard-left {
  flex: 3;
}

.keyboard-right {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.keyboard-right .key-item {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36rpx;
  color: #333;
  border-left: 1px solid #eee;
  position: relative;
  font-weight: 600;
  height: 120rpx;
}

/* 添加右侧键盘的水平分割线 */
.keyboard-right .key-item:first-child::after {
  content: '';
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  height: 1px;
  background-color: #eee;
}

.keyboard-right .key-item.delete {
  color: #333;
  background-color: #fff;
}

.keyboard-right .key-item.confirm {
  color: #333;
  font-weight: 600;
  background-color: #FFEB3B;
}

.keyboard-row {
  display: flex;
}

.key-item {
  flex: 1;
  height: 120rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36rpx;
  color: #333;
  background: #fff;
  border-bottom: 1px solid #eee;
  position: relative;
  font-weight: 600;
}

/* 添加竖向分割线 */
.key-item:not(:last-child)::after {
  content: '';
  position: absolute;
  top: 0;
  bottom: 0;
  right: 0;
  width: 1px;
  background-color: #eee;
}

.key-item:active {
  background: #f5f5f5;
} 

/* 联系方式相关样式 */
.contact-icons {
  display: flex;
  gap: 8px;
  align-items: center;
}



