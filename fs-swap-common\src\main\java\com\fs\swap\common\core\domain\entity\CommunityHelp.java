package com.fs.swap.common.core.domain.entity;

import java.util.Date;
import com.fs.swap.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.fs.swap.common.annotation.Excel;

/**
 * 邻里互助对象 community_help
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
public class CommunityHelp extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 互助ID */
    private Long id;

    /** 标题 */
    @Excel(name = "标题")
    private String title;

    /** 详细内容 */
    @Excel(name = "详细内容")
    private String content;

    /** 图片列表(JSON格式) */
    @Excel(name = "图片列表")
    private String images;

    /** 分类 */
    @Excel(name = "分类")
    private String category;

    /** 发布类型 */
    @Excel(name = "发布类型")
    private String publishType;

    /** 联系方式 */
    @Excel(name = "联系方式")
    private String contactInfo;

    /** 浏览次数 */
    @Excel(name = "浏览次数")
    private Integer viewCount;

    /** 状态(1:正常,2:已完成,3:已下架) */
    @Excel(name = "状态")
    private String status;

    /** 关联社区ID */
    @Excel(name = "关联社区ID")
    private Long communityId;

    /** 关联小区ID */
    @Excel(name = "关联小区ID")
    private Long residentialId;

    /** 发布用户ID */
    @Excel(name = "发布用户ID")
    private Long userId;

    /** 买家ID */
    @Excel(name = "买家ID")
    private Long buyerId;

    /** 截止时间 */
    @Excel(name = "截止时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /** 删除标志(0:正常,1:删除) */
    @Excel(name = "删除标志")
    private Integer deleted;

    // 扩展字段，不映射到数据库
    /** 发布者昵称 */
    private String nickname;

    /** 发布者头像 */
    private String avatar;

    /** 话题名称 */
    private String topicName;

    /** 第一张图片URL */
    private String firstImage;

    /** 审核备注 */
    private String auditRemark;

    /** 搜索关键词 */
    private String searchValue;

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setTitle(String title)
    {
        this.title = title;
    }

    public String getTitle()
    {
        return title;
    }
    public void setContent(String content)
    {
        this.content = content;
    }

    public String getContent()
    {
        return content;
    }
    public void setImages(String images)
    {
        this.images = images;
    }

    public String getImages()
    {
        return images;
    }
    public void setCategory(String category)
    {
        this.category = category;
    }

    public String getCategory()
    {
        return category;
    }
    public void setPublishType(String publishType)
    {
        this.publishType = publishType;
    }

    public String getPublishType()
    {
        return publishType;
    }
    public void setContactInfo(String contactInfo)
    {
        this.contactInfo = contactInfo;
    }

    public String getContactInfo()
    {
        return contactInfo;
    }
    public void setViewCount(Integer viewCount)
    {
        this.viewCount = viewCount;
    }

    public Integer getViewCount()
    {
        return viewCount;
    }
    public void setStatus(String status)
    {
        this.status = status;
    }

    public String getStatus()
    {
        return status;
    }
    public void setCommunityId(Long communityId)
    {
        this.communityId = communityId;
    }

    public Long getCommunityId()
    {
        return communityId;
    }
    public void setResidentialId(Long residentialId)
    {
        this.residentialId = residentialId;
    }

    public Long getResidentialId()
    {
        return residentialId;
    }
    public void setUserId(Long userId)
    {
        this.userId = userId;
    }

    public Long getUserId()
    {
        return userId;
    }
    public void setBuyerId(Long buyerId)
    {
        this.buyerId = buyerId;
    }

    public Long getBuyerId()
    {
        return buyerId;
    }
    public void setEndTime(Date endTime)
    {
        this.endTime = endTime;
    }

    public Date getEndTime()
    {
        return endTime;
    }
    public void setDeleted(Integer deleted)
    {
        this.deleted = deleted;
    }

    public Integer getDeleted()
    {
        return deleted;
    }

    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public String getTopicName() {
        return topicName;
    }

    public void setTopicName(String topicName) {
        this.topicName = topicName;
    }

    public String getFirstImage() {
        return firstImage;
    }

    public void setFirstImage(String firstImage) {
        this.firstImage = firstImage;
    }

    public String getAuditRemark() {
        return auditRemark;
    }

    public void setAuditRemark(String auditRemark) {
        this.auditRemark = auditRemark;
    }

    public String getSearchValue() {
        return searchValue;
    }

    public void setSearchValue(String searchValue) {
        this.searchValue = searchValue;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("title", getTitle())
            .append("content", getContent())
            .append("images", getImages())
            .append("category", getCategory())
            .append("publishType", getPublishType())
            .append("contactInfo", getContactInfo())
            .append("viewCount", getViewCount())
            .append("status", getStatus())
            .append("communityId", getCommunityId())
            .append("residentialId", getResidentialId())
            .append("userId", getUserId())
            .append("buyerId", getBuyerId())
            .append("endTime", getEndTime())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .append("deleted", getDeleted())
            .toString();
    }
}