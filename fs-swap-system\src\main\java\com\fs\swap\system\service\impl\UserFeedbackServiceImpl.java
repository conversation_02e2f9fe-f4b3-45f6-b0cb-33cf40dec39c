package com.fs.swap.system.service.impl;

import com.fs.swap.common.core.domain.entity.UserFeedback;
import com.fs.swap.system.mapper.UserFeedbackMapper;
import com.fs.swap.system.service.IUserFeedbackService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 用户反馈Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-27
 */
@Service
public class UserFeedbackServiceImpl implements IUserFeedbackService
{
    @Autowired
    private UserFeedbackMapper userFeedbackMapper;

    /**
     * 查询用户反馈
     *
     * @param id 用户反馈主键
     * @return 用户反馈
     */
    @Override
    public UserFeedback selectUserFeedbackById(Long id)
    {
        return userFeedbackMapper.selectUserFeedbackById(id);
    }

    /**
     * 查询用户反馈列表
     *
     * @param userFeedback 用户反馈
     * @return 用户反馈
     */
    @Override
    public List<UserFeedback> selectUserFeedbackList(UserFeedback userFeedback)
    {
        return userFeedbackMapper.selectUserFeedbackList(userFeedback);
    }

    /**
     * 新增用户反馈
     *
     * @param userFeedback 用户反馈
     * @return 结果
     */
    @Override
    public int insertUserFeedback(UserFeedback userFeedback)
    {
        return userFeedbackMapper.insertUserFeedback(userFeedback);
    }

    /**
     * 修改用户反馈
     *
     * @param userFeedback 用户反馈
     * @return 结果
     */
    @Override
    public int updateUserFeedback(UserFeedback userFeedback)
    {
        return userFeedbackMapper.updateUserFeedback(userFeedback);
    }

    /**
     * 批量删除用户反馈
     *
     * @param ids 需要删除的用户反馈主键
     * @return 结果
     */
    @Override
    public int deleteUserFeedbackByIds(Long[] ids)
    {
        return userFeedbackMapper.deleteUserFeedbackByIds(ids);
    }

    /**
     * 删除用户反馈信息
     *
     * @param id 用户反馈主键
     * @return 结果
     */
    @Override
    public int deleteUserFeedbackById(Long id)
    {
        return userFeedbackMapper.deleteUserFeedbackById(id);
    }

    /**
     * 根据用户ID查询反馈列表
     *
     * @param userId 用户ID
     * @return 用户反馈集合
     */
    @Override
    public List<UserFeedback> selectUserFeedbackListByUserId(Long userId)
    {
        return userFeedbackMapper.selectUserFeedbackListByUserId(userId);
    }
}
