(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-08fa3263"],{"413f":function(e,t,n){"use strict";n("fe01")},"820e":function(e,t,n){"use strict";var i=n("23e7"),a=n("c65b"),r=n("59ed"),s=n("f069"),o=n("e667"),l=n("2266"),c=n("5eed");i({target:"Promise",stat:!0,forced:c},{allSettled:function(e){var t=this,n=s.f(t),i=n.resolve,c=n.reject,u=o((function(){var n=r(t.resolve),s=[],o=0,c=1;l(e,(function(e){var r=o++,l=!1;c++,a(n,t,e).then((function(e){l||(l=!0,s[r]={status:"fulfilled",value:e},--c||i(s))}),(function(e){l||(l=!0,s[r]={status:"rejected",reason:e},--c||i(s))}))})),--c||i(s)}));return u.error&&c(u.value),n.promise}})},"939b":function(e,t,n){"use strict";var i=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"area-chain-selector"},[n("div",{class:{"area-selector-row":e.inlineLayout}},[n("el-form-item",{attrs:{label:e.labels.region,prop:e.props.region}},[n("el-cascader",{attrs:{options:e.regionOptions,props:e.cascaderProps,placeholder:e.placeholders.region,clearable:!0,filterable:!0,"show-all-levels":!0,loading:e.loading},on:{change:e.handleRegionPathChange},model:{value:e.selectedRegionPath,callback:function(t){e.selectedRegionPath=t},expression:"selectedRegionPath"}},[n("template",{slot:"empty"},[e.loading?n("div",{staticClass:"el-cascader-menu__empty-text"},[n("i",{staticClass:"el-icon-loading"}),e._v(" 加载中... ")]):n("div",{staticClass:"el-cascader-menu__empty-text"},[e._v(" 暂无数据 ")])])],2)],1),e.showCommunity?n("el-form-item",{attrs:{label:e.labels.community,prop:e.props.community}},[n("el-select",{attrs:{placeholder:e.placeholders.community,filterable:"",clearable:"",loading:e.loading,disabled:!e.selectedRegionId},on:{change:e.handleCommunityChange},model:{value:e.selectedCommunityId,callback:function(t){e.selectedCommunityId=t},expression:"selectedCommunityId"}},e._l(e.communityOptions,(function(e){return n("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1):e._e(),e.showResidential?n("el-form-item",{attrs:{label:e.labels.residential,prop:e.props.residential}},[n("el-select",{attrs:{placeholder:e.placeholders.residential,filterable:"",clearable:"",loading:e.loading,disabled:!e.selectedCommunityId},on:{change:e.handleResidentialChange},model:{value:e.selectedResidentialId,callback:function(t){e.selectedResidentialId=t},expression:"selectedResidentialId"}},e._l(e.residentialOptions,(function(e){return n("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1):e._e()],1),e.error?n("div",{staticClass:"area-selector-error"},[n("el-alert",{attrs:{title:e.error,type:"error","show-icon":"",closable:!1}})],1):e._e()])},a=[],r=n("5530"),s=n("c7eb"),o=n("1da1"),l=(n("d9e2"),n("4de4"),n("a15b"),n("d81d"),n("14d9"),n("b0c0"),n("4ec9"),n("a9e3"),n("d3b7"),n("820e"),n("3ca3"),n("0643"),n("2382"),n("4e3e"),n("a573"),n("159b"),n("ddb0"),n("a04a")),c={name:"AreaChainSelector",components:{},props:{regionId:{type:[Number,String],default:null},communityId:{type:[Number,String],default:null},residentialId:{type:[Number,String],default:null},props:{type:Object,default:function(){return{region:"regionId",community:"communityId",residential:"residentialId"}}},labels:{type:Object,default:function(){return{region:"行政区域",community:"社区",residential:"小区"}}},placeholders:{type:Object,default:function(){return{region:"请选择行政区域",community:"请选择社区",residential:"请选择小区"}}},showCommunity:{type:Boolean,default:!0},showResidential:{type:Boolean,default:!0},isLoading:{type:Boolean,default:!1},showRefreshButton:{type:Boolean,default:!0},inlineLayout:{type:Boolean,default:!1}},data:function(){return{selectedRegionId:null,selectedCommunityId:null,selectedResidentialId:null,selectedRegionPath:[],loading:!1,error:null,internalLoading:!1,regionList:[],communityList:[],residentialList:[],cachedRegionOptions:null,regionListVersion:0,cascaderProps:{value:"id",label:"name",children:"children",checkStrictly:!1,emitPath:!0,expandTrigger:"click",lazy:!1,multiple:!1}}},computed:{regionOptions:function(){return this.cachedRegionOptions||[]},communityOptions:function(){var e=this;return this.selectedRegionId?this.communityList.filter((function(t){return t.regionId===Number(e.selectedRegionId)})).map((function(e){return{label:e.name,value:e.id}})):[]},residentialOptions:function(){var e=this;return this.selectedCommunityId?this.residentialList.filter((function(t){return t.communityId===Number(e.selectedCommunityId)})).map((function(e){return{label:e.name,value:e.id}})):[]}},watch:{regionId:function(e){this.isLoading||e===this.selectedRegionId||(this.selectedRegionId=e?Number(e):null)},communityId:function(e){this.isLoading||e===this.selectedCommunityId||(this.selectedCommunityId=e?Number(e):null)},residentialId:function(e){this.isLoading||e===this.selectedResidentialId||(this.selectedResidentialId=e?Number(e):null)},showCommunity:function(e){e||(this.selectedCommunityId=null,this.selectedResidentialId=null)},showResidential:function(e){e||(this.selectedResidentialId=null)},selectedRegionId:function(e){this.$emit("update:regionId",e),this.internalLoading||e===this.regionId||(this.selectedCommunityId=null,this.selectedResidentialId=null,this.$emit("region-change",e))},selectedRegionPath:function(e){if(!this.internalLoading)if(e&&0!==e.length){var t=e[e.length-1];t!==this.selectedRegionId&&(this.selectedRegionId=t)}else this.selectedRegionId=null},selectedCommunityId:function(e){this.$emit("update:communityId",e),this.internalLoading||e===this.communityId||(this.selectedResidentialId=null,this.$emit("community-change",e))},selectedResidentialId:function(e){this.$emit("update:residentialId",e),this.internalLoading||e===this.residentialId||this.$emit("residential-change",e)},error:function(e){e&&this.$emit("error",e)},regionList:{handler:function(){this.buildRegionOptionsCache()},immediate:!0}},created:function(){this.loadData(),this.showCommunity?this.showResidential||(this.selectedResidentialId=null):(this.selectedCommunityId=null,this.selectedResidentialId=null)},methods:{loadData:function(){var e=arguments,t=this;return Object(o["a"])(Object(s["a"])().mark((function n(){var i,a,r,o,c,u,d;return Object(s["a"])().wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(i=e.length>0&&void 0!==e[0]&&e[0],!t.loading||i){n.next=3;break}return n.abrupt("return");case 3:return t.loading=!0,t.error=null,n.prev=5,a=l["a"].loadRegions(i),r=l["a"].loadCommunities(i),o=l["a"].loadResidentials(i),n.next=11,Promise.allSettled([a,r,o]);case 11:if(c=n.sent,u=c.filter((function(e){return"rejected"===e.status})),!(u.length>0)){n.next=16;break}throw d=u.map((function(e){var t;return(null===(t=e.reason)||void 0===t?void 0:t.message)||"未知错误"})).join(", "),new Error("部分数据加载失败: ".concat(d));case 16:if(t.regionList=l["a"].getRegions(),t.communityList=l["a"].getCommunities(),t.residentialList=l["a"].getResidentials(),0!==t.regionList.length){n.next=21;break}throw new Error("区域数据为空，请检查数据源");case 21:return n.abrupt("return",!0);case 24:return n.prev=24,n.t0=n["catch"](5),console.error("加载区域数据失败:",n.t0),t.error=n.t0.message||"加载区域数据失败，请稍后重试",t.$emit("load-error",n.t0),n.abrupt("return",!1);case 30:return n.prev=30,t.loading=!1,n.finish(30);case 33:case"end":return n.stop()}}),n,null,[[5,24,30,33]])})))()},refreshData:function(){var e=this;return Object(o["a"])(Object(s["a"])().mark((function t(){return Object(s["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.abrupt("return",e.loadData(!0));case 1:case"end":return t.stop()}}),t)})))()},handleRegionPathChange:function(){this.isLoading},handleCommunityChange:function(e){this.isLoading||(this.selectedCommunityId=e)},handleResidentialChange:function(e){this.isLoading||(this.selectedResidentialId=e)},clearAll:function(){this.selectedRegionId=null,this.selectedCommunityId=null,this.selectedResidentialId=null},batchSetAreaValues:function(e,t,n){var i=this;return Object(o["a"])(Object(s["a"])().mark((function a(){var r;return Object(s["a"])().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return r=i.internalLoading,i.internalLoading=!0,a.prev=2,null!==e&&(i.selectedRegionId=e),a.next=6,i.$nextTick();case 6:return null!==t&&(i.selectedCommunityId=t),a.next=9,i.$nextTick();case 9:return null!==n&&(i.selectedResidentialId=n),a.next=12,i.$nextTick();case 12:return a.prev=12,i.internalLoading=r,a.finish(12);case 15:case"end":return a.stop()}}),a,null,[[2,,12,15]])})))()},initForEdit:function(){var e=arguments,t=this;return Object(o["a"])(Object(s["a"])().mark((function n(){var i,a,r,o,c,u,d,h,m;return Object(s["a"])().wrap((function(n){while(1)switch(n.prev=n.next){case 0:return i=e.length>0&&void 0!==e[0]?e[0]:{},n.prev=1,t.internalLoading=!0,t.selectedRegionId=null,t.selectedCommunityId=null,t.selectedResidentialId=null,t.selectedRegionPath=[],n.next=9,t.$nextTick();case 9:if(0!==t.regionList.length&&0!==t.communityList.length&&0!==t.residentialList.length){n.next=12;break}return n.next=12,t.loadData(!1);case 12:if(!i.regionId){n.next=53;break}return a=Number(i.regionId),r=l["a"].getRegionPath(a),r&&r.length>0?t.selectedRegionPath=r.map((function(e){return e.id})):t.selectedRegionId=a,n.next=18,t.$nextTick();case 18:if(t.selectedRegionId){n.next=22;break}return t.selectedRegionId=a,n.next=22,t.$nextTick();case 22:if(!t.showCommunity||!i.communityId){n.next=49;break}if(o=Number(i.communityId),c=t.communityOptions,0!==c.length){n.next=31;break}if(u=l["a"].getCommunityById(o),!u||!u.regionId){n.next=31;break}return t.selectedRegionId=u.regionId,n.next=31,t.$nextTick();case 31:return t.selectedCommunityId=o,n.next=34,t.$nextTick();case 34:if(!t.showResidential||!i.residentialId){n.next=46;break}if(d=Number(i.residentialId),h=t.residentialOptions,0!==h.length){n.next=43;break}if(m=l["a"].getResidentialById(d),!m||!m.communityId){n.next=43;break}return t.selectedCommunityId=m.communityId,n.next=43,t.$nextTick();case 43:t.selectedResidentialId=d,n.next=47;break;case 46:t.selectedResidentialId=null;case 47:n.next=51;break;case 49:t.selectedCommunityId=null,t.selectedResidentialId=null;case 51:n.next=57;break;case 53:t.selectedRegionId=null,t.selectedCommunityId=null,t.selectedResidentialId=null,t.selectedRegionPath=[];case 57:return n.abrupt("return",!0);case 60:return n.prev=60,n.t0=n["catch"](1),n.abrupt("return",!1);case 63:return n.prev=63,t.internalLoading=!1,n.finish(63);case 66:case"end":return n.stop()}}),n,null,[[1,60,63,66]])})))()},buildRegionOptionsCache:function(){try{if(!this.regionList||0===this.regionList.length)return this.cachedRegionOptions=[],void(this.regionListVersion=0);var e=new Map;this.regionList.forEach((function(t){t&&t.id&&e.set(t.id,Object(r["a"])(Object(r["a"])({},t),{},{children:[]}))}));var t=[];this.regionList.forEach((function(n){if(n&&n.id){var i=e.get(n.id);i&&(n.pid&&e.has(n.pid)?e.get(n.pid).children.push(i):1===n.type&&t.push(i))}}));var n=function(e){return{id:e.id,name:e.name||"未知",children:e.children.length>0?e.children.map(n):null,leaf:0===e.children.length}},i=t.map(n);this.cachedRegionOptions=i,this.regionListVersion=this.regionList.length}catch(a){console.error("构建区域选项缓存失败:",a),this.cachedRegionOptions=[],this.regionListVersion=0}}}},u=c,d=(n("413f"),n("2877")),h=Object(d["a"])(u,i,a,!1,null,"2cf930a3",null);t["a"]=h.exports},a04a:function(e,t,n){"use strict";var i=n("2909"),a=n("5530"),r=n("c7eb"),s=n("1da1"),o=n("d4ec"),l=n("bee2"),c=(n("99af"),n("4de4"),n("7db0"),n("a15b"),n("d81d"),n("fb6a"),n("b0c0"),n("e9c4"),n("4ec9"),n("a9e3"),n("b64b"),n("d3b7"),n("07ac"),n("25f0"),n("3ca3"),n("0643"),n("2382"),n("fffc"),n("4e3e"),n("a573"),n("159b"),n("ddb0"),n("8c69")),u=n("ea81"),d=n("0210"),h="1.0.0",m="area_data_cache_version",g={REGIONS:"cached_regions",COMMUNITIES:"cached_communities",RESIDENTIALS:"cached_residentials",TIMESTAMP:"area_data_timestamp"},f=36e5,p=function(){function e(){Object(o["a"])(this,e),this.loading={regions:!1,communities:!1,residentials:!1,all:!1},this.cache={regions:[],communities:[],residentials:[],regionMap:new Map,communityMap:new Map,residentialMap:new Map},this.loadPromises={regions:null,communities:null,residentials:null,all:null},this.init()}return Object(l["a"])(e,[{key:"init",value:function(){this.checkCacheVersion(),this.loadCachedData()}},{key:"checkCacheVersion",value:function(){var e=localStorage.getItem(m);e!==h&&(Object.values(g).forEach((function(e){localStorage.removeItem(e)})),localStorage.setItem(m,h))}},{key:"loadCachedData",value:function(){try{var e=localStorage.getItem(g.TIMESTAMP),t=Date.now();if(!e)return;var n=t-parseInt(e);if(console.log("缓存时间戳: ".concat(new Date(parseInt(e)).toLocaleString(),", 当前时间: ").concat(new Date(t).toLocaleString(),", 缓存年龄: ").concat(Math.floor(n/1e3/60)," 分钟")),n<f){console.log("缓存未过期，尝试加载缓存数据");var i=localStorage.getItem(g.REGIONS);if(i){var a=JSON.parse(i);a.length>0&&(this.cache.regions=a,this.cache.regionMap=new Map(a.map((function(e){return[e.id,e]}))))}else console.log("缓存中没有区域数据");var r=localStorage.getItem(g.COMMUNITIES);if(r){var s=JSON.parse(r);console.log("从缓存加载社区数据，数量: ".concat(s.length)),s.length>0&&(this.cache.communities=s,this.cache.communityMap=new Map(s.map((function(e){return[e.id,e]}))),console.log("社区数据加载成功，Map大小:",this.cache.communityMap.size))}else console.log("缓存中没有社区数据");var o=localStorage.getItem(g.RESIDENTIALS);if(o){var l=JSON.parse(o);console.log("从缓存加载小区数据，数量: ".concat(l.length)),l.length>0&&(this.cache.residentials=l,this.cache.residentialMap=new Map(l.map((function(e){return[e.id,e]}))),console.log("小区数据加载成功，Map大小:",this.cache.residentialMap.size))}else console.log("缓存中没有小区数据");console.log("缓存数据加载完成")}else console.log("缓存已过期 (".concat(Math.floor(n/1e3/60)," 分钟)，需要重新加载数据"))}catch(c){console.error("加载缓存数据失败:",c),this.clearCache()}}},{key:"clearCache",value:function(){Object.values(g).forEach((function(e){localStorage.removeItem(e)})),this.cache={regions:[],communities:[],residentials:[],regionMap:new Map,communityMap:new Map,residentialMap:new Map}}},{key:"saveToCache",value:function(){try{localStorage.setItem(g.TIMESTAMP,Date.now().toString()),this.cache.regions.length>0&&localStorage.setItem(g.REGIONS,JSON.stringify(this.cache.regions)),this.cache.communities.length>0&&localStorage.setItem(g.COMMUNITIES,JSON.stringify(this.cache.communities)),this.cache.residentials.length>0&&localStorage.setItem(g.RESIDENTIALS,JSON.stringify(this.cache.residentials))}catch(e){console.error("保存缓存数据失败:",e)}}},{key:"loadAllData",value:function(){var e=Object(s["a"])(Object(r["a"])().mark((function e(){var t,n,i,a,s=this,o=arguments;return Object(r["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t=o.length>0&&void 0!==o[0]&&o[0],console.log("开始加载所有区域数据, forceRefresh=".concat(t)),n=this.cache.regions.length>0,i=this.cache.communities.length>0,a=this.cache.residentials.length>0,console.log("当前缓存状态:",{hasRegions:n,hasCommunities:i,hasResidentials:a}),!this.loadPromises.all||t){e.next=9;break}return console.log("数据正在加载中，返回现有Promise"),e.abrupt("return",this.loadPromises.all);case 9:if(!(n&&i&&a)||t){e.next=12;break}return console.log("缓存中已有所有数据，跳过加载"),e.abrupt("return",Promise.resolve({regions:this.cache.regions,communities:this.cache.communities,residentials:this.cache.residentials}));case 12:return t&&(console.log("强制刷新，清除现有缓存"),this.clearCache()),this.loading.all=!0,console.log("开始从服务器加载数据..."),this.loadPromises.all=Promise.all([this.loadRegions(t),this.loadCommunities(t),this.loadResidentials(t)]).then((function(e){return console.log("所有数据加载完成:",{regions:e[0].length,communities:e[1].length,residentials:e[2].length}),{regions:e[0],communities:e[1],residentials:e[2]}})).finally((function(){s.loading.all=!1})),e.abrupt("return",this.loadPromises.all);case 17:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}()},{key:"loadRegions",value:function(){var e=Object(s["a"])(Object(r["a"])().mark((function e(){var t,n=this,i=arguments;return Object(r["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t=i.length>0&&void 0!==i[0]&&i[0],console.log("开始加载区域数据, forceRefresh=".concat(t,", 当前缓存数量=").concat(this.cache.regions.length)),!(this.cache.regions.length>0)||t){e.next=5;break}return console.log("缓存中已有区域数据，跳过加载"),e.abrupt("return",Promise.resolve(this.cache.regions));case 5:if(!this.loadPromises.regions||t){e.next=8;break}return console.log("区域数据正在加载中，返回现有Promise"),e.abrupt("return",this.loadPromises.regions);case 8:return this.loading.regions=!0,console.log("开始从服务器加载区域数据..."),this.loadPromises.regions=Object(c["d"])().then((function(e){console.log("区域数据API响应:",e?"成功":"失败");var t=[];return e.data&&Array.isArray(e.data)?(t=e.data,console.log("从response.data中提取区域数据")):e.rows&&Array.isArray(e.rows)?(t=e.rows,console.log("从response.rows中提取区域数据")):Array.isArray(e)?(t=e,console.log("响应本身是区域数据数组")):console.warn("无法从响应中提取区域数据:",e),console.log("加载区域数据成功，数量:",t.length),t.length>0&&console.log("区域数据示例:",t.slice(0,3)),t=t.map((function(e){return{id:Number(e.id),name:e.name,type:Number(e.type||0),pid:Number(e.pid||0),code:Number(e.code||0)}})),n.cache.regions=t,n.cache.regionMap=new Map(t.map((function(e){return[e.id,e]}))),console.log("区域Map创建完成，大小:",n.cache.regionMap.size),n.saveToCache(),console.log("区域数据已保存到localStorage"),t})).catch((function(e){throw console.error("加载区域数据失败:",e),e})).finally((function(){n.loading.regions=!1,console.log("区域数据加载完成")})),e.abrupt("return",this.loadPromises.regions);case 12:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}()},{key:"loadCommunities",value:function(){var e=Object(s["a"])(Object(r["a"])().mark((function e(){var t,n=this,i=arguments;return Object(r["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t=i.length>0&&void 0!==i[0]&&i[0],!(this.cache.communities.length>0)||t){e.next=3;break}return e.abrupt("return",Promise.resolve(this.cache.communities));case 3:if(!this.loadPromises.communities||t){e.next=5;break}return e.abrupt("return",this.loadPromises.communities);case 5:return this.loading.communities=!0,this.loadPromises.communities=Object(u["d"])().then((function(e){var t=[];return e.data&&Array.isArray(e.data)?t=e.data:e.rows&&Array.isArray(e.rows)?t=e.rows:Array.isArray(e)&&(t=e),console.log("加载社区数据成功，数量:",t.length),console.log("社区数据示例:",t.slice(0,3)),t=t.map((function(e){return{id:Number(e.id),name:e.name,regionId:Number(e.regionId||0),code:e.code,status:Number(e.status||0)}})),n.cache.communities=t,n.cache.communityMap=new Map(t.map((function(e){return[e.id,e]}))),console.log("社区Map创建完成，大小:",n.cache.communityMap.size),n.saveToCache(),t})).catch((function(e){throw console.error("加载社区数据失败:",e),e})).finally((function(){n.loading.communities=!1})),e.abrupt("return",this.loadPromises.communities);case 8:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}()},{key:"loadResidentials",value:function(){var e=Object(s["a"])(Object(r["a"])().mark((function e(){var t,n=this,i=arguments;return Object(r["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t=i.length>0&&void 0!==i[0]&&i[0],!(this.cache.residentials.length>0)||t){e.next=3;break}return e.abrupt("return",Promise.resolve(this.cache.residentials));case 3:if(!this.loadPromises.residentials||t){e.next=5;break}return e.abrupt("return",this.loadPromises.residentials);case 5:return this.loading.residentials=!0,this.loadPromises.residentials=Object(d["d"])().then((function(e){var t=[];return e.data&&Array.isArray(e.data)?t=e.data:e.rows&&Array.isArray(e.rows)?t=e.rows:Array.isArray(e)&&(t=e),console.log("加载小区数据成功，数量:",t.length),console.log("小区数据示例:",t.slice(0,3)),t=t.map((function(e){return{id:Number(e.id),name:e.name,communityId:Number(e.communityId||0),regionId:Number(e.regionId||0),address:e.address,status:Number(e.status||0)}})),n.cache.residentials=t,n.cache.residentialMap=new Map(t.map((function(e){return[e.id,e]}))),console.log("小区Map创建完成，大小:",n.cache.residentialMap.size),n.saveToCache(),t})).catch((function(e){throw console.error("加载小区数据失败:",e),e})).finally((function(){n.loading.residentials=!1})),e.abrupt("return",this.loadPromises.residentials);case 8:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}()},{key:"getCommunityListByRegionId",value:function(e){return e?this.cache.communities.filter((function(t){return t.regionId===Number(e)})):[]}},{key:"getResidentialListByCommunityId",value:function(e){return e?this.cache.residentials.filter((function(t){return t.communityId===Number(e)})):[]}},{key:"getRegions",value:function(){return this.cache.regions}},{key:"getCommunities",value:function(){return this.cache.communities}},{key:"getResidentials",value:function(){return this.cache.residentials}},{key:"getLoadingStatus",value:function(){return Object(a["a"])({},this.loading)}},{key:"getRegionById",value:function(e){if(!e)return null;var t=Number(e),n=this.cache.regionMap.get(t);if(!n){console.warn("未找到ID为".concat(t,"的区域信息，当前区域Map大小:"),this.cache.regionMap.size);var i=this.cache.regions.find((function(e){return e.id===t}));if(i)return console.log("从数组中找到ID为".concat(t,"的区域信息:"),i),i}return n||null}},{key:"getRegionPath",value:function(e){if(!e)return[];var t=this.getRegionById(e);if(!t)return console.warn("获取区域路径失败: 未找到ID为".concat(e,"的区域")),[];var n=[t];if(t.pid&&0!==t.pid){var a=this.getRegionPath(t.pid);return[].concat(Object(i["a"])(a),[t])}return n}},{key:"getRegionFullName",value:function(e){if(!e)return"";var t=this.getRegionPath(e);if(!t.length)return console.warn("获取区域完整路径名称失败: 路径为空"),"";var n=t.map((function(e){return e.name})).join("/");return n}},{key:"getCommunityById",value:function(e){if(!e)return null;var t=Number(e),n=this.cache.communityMap.get(t);if(!n){console.warn("未找到ID为".concat(t,"的社区信息，当前社区Map大小:"),this.cache.communityMap.size);var i=this.cache.communities.find((function(e){return e.id===t}));if(i)return console.log("从数组中找到ID为".concat(t,"的社区信息:"),i),i}return n||null}},{key:"getResidentialById",value:function(e){if(!e)return null;var t=Number(e),n=this.cache.residentialMap.get(t);if(!n){var i=this.cache.residentials.find((function(e){return e.id===t}));if(i)return i}return n||null}}])}(),b=new p;t["a"]=b},fe01:function(e,t,n){}}]);