(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d2304f6"],{ec79:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",attrs:{model:e.queryParams,size:"small",inline:!0,"label-width":"68px"}},[a("el-form-item",{attrs:{label:"id",prop:"id"}},[a("el-input",{attrs:{placeholder:"请输入id",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handle<PERSON>(t)}},model:{value:e.queryParams.id,callback:function(t){e.$set(e.queryParams,"id",t)},expression:"queryParams.id"}})],1),a("el-form-item",{attrs:{label:"性别",prop:"gender"}},[a("el-select",{attrs:{placeholder:"请选择性别",clearable:""},model:{value:e.queryParams.gender,callback:function(t){e.$set(e.queryParams,"gender",t)},expression:"queryParams.gender"}},e._l(e.dict.type.sys_user_sex,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",{attrs:{label:"状态",prop:"status"}},[a("el-select",{attrs:{placeholder:"请选择状态",clearable:""},model:{value:e.queryParams.status,callback:function(t){e.$set(e.queryParams,"status",t)},expression:"queryParams.status"}},e._l(e.dict.type.user_status,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",{attrs:{label:"是否删除",prop:"deleted"}},[a("el-select",{attrs:{placeholder:"请选择是否删除",clearable:""},model:{value:e.queryParams.deleted,callback:function(t){e.$set(e.queryParams,"deleted",t)},expression:"queryParams.deleted"}},e._l(e.dict.type.sys_normal_disable,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),a("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1),a("el-row",{staticClass:"mb8",attrs:{gutter:10}},[a("el-col",{attrs:{span:1.5}}),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["operation:userInfo:export"],expression:"['operation:userInfo:export']"}],attrs:{type:"warning",plain:"",icon:"el-icon-download",size:"mini"},on:{click:e.handleExport}},[e._v("导出 ")])],1),a("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.userInfoList},on:{"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"55",align:"center"}}),a("el-table-column",{attrs:{label:"id",align:"center",prop:"id"}}),a("el-table-column",{attrs:{label:"昵称",align:"center",width:"150",prop:"nickname"}}),a("el-table-column",{attrs:{label:"金币",align:"center",prop:"silver"}}),a("el-table-column",{attrs:{label:"用户头像图片",align:"center",prop:"avatar",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("image-preview",{attrs:{src:e.filePrefix+t.row.avatar,width:50,height:50}})]}}])}),a("el-table-column",{attrs:{label:"性别",align:"center",prop:"gender"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("dict-tag",{attrs:{options:e.dict.type.sys_user_sex,value:t.row.gender}})]}}])}),a("el-table-column",{attrs:{label:"生日",align:"center",prop:"birthday",width:"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(e.parseTime(t.row.birthday,"{y}-{m}-{d}")))])]}}])}),a("el-table-column",{attrs:{label:"等级",align:"center",prop:"userLevel"}}),a("el-table-column",{attrs:{label:"状态",align:"center",prop:"status"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("dict-tag",{attrs:{options:e.dict.type.user_status,value:t.row.status}})]}}])}),a("el-table-column",{attrs:{label:"创建时间",align:"center",prop:"createTime",width:"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(e.parseTime(t.row.createTime,"{y}-{m}-{d}")))])]}}])}),a("el-table-column",{attrs:{label:"更新时间",align:"center",prop:"updateTime",width:"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(e.parseTime(t.row.updateTime,"{y}-{m}-{d}")))])]}}])}),a("el-table-column",{attrs:{label:"删除状态",align:"center",prop:"deleted"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("dict-tag",{attrs:{options:e.dict.type.sys_normal_disable,value:t.row.deleted}})]}}])}),a("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["operation:userInfo:edit"],expression:"['operation:userInfo:edit']"}],attrs:{size:"mini",type:"text",icon:"el-icon-edit"},on:{click:function(a){return e.handleUpdate(t.row)}}},[e._v("修改 ")])]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}}),a("el-dialog",{attrs:{title:e.title,visible:e.open,width:"500px","append-to-body":""},on:{"update:visible":function(t){e.open=t}}},[a("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"80px"}},[a("el-form-item",{attrs:{label:"金币",prop:"silver"}},[a("el-input",{attrs:{placeholder:"请输入金币"},model:{value:e.form.silver,callback:function(t){e.$set(e.form,"silver",t)},expression:"form.silver"}})],1),a("el-form-item",{attrs:{label:"性别",prop:"gender"}},[a("el-select",{attrs:{placeholder:"请选择性别"},model:{value:e.form.gender,callback:function(t){e.$set(e.form,"gender",t)},expression:"form.gender"}},e._l(e.dict.type.sys_user_sex,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:parseInt(e.value)}})})),1)],1),a("el-form-item",{attrs:{label:"用户等级",prop:"userLevel"}},[a("el-input",{attrs:{placeholder:"请输入用户等级"},model:{value:e.form.userLevel,callback:function(t){e.$set(e.form,"userLevel",t)},expression:"form.userLevel"}})],1),a("el-form-item",{attrs:{label:"状态",prop:"status"}},[a("el-select",{attrs:{placeholder:"请选择状态"},model:{value:e.form.status,callback:function(t){e.$set(e.form,"status",t)},expression:"form.status"}},e._l(e.dict.type.user_status,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:parseInt(e.value)}})})),1)],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),a("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1)],1)},l=[],n=a("5530"),s=(a("d81d"),a("d3b7"),a("0643"),a("a573"),a("b775"));function o(e){return Object(s["a"])({url:"/operation/userInfo/list",method:"get",params:e})}function i(e){return Object(s["a"])({url:"/operation/userInfo/"+e,method:"get"})}function u(e){return Object(s["a"])({url:"/operation/userInfo",method:"post",data:e})}function c(e){return Object(s["a"])({url:"/operation/userInfo",method:"put",data:e})}function d(e){return Object(s["a"])({url:"/operation/userInfo/"+e,method:"delete"})}var p={name:"UserInfo",dicts:["user_status","sys_user_sex","sys_normal_disable"],data:function(){return{loading:!0,filePrefix:"https://ei-image.funnyswap.com/",ids:[],single:!0,multiple:!0,showSearch:!0,total:0,userInfoList:[],title:"",open:!1,queryParams:{pageNum:1,pageSize:10,id:null,gender:null,lastLoginIp:null,status:null,deleted:null},form:{},rules:{password:[{required:!0,message:"用户密码不能为空",trigger:"blur"}],silver:[{required:!0,message:"金币不能为空",trigger:"blur"}],lastLoginIp:[{required:!0,message:"最近登录ip地址不能为空",trigger:"blur"}],status:[{required:!0,message:"0 可用, 1 禁用, 2 注销不能为空",trigger:"change"}],createTime:[{required:!0,message:"创建时间不能为空",trigger:"blur"}],deleted:[{required:!0,message:"逻辑删除不能为空",trigger:"change"}]}}},created:function(){this.getList()},methods:{getList:function(){var e=this;this.loading=!0,o(this.queryParams).then((function(t){e.userInfoList=t.rows,e.total=t.total,e.loading=!1}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={id:null,mobile:null,openid:null,password:null,nickname:null,silver:null,avatar:null,gender:null,birthday:null,lastLoginTime:null,lastLoginIp:null,userLevel:null,status:null,createTime:null,updateTime:null,deleted:null},this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.id})),this.single=1!==e.length,this.multiple=!e.length},handleAdd:function(){this.reset(),this.open=!0,this.title="添加用户信息"},handleUpdate:function(e){var t=this;this.reset();var a=e.id||this.ids;i(a).then((function(e){t.form=e.data,t.open=!0,t.title="修改用户信息"}))},submitForm:function(){var e=this;this.$refs["form"].validate((function(t){t&&(null!=e.form.id?c(e.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.open=!1,e.getList()})):u(e.form).then((function(t){e.$modal.msgSuccess("新增成功"),e.open=!1,e.getList()})))}))},handleDelete:function(e){var t=this,a=e.id||this.ids;this.$modal.confirm('是否确认删除用户信息编号为"'+a+'"的数据项？').then((function(){return d(a)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleExport:function(){this.download("operation/userInfo/export",Object(n["a"])({},this.queryParams),"userInfo_".concat((new Date).getTime(),".xlsx"))}}},m=p,f=a("2877"),h=Object(f["a"])(m,r,l,!1,null,null,null);t["default"]=h.exports}}]);