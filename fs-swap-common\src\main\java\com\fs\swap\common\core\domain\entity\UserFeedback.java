package com.fs.swap.common.core.domain.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fs.swap.common.annotation.Excel;
import com.fs.swap.common.core.domain.BaseEntity;
import lombok.Data;

import java.util.Date;

/**
 * 用户反馈对象 user_feedback
 *
 * <AUTHOR>
 * @date 2025-06-27
 */
@Data
public class UserFeedback extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 反馈ID */
    private Long id;

    /** 用户ID */
    @Excel(name = "用户ID")
    private Long userId;

    /** 反馈内容 */
    @Excel(name = "反馈内容")
    private String content;

    /** 图片地址，多个用逗号分隔 */
    @Excel(name = "图片地址")
    private String images;

    /** 联系方式 */
    @Excel(name = "联系方式")
    private String contact;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    // 非数据库字段，用于关联查询
    private transient String nickname;
    private transient String avatar;
}
