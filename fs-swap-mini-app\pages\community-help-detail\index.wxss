/* pages/community-help-detail/index.wxss */
page {
  background: #f7f8fa;
  min-height: 100vh;
}

/* 隐藏所有滚动条 */
page::-webkit-scrollbar,
::-webkit-scrollbar {
  width: 0 !important;
  height: 0 !important;
  background: transparent !important;
  display: none !important;
}

.container {
  background: #f7f8fa;
  min-height: 100vh;
  padding-bottom: 120rpx; /* 为底部操作栏留出空间 */
}

.detail-content {
  padding: 20rpx 24rpx;
  position: relative;
}

/* 用户信息区域 */
.user-section {
  background-color: white;
  border-radius: 24rpx;
  padding: 28rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.03);
}

.user-info {
  display: flex;
  align-items: center;
}

.user-avatar {
  width: 88rpx;
  height: 88rpx;
  border-radius: 50%;
  margin-right: 28rpx;
  border: 2rpx solid #f0f0f0;
}

.user-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.user-name-row {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.user-name {
  font-size: 34rpx;
  font-weight: 600;
  color: #333;
  flex: 1;
}

/* 发布类型标签 */
.publish-type-tag {
  padding: 6rpx 12rpx;
  border-radius: 16rpx;
  font-size: 22rpx;
  font-weight: 500;
  color: white;
  text-align: center;
  min-width: 50rpx;
}

.request-tag {
  background: linear-gradient(135deg, #ff6b6b, #ff5252);
}

.offer-tag {
  background: linear-gradient(135deg, #4ecdc4, #26a69a);
}

.tag-text {
  font-size: 22rpx;
  font-weight: 500;
}

.user-meta {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.publish-time {
  font-size: 26rpx;
  color: #969799;
}

.meta-divider {
  font-size: 26rpx;
  color: #d4d6d9;
}

.view-count {
  display: flex;
  align-items: center;
  gap: 4rpx;
}

.view-text {
  font-size: 26rpx;
  color: #969799;
}

/* 内容区域 */
.content-section {
  background-color: white;
  border-radius: 24rpx;
  padding: 28rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.03);
}

/* 标题和分类区域 */
.title-section {
  margin-bottom: 24rpx;
}

.title-row {
  display: flex;
  align-items: flex-start;
  gap: 16rpx;
}

.help-title {
  font-size: 38rpx;
  font-weight: 600;
  color: #333;
  line-height: 1.4;
  word-break: break-word;
  flex: 1;
}

/* 分类标签 */
.category-tag {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 4rpx 12rpx;
  background: rgba(59, 127, 255, 0.08);
  border: 1rpx solid rgba(59, 127, 255, 0.15);
  border-radius: 16rpx;
  margin-top: 4rpx;
  flex-shrink: 0;
}

.category-text {
  font-size: 20rpx;
  color: #3b7fff;
  font-weight: 500;
  line-height: 1;
}

.help-content {
  font-size: 30rpx;
  color: #646566;
  line-height: 1.7;
  margin-bottom: 28rpx;
  word-break: break-word;
  white-space: pre-wrap;
}

/* 截止时间样式 */
.end-time-section {
  margin-top: 28rpx;
}

.end-time {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 16rpx;
  background: rgba(255, 107, 53, 0.06);
  border-radius: 12rpx;
  border: 1rpx solid rgba(255, 107, 53, 0.15);
}

.end-time-text {
  font-size: 26rpx;
  color: #ff6b35;
  font-weight: 500;
}

/* 图片展示 */
.images-container {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
  margin-top: 24rpx;
}

.image-item {
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  position: relative;
}

.image-item:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.12);
}

/* 单张图片 - 占满宽度 */
.single-image {
  width: 100%;
  max-height: 400rpx;
}

/* 两张图片 - 每张占一半 */
.two-images {
  width: calc(50% - 6rpx);
}

/* 三张及以上图片 - 每行三张 */
.three-images {
  width: calc(33.333% - 8rpx);
}

/* 图片高度统一设置 */
.single-image {
  aspect-ratio: 16/9;
  max-height: 400rpx;
}

.two-images {
  aspect-ratio: 1/1;
  height: 200rpx;
}

.three-images {
  aspect-ratio: 1/1;
  height: 180rpx;
}

/* 更多图片提示遮罩 */
.more-images-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12rpx;
  backdrop-filter: blur(2rpx);
}

.more-text {
  color: white;
  font-size: 32rpx;
  font-weight: 600;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

/* 图片加载状态优化 */
.image-item .van-image {
  background-color: #f7f8fa;
  border-radius: 12rpx;
  width: 100%;
  height: 100%;
}





/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 120rpx 0;
}

/* 底部操作栏 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 110rpx;
  background-color: rgba(255, 255, 255, 0.95);
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.08);
  z-index: 99;
  padding: 0 24rpx;
  border-top: 1rpx solid #f5f5f5;
  backdrop-filter: blur(20px);
}

.action-group {
  display: flex;
  align-items: center;
}

.action-group.left {
  width: 50%;
}

.action-group.right {
  width: 50%;
  justify-content: flex-end;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 0 20rpx;
  height: 100%;
  position: relative;
  background: none;
  border: none;
  margin: 0;
  line-height: normal;
  border-radius: 0;
  font-size: inherit;
  text-align: center;
  color: inherit;
  box-sizing: border-box;
}

.action-item:active {
  opacity: 0.8;
}

.action-hover {
  opacity: 0.7;
  transform: scale(0.95);
}

.action-item text {
  font-size: 20rpx;
  color: #999999;
  margin-top: 4rpx;
}

/* 移除button的默认边框 */
.share-button::after {
  border: none;
}

.share-text {
  color: #1989fa !important;
  font-weight: 500;
}

/* 分享按钮脉冲动画 */
.share-button {
  position: relative;
  overflow: hidden;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

/* 操作按钮组样式 */
.action-button-group {
  display: flex;
  align-items: center;
  gap: 16rpx;
  justify-content: flex-end;
  width: 100%;
}

.action-button {
  height: 80rpx;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 20rpx;
  font-size: 28rpx;
  font-weight: 500;
  padding: 0 36rpx;
  transition: all 0.2s;
  width: 180rpx;
}

.action-button:active {
  transform: scale(0.97);
}

.action-button van-icon {
  margin-right: 10rpx;
}

.button-hover {
  opacity: 0.8;
  transform: scale(0.95);
}

/* 联系按钮样式 */
.action-button.contact {
  background-color: #52c41a;
  color: #ffffff;
  width: 180rpx;
  min-width: 180rpx;
  box-shadow: 0 6rpx 16rpx rgba(82, 196, 26, 0.35);
  font-size: 28rpx;
  font-weight: 500;
}

.action-button.contact:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(82, 196, 26, 0.2);
}

/* 编辑按钮样式 */
.action-button.edit {
  background-color: #1989fa;
  color: #ffffff;
  min-width: 140rpx;
  width: 140rpx;
  box-shadow: 0 6rpx 16rpx rgba(25, 137, 250, 0.35);
  font-size: 28rpx;
  font-weight: 500;
}

.action-button.edit:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(25, 137, 250, 0.2);
}



/* 联系确认弹框样式 */
.contact-dialog-content {
  padding: 30rpx;
}

.contact-dialog-item {
  display: flex;
  margin-bottom: 20rpx;
  align-items: center;
}

.contact-dialog-label {
  font-size: 28rpx;
  color: #666666;
  width: 120rpx;
}

.contact-dialog-value {
  font-size: 28rpx;
  color: #333333;
  flex: 1;
  font-weight: 500;
}

.contact-dialog-notice {
  margin-top: 10rpx;
  margin-bottom: 20rpx;
  font-size: 22rpx;
  color: #ff4d4f;
  text-align: left;
  padding-left: 0;
}

.original-price {
  text-decoration: line-through;
  color: #999999;
  margin-right: 8rpx;
  font-size: 24rpx;
}

/* 联系结果弹框样式 */
.contact-result-content {
  padding: 48rpx 32rpx 40rpx;
  text-align: center;
  background-color: #ffffff;
}

.success-icon {
  margin-bottom: 48rpx;
  display: flex;
  justify-content: center;
  animation: success-bounce 0.6s ease-out;
}

@keyframes success-bounce {
  0% {
    transform: scale(0.3);
    opacity: 0;
  }
  50% {
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.result-message {
  margin-bottom: 32rpx;
}

.message-text {
  font-size: 32rpx;
  color: #333333;
  line-height: 1.5;
}

.contact-tip {
  margin-top: 32rpx;
  padding: 24rpx;
  background-color: #f8f9fc;
  border-radius: 16rpx;
  border: 1rpx solid #e8eaed;
}

.tip-item {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16rpx;
  gap: 8rpx;
}

.tip-item:last-child {
  margin-bottom: 0;
}

.tip-text {
  font-size: 28rpx;
  color: #666666;
  line-height: 1.4;
}

/* 下架按钮样式 */
.action-button.offline {
  background-color: #ff4d4f;
  color: #ffffff;
  min-width: 140rpx;
  width: 140rpx;
  box-shadow: 0 6rpx 16rpx rgba(255, 77, 79, 0.35);
  font-size: 28rpx;
  font-weight: 500;
}

.action-button.offline:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(255, 77, 79, 0.2);
}

/* 重新上架按钮样式 */
.action-button.online {
  background-color: #13c2c2;
  color: #ffffff;
  min-width: 140rpx;
  width: 140rpx;
  box-shadow: 0 6rpx 16rpx rgba(19, 194, 194, 0.35);
  font-size: 28rpx;
  font-weight: 500;
}

.action-button.online:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(19, 194, 194, 0.2);
}

/* 下架确认弹窗样式 */
.offline-dialog-content {
  padding: 30rpx;
  text-align: center;
  font-size: 28rpx;
  color: #333333;
  line-height: 1.6;
}

/* 重新上架确认弹窗样式 */
.online-dialog-content {
  padding: 30rpx;
  text-align: center;
  font-size: 28rpx;
  color: #333333;
  line-height: 1.6;
}


