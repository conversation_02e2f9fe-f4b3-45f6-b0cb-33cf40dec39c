/**
 * 系统信息服务 - 统一管理系统配置信息的获取、缓存和处理
 * 包括：分类数据、标签数据、审核状态、文件服务器URL等
 */
const api = require('../config/api');

// 缓存配置
const CACHE_CONFIG = {
  SYSTEM_INFO_CACHE_TIME: 30 * 60 * 1000, // 30分钟缓存有效期（降低缓存时间）
  CACHE_KEY: 'systemInfo',
  APP_LAUNCH_KEY: 'systemInfo_app_launch_time' // 记录应用启动时间的key
};

/**
 * 系统信息服务类
 */
class SystemInfoService {
  constructor() {
    this.cache = null; // 内存缓存
    this.loading = false; // 防重复加载标识
    this.loadPromise = null; // 加载Promise，防止并发请求
    this.appLaunchTime = null; // 应用启动时间
  }

  /**
   * 获取系统信息（智能缓存）
   * @param {boolean} forceRefresh 是否强制刷新
   * @returns {Promise<Object>} 系统信息对象
   */
  async getSystemInfo(forceRefresh = false) {
    // 如果正在加载中，返回加载Promise
    if (this.loading && this.loadPromise) {
      return this.loadPromise;
    }

    // 检查是否需要因为应用启动而刷新
    const shouldRefreshOnLaunch = this.shouldRefreshOnAppLaunch();

    // 检查内存缓存
    if (!forceRefresh && !shouldRefreshOnLaunch && this.cache && this.isCacheValid(this.cache)) {
      return Promise.resolve(this.cache);
    }

    // 检查本地存储缓存
    if (!forceRefresh && !shouldRefreshOnLaunch) {
      const localCache = this.getLocalCache();
      if (localCache && this.isCacheValid(localCache)) {
        this.cache = localCache; // 更新内存缓存
        return Promise.resolve(localCache);
      }
    }

    // 从服务器获取最新数据
    this.loading = true;
    this.loadPromise = this.fetchFromServer()
      .finally(() => {
        this.loading = false;
        this.loadPromise = null;
      });

    return this.loadPromise;
  }

  /**
   * 从服务器获取系统信息
   * @returns {Promise<Object>} 系统信息对象
   */
  async fetchFromServer() {
    try {
      const res = await api.systemInfo();

      if (res.code === 200 && res.data) {
        const systemData = res.data;

        // 添加缓存时间戳
        systemData.cacheTime = Date.now();

        // 保存到本地缓存
        this.saveToLocalCache(systemData);

        // 更新内存缓存
        this.cache = systemData;

        // 更新应用启动时间记录
        this.updateAppLaunchTime();

        return systemData;
      } else {
        throw new Error(res.msg || '获取系统信息失败');
      }
    } catch (error) {
      // 网络错误时，尝试使用过期的本地缓存
      const localCache = this.getLocalCache();
      if (localCache) {
        this.cache = localCache;
        return localCache;
      }

      // 完全无缓存时，抛出错误
      throw new Error('无法获取系统信息，请检查网络连接');
    }
  }

  /**
   * 获取本地缓存
   * @returns {Object|null} 缓存的系统信息对象
   */
  getLocalCache() {
    try {
      const cachedData = wx.getStorageSync(CACHE_CONFIG.CACHE_KEY);
      return cachedData || null;
    } catch (error) {
      return null;
    }
  }

  /**
   * 保存到本地缓存
   * @param {Object} systemData 系统信息对象
   */
  saveToLocalCache(systemData) {
    try {
      wx.setStorageSync(CACHE_CONFIG.CACHE_KEY, systemData);
    } catch (error) {
    }
  }

  /**
   * 检查缓存是否有效
   * @param {Object} cachedData 缓存数据
   * @returns {boolean} 是否有效
   */
  isCacheValid(cachedData) {
    if (!cachedData || !cachedData.cacheTime) {
      return false;
    }

    const cacheAge = Date.now() - cachedData.cacheTime;
    return cacheAge <= CACHE_CONFIG.SYSTEM_INFO_CACHE_TIME;
  }

  /**
   * 检查是否应该因为应用启动而刷新配置
   * 每次应用启动时都会刷新一次配置，确保获取最新的后端配置
   * @returns {boolean} 是否需要刷新
   */
  shouldRefreshOnAppLaunch() {
    try {
      // 获取当前应用启动时间
      const currentLaunchTime = this.getCurrentAppLaunchTime();

      // 获取上次获取配置时的应用启动时间
      const lastConfigLaunchTime = wx.getStorageSync(CACHE_CONFIG.APP_LAUNCH_KEY);

      // 如果应用启动时间不同，说明是新的应用启动，需要刷新配置
      return !lastConfigLaunchTime || currentLaunchTime !== lastConfigLaunchTime;
    } catch (error) {
      // 出错时默认刷新
      return true;
    }
  }

  /**
   * 获取当前应用启动时间
   * 如果还没有记录，则创建一个新的启动时间
   * @returns {number} 应用启动时间戳
   */
  getCurrentAppLaunchTime() {
    if (!this.appLaunchTime) {
      // 尝试从全局App实例获取启动时间，如果没有则使用当前时间
      const app = getApp();
      if (app && app.launchTime) {
        this.appLaunchTime = app.launchTime;
      } else {
        // 如果App实例没有启动时间，使用当前时间作为启动时间
        this.appLaunchTime = Date.now();
      }
    }
    return this.appLaunchTime;
  }

  /**
   * 更新应用启动时间记录
   * 在成功获取配置后调用，记录本次获取配置时的应用启动时间
   */
  updateAppLaunchTime() {
    try {
      const currentLaunchTime = this.getCurrentAppLaunchTime();
      wx.setStorageSync(CACHE_CONFIG.APP_LAUNCH_KEY, currentLaunchTime);
    } catch (error) {
      // 静默处理错误
    }
  }

  /**
   * 清理缓存
   */
  clearCache() {
    try {
      // 清理内存缓存
      this.cache = null;

      // 清理本地缓存
      wx.removeStorageSync(CACHE_CONFIG.CACHE_KEY);

      // 清理应用启动时间记录
      wx.removeStorageSync(CACHE_CONFIG.APP_LAUNCH_KEY);

      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * 强制刷新系统信息
   * @returns {Promise<Object>} 最新的系统信息
   */
  async forceRefresh() {
    this.clearCache();
    return this.getSystemInfo(true);
  }

  /**
   * 获取缓存状态信息
   * @returns {Object} 缓存状态对象
   */
  getCacheStatus() {
    try {
      const localCache = this.getLocalCache();
      const shouldRefreshOnLaunch = this.shouldRefreshOnAppLaunch();
      const currentLaunchTime = this.getCurrentAppLaunchTime();
      const lastConfigLaunchTime = wx.getStorageSync(CACHE_CONFIG.APP_LAUNCH_KEY);

      if (!localCache || !localCache.cacheTime) {
        return {
          hasCache: false,
          message: '无缓存数据',
          shouldRefreshOnLaunch: shouldRefreshOnLaunch,
          currentLaunchTime: new Date(currentLaunchTime).toLocaleString(),
          lastConfigLaunchTime: lastConfigLaunchTime ? new Date(lastConfigLaunchTime).toLocaleString() : '无记录'
        };
      }

      const cacheAge = Date.now() - localCache.cacheTime;
      const isExpired = cacheAge > CACHE_CONFIG.SYSTEM_INFO_CACHE_TIME;

      return {
        hasCache: true,
        isExpired: isExpired,
        cacheTime: new Date(localCache.cacheTime).toLocaleString(),
        cacheAge: Math.round(cacheAge / (60 * 1000)), // 分钟
        maxAge: Math.round(CACHE_CONFIG.SYSTEM_INFO_CACHE_TIME / (60 * 1000)), // 分钟
        message: shouldRefreshOnLaunch ? '应用启动需要刷新' : (isExpired ? '缓存已过期' : '缓存有效'),
        isDefault: localCache.isDefault || false,
        shouldRefreshOnLaunch: shouldRefreshOnLaunch,
        currentLaunchTime: new Date(currentLaunchTime).toLocaleString(),
        lastConfigLaunchTime: lastConfigLaunchTime ? new Date(lastConfigLaunchTime).toLocaleString() : '无记录'
      };
    } catch (error) {
      return {
        hasCache: false,
        error: error.message,
        message: '获取状态失败'
      };
    }
  }

  // ========== 通用方法 ==========

  /**
   * 通用获取字典数据方法
   * @param {string} fieldName 字段名
   * @returns {Promise<Array>} 字典数据列表
   */
  async getDictData(fieldName) {
    const systemInfo = await this.getSystemInfo();
    return systemInfo[fieldName] || [];
  }

  /**
   * 通用格式化字典数据方法
   * @param {Array} dictData 字典数据数组
   * @param {boolean} includeAll 是否包含"全部"选项
   * @returns {Array} 格式化的数据列表
   */
  formatDictData(dictData, includeAll = false) {
    const formatted = [];
    
    // 添加"全部"选项
    if (includeAll) {
      formatted.push({ id: 'all', name: '全部' });
    }
    
    // 转换后端数据为前端格式
    dictData.forEach(item => {
      formatted.push({
        id: item.dictValue,
        name: item.dictLabel
      });
    });
    
    return formatted;
  }

  /**
   * 通用创建名称映射方法
   * @param {Array} dictData 字典数据数组
   * @returns {Object} 名称映射对象
   */
  createNameMap(dictData) {
    const nameMap = {};
    dictData.forEach(item => {
      nameMap[item.dictValue] = item.dictLabel;
    });
    return nameMap;
  }

  // ========== 便捷方法 ==========

  /**
   * 获取文件服务器URL
   * @returns {Promise<string>} 文件服务器URL
   */
  async getFileUrl() {
    const systemInfo = await this.getSystemInfo();
    return systemInfo.fileUrl || '';
  }

  async getProductSilver() {
	const systemInfo = await this.getSystemInfo();
	return systemInfo.productSilver || '2';
      }

  /**
   * 获取互助服务费用
   * @returns {Promise<string>} 互助服务费用
   */
  async getHelpSilver() {
    const systemInfo = await this.getSystemInfo();
    return systemInfo.helpSilver || '5';
  }
  /**
   * 获取社区服务分类列表
   * @returns {Promise<Array>} 分类列表
   */
  async getCommunityServiceCategories() {
    return this.getDictData('communityServiceCategories');
  }

  /**
   * 获取格式化的分类数据
   * @returns {Promise<Array>} 格式化的分类列表
   */
  async getFormattedCategories() {
    const categories = await this.getCommunityServiceCategories();
    return this.formatDictData(categories, true);
  }

  /**
   * 获取分类名称映射
   * @returns {Promise<Object>} 分类名称映射对象
   */
  async getCategoryNameMap() {
    const categories = await this.getCommunityServiceCategories();
    return this.createNameMap(categories);
  }

  /**
   * 获取社区服务标签列表
   * @returns {Promise<Array>} 标签列表
   */
  async getCommunityServiceTags() {
    const systemInfo = await this.getSystemInfo();
    return systemInfo.communityServiceTags || [];
  }

  /**
   * 获取通用审核状态列表
   * 适用于所有需要审核状态的功能模块（社区服务、投稿等）
   * @returns {Promise<Array>} 审核状态列表，包含状态码、文本、颜色等信息
   */
  async getAuditStatusList() {
    const systemInfo = await this.getSystemInfo();
    return systemInfo.common_audit_status || [];
  }

  /**
   * 获取审核状态映射对象
   * 将审核状态数组转换为以状态码为key的映射对象，便于快速查找
   * @returns {Promise<Object>} 状态码到状态信息的映射
   */
  async getAuditStatusMap() {
    const statusList = await this.getAuditStatusList();
    const statusMap = {};
    statusList.forEach(status => {
      if (status.code !== undefined) {
        statusMap[status.code] = status;
      }
    });
    return statusMap;
  }

  /**
   * 处理图片URL，添加文件服务器前缀
   * @param {string} imagePath 图片路径
   * @returns {Promise<string>} 完整的图片URL
   */
  async processImageUrl(imagePath) {
    if (!imagePath || imagePath.trim() === '') {
      return '';
    }
    
    // 如果已经是完整URL，直接返回
    if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {
      return imagePath;
    }
    
    // 获取文件服务器URL并拼接
    const fileUrl = await this.getFileUrl();
    return fileUrl ? fileUrl + imagePath.trim() : imagePath.trim();
  }

  /**
   * 批量处理图片URL列表
   * @param {string} imagesStr 逗号分隔的图片路径字符串
   * @returns {Promise<Object>} 包含images字符串和imageUrl第一张图片的对象
   */
  async processImageUrls(imagesStr) {
    if (!imagesStr || imagesStr.trim() === '') {
      return { images: '', imageUrl: '' };
    }
    
    const fileUrl = await this.getFileUrl();
    const imageArray = imagesStr.split(',');
    
    const fullImageUrls = imageArray.map(img => {
      if (!img || !img.trim()) return '';
      
      const trimmedImg = img.trim();
      if (trimmedImg.startsWith('http://') || trimmedImg.startsWith('https://')) {
        return trimmedImg;
      }
      
      return fileUrl ? fileUrl + trimmedImg : trimmedImg;
    }).filter(url => url);
    
    return {
      images: fullImageUrls.join(','),
      imageUrl: fullImageUrls.length > 0 ? fullImageUrls[0] : ''
    };
  }

  /**
   * 获取社区电话分类列表
   * @returns {Promise<Array>} 电话分类列表
   */
  async getCommunityPhoneCategories() {
    return this.getDictData('communityPhoneCategories');
  }

  /**
   * 获取格式化的电话分类数据
   * @returns {Promise<Array>} 格式化的电话分类列表
   */
  async getFormattedPhoneCategories() {
    const categories = await this.getCommunityPhoneCategories();
    return this.formatDictData(categories, true);
  }

  /**
   * 获取电话分类名称映射
   * @returns {Promise<Object>} 电话分类名称映射对象
   */
  async getPhoneCategoryNameMap() {
    const categories = await this.getCommunityPhoneCategories();
    return this.createNameMap(categories);
  }

  /**
   * 获取碳豆类型类型列表
   * @returns {Promise<Array>} 碳豆类型类型列表
   */
  async getSilverEventTypes() {
    return this.getDictData('silverEventType');
  }

  /**
   * 获取社区互助需求分类列表
   * @returns {Promise<Array>} 分类列表
   */
  async getCommunityHelpRequireCategories() {
    return this.getDictData('communityHelpRequireCategories');
  }

  /**
   * 获取社区互助服务分类列表
   * @returns {Promise<Array>} 分类列表
   */
  async getCommunityHelpServiceCategories() {
    return this.getDictData('communityHelpServiceCategories');
  }

  /**
   * 获取格式化的互助需求分类数据
   * @returns {Promise<Array>} 格式化的分类列表
   */
  async getFormattedHelpRequireCategories() {
    const categories = await this.getCommunityHelpRequireCategories();
    return this.formatDictData(categories, true);
  }

  /**
   * 获取格式化的互助服务分类数据
   * @returns {Promise<Array>} 格式化的分类列表
   */
  async getFormattedHelpServiceCategories() {
    const categories = await this.getCommunityHelpServiceCategories();
    return this.formatDictData(categories, true);
  }

  /**
   * 获取互助需求分类名称映射
   * @returns {Promise<Object>} 分类名称映射对象
   */
  async getHelpRequireCategoryNameMap() {
    const categories = await this.getCommunityHelpRequireCategories();
    return this.createNameMap(categories);
  }

  /**
   * 获取互助服务分类名称映射
   * @returns {Promise<Object>} 分类名称映射对象
   */
  async getHelpServiceCategoryNameMap() {
    const categories = await this.getCommunityHelpServiceCategories();
    return this.createNameMap(categories);
  }

  /**
   * 获取格式化的互助分类数据（兼容旧方法）
   * @returns {Promise<Array>} 格式化的分类列表
   */
  async getFormattedHelpCategories() {
    const categories = await this.getCommunityHelpRequireCategories();
    return this.formatDictData(categories, true);
  }

  /**
   * 获取互助分类名称映射（兼容旧方法）
   * @returns {Promise<Object>} 分类名称映射对象
   */
  async getHelpCategoryNameMap() {
    const categories = await this.getCommunityHelpRequireCategories();
    return this.createNameMap(categories);
  }

  /**
   * 获取社区互助发布类型列表
   * @returns {Promise<Array>} 发布类型列表
   */
  async getCommunityHelpPublishTypes() {
    return this.getDictData('communityHelpPublishTypes');
  }

  /**
   * 获取格式化的互助发布类型数据
   * @returns {Promise<Array>} 格式化的发布类型列表
   */
  async getFormattedHelpPublishTypes() {
    const publishTypes = await this.getCommunityHelpPublishTypes();
    return this.formatDictData(publishTypes, true);
  }

  /**
   * 获取互助发布类型名称映射
   * @returns {Promise<Object>} 发布类型名称映射对象
   */
  async getHelpPublishTypeNameMap() {
    const publishTypes = await this.getCommunityHelpPublishTypes();
    return this.createNameMap(publishTypes);
  }

  /**
   * 获取格式化的碳豆类型类型数据
   * @returns {Promise<Array>} 格式化的碳豆类型类型列表
   */
  async getFormattedSilverEventTypes() {
    const types = await this.getSilverEventTypes();
    return this.formatDictData(types, false);
  }

  /**
   * 获取碳豆类型类型名称映射
   * @returns {Promise<Object>} 碳豆类型类型名称映射对象
   */
  async getSilverEventTypeNameMap() {
    const types = await this.getSilverEventTypes();
    return this.createNameMap(types);
  }

  /**
   * 获取活动分类列表
   * @returns {Promise<Array>} 活动分类列表
   */
  async getActivityCategories() {
    return this.getDictData('activityCategories');
  }

  /**
   * 获取格式化的活动分类数据
   * @returns {Promise<Array>} 格式化的活动分类列表
   */
  async getFormattedActivityCategories() {
    const categories = await this.getActivityCategories();
    return this.formatDictData(categories, true);
  }

  /**
   * 获取活动分类名称映射
   * @returns {Promise<Object>} 活动分类名称映射对象
   */
  async getActivityCategoryNameMap() {
    const categories = await this.getActivityCategories();
    return this.createNameMap(categories);
  }
}

// 创建单例实例
const systemInfoService = new SystemInfoService();

module.exports = systemInfoService; 