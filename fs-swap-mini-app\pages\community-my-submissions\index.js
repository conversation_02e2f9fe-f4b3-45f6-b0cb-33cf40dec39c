const api = require('../../config/api.js')
const userUtils = require('../../utils/user.js')
const systemInfoService = require('../../services/systemInfo.js')
const util = require('../../utils/util.js')

Page({
  data: {
    // 页面状态
    loading: false,
    refreshing: false,
    loadingMore: false,
    hasMore: true,
    
    // 列表数据
    submissionList: [],
    
    // 分页参数
    pageNum: 1,
    pageSize: 10,
    total: 0,
    
    // 标签页
    activeTab: 0,
    tabs: [
      { title: '常用电话', key: 'phone' },
      { title: '周边服务', key: 'nearby' }
    ],
    
    // 审核状态映射（动态加载）
    auditStatusMap: {}
  },

  async onLoad(options) {
    // 根据传入的type参数设置标签页
    if (options.type) {
      const tabIndex = this.data.tabs.findIndex(tab => tab.key === options.type)
      if (tabIndex !== -1) {
        this.setData({ activeTab: tabIndex })
      }
    }
    
    // 加载审核状态映射
    await this.loadAuditStatusMap();
    
    this.loadSubmissionData()
  },

  async onShow() {
    // 页面显示时刷新数据
    this.loadSubmissionData()
  },

  // 加载审核状态映射
  async loadAuditStatusMap() {
    try {
      const auditStatusMap = await systemInfoService.getAuditStatusMap();
      this.setData({ auditStatusMap });
    } catch (error) {
      console.error('加载审核状态映射失败:', error);
      // 使用默认映射作为降级
      this.setData({
        auditStatusMap: {
          0: { text: '待审核', color: '#ff9500' },
          1: { text: '已通过', color: '#07c160' },
          2: { text: '已拒绝', color: '#ee0a24' }
        }
      });
    }
  },

  // 加载提交数据
  loadSubmissionData: function() {
    if (this.data.loading) return
    
    this.setData({ loading: true })
    
    const currentTab = this.data.tabs[this.data.activeTab]
    const apiMethod = currentTab.key === 'nearby' ? 
      api.getMySubmittedNearby : api.getMySubmittedPhones
    
    apiMethod({
      pageNum: this.data.pageNum,
      pageSize: this.data.pageSize
    }).then(res => {
      if (res.code === 200) {
        const newList = res.rows || []
        
        // 处理数据格式
        const processedList = newList.map(item => {
          return {
            ...item,
            statusInfo: this.data.auditStatusMap[item.auditStatus] || this.data.auditStatusMap[0],
            createTimeFormatted: this.formatTime(item.createTime),
            imagesArray: item.imageUrls || []
          }
        })
        
        this.setData({
          submissionList: this.data.pageNum === 1 ? processedList : 
            [...this.data.submissionList, ...processedList],
          total: res.total || 0,
          hasMore: processedList.length === this.data.pageSize,
          loading: false,
          refreshing: false
        })
      } else {
        throw new Error(res.msg || '加载失败')
      }
    }).catch(error => {
      console.error('加载提交数据失败:', error)
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      })
      this.setData({ 
        loading: false,
        refreshing: false 
      })
    })
  },

  // 格式化时间 - 使用统一的工具方法
  formatTime: function(dateStr) {
    return util.formatTimeUnified(dateStr, { 
      type: 'relative', 
      suffix: '提交' 
    });
  },

  // 切换标签页
  onTabChange: function(e) {
    const index = e.detail.index
    this.setData({
      activeTab: index,
      pageNum: 1,
      submissionList: [],
      hasMore: true
    })
    this.loadSubmissionData()
  },

  // 下拉刷新
  onRefresh: function() {
    this.setData({
      pageNum: 1,
      submissionList: [],
      hasMore: true,
      refreshing: true
    })
    this.loadSubmissionData()
  },

  // 加载更多
  onLoadMore: function() {
    if (!this.data.hasMore || this.data.loadingMore) return
    
    this.setData({
      pageNum: this.data.pageNum + 1,
      loadingMore: true
    })
    this.loadSubmissionData()
  },

  // 点击列表项
  onItemTap: function(e) {
    const item = e.currentTarget.dataset.item
    const currentTab = this.data.tabs[this.data.activeTab]
    
    if (currentTab.key === 'nearby') {
      // 查看周边服务详情
      wx.navigateTo({
        url: `/pages/community-service-detail/index?id=${item.id}`
      })
    } else {
      // 查看电话详情或直接拨打
      if (item.phone) {
        wx.showModal({
          title: '拨打电话',
          content: `确定要拨打 ${item.name} 的电话 ${item.phone} 吗？`,
          confirmText: '拨打',
          cancelText: '取消',
          success: (res) => {
            if (res.confirm) {
              wx.makePhoneCall({
                phoneNumber: item.phone
              })
            }
          }
        })
      }
    }
  },

  // 返回按钮
  onNavigateBack: function() {
    wx.navigateBack({
      delta: 1,
      fail: () => {
        wx.navigateTo({
          url: '/pages/community-service/index'
        })
      }
    })
  }
}) 