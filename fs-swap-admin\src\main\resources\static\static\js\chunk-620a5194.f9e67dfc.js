(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-620a5194"],{"18b1":function(e,t,n){"use strict";n("7b8c")},"7b8c":function(e,t,n){},"7db5":function(e,t,n){"use strict";n.d(t,"b",(function(){return r})),n.d(t,"a",(function(){return i})),n.d(t,"c",(function(){return o}));n("b0c0"),n("a9e3");var a=n("a04a");function r(e,t,n){if(!n)return"-";try{if(0===a["a"].getRegions().length)return"".concat(n);var r=a["a"].getRegionFullName(n);if(r)return r;var i=a["a"].getRegionById(n);return i?i.name:"".concat(n)}catch(o){return"".concat(n)}}function i(e,t,n){if(!n)return"-";try{if(0===a["a"].getCommunities().length)return"".concat(n);var r=a["a"].getCommunityById(n);return r?r.name:"".concat(n)}catch(i){return"".concat(n)}}function o(e,t,n){if(!n)return"-";try{if(0===a["a"].getResidentials().length)return"".concat(n);var r=a["a"].getResidentialById(n);return r?r.name:"".concat(n)}catch(i){return"".concat(n)}}},a04a:function(e,t,n){"use strict";var a=n("2909"),r=n("5530"),i=n("c7eb"),o=n("1da1"),s=n("d4ec"),l=n("bee2"),c=(n("99af"),n("4de4"),n("7db0"),n("a15b"),n("d81d"),n("fb6a"),n("b0c0"),n("e9c4"),n("4ec9"),n("a9e3"),n("b64b"),n("d3b7"),n("07ac"),n("25f0"),n("3ca3"),n("0643"),n("2382"),n("fffc"),n("4e3e"),n("a573"),n("159b"),n("ddb0"),n("8c69")),u=n("ea81"),d=n("0210"),m="1.0.0",h="area_data_cache_version",g={REGIONS:"cached_regions",COMMUNITIES:"cached_communities",RESIDENTIALS:"cached_residentials",TIMESTAMP:"area_data_timestamp"},p=36e5,f=function(){function e(){Object(s["a"])(this,e),this.loading={regions:!1,communities:!1,residentials:!1,all:!1},this.cache={regions:[],communities:[],residentials:[],regionMap:new Map,communityMap:new Map,residentialMap:new Map},this.loadPromises={regions:null,communities:null,residentials:null,all:null},this.init()}return Object(l["a"])(e,[{key:"init",value:function(){this.checkCacheVersion(),this.loadCachedData()}},{key:"checkCacheVersion",value:function(){var e=localStorage.getItem(h);e!==m&&(Object.values(g).forEach((function(e){localStorage.removeItem(e)})),localStorage.setItem(h,m))}},{key:"loadCachedData",value:function(){try{var e=localStorage.getItem(g.TIMESTAMP),t=Date.now();if(!e)return;var n=t-parseInt(e);if(console.log("缓存时间戳: ".concat(new Date(parseInt(e)).toLocaleString(),", 当前时间: ").concat(new Date(t).toLocaleString(),", 缓存年龄: ").concat(Math.floor(n/1e3/60)," 分钟")),n<p){console.log("缓存未过期，尝试加载缓存数据");var a=localStorage.getItem(g.REGIONS);if(a){var r=JSON.parse(a);r.length>0&&(this.cache.regions=r,this.cache.regionMap=new Map(r.map((function(e){return[e.id,e]}))))}else console.log("缓存中没有区域数据");var i=localStorage.getItem(g.COMMUNITIES);if(i){var o=JSON.parse(i);console.log("从缓存加载社区数据，数量: ".concat(o.length)),o.length>0&&(this.cache.communities=o,this.cache.communityMap=new Map(o.map((function(e){return[e.id,e]}))),console.log("社区数据加载成功，Map大小:",this.cache.communityMap.size))}else console.log("缓存中没有社区数据");var s=localStorage.getItem(g.RESIDENTIALS);if(s){var l=JSON.parse(s);console.log("从缓存加载小区数据，数量: ".concat(l.length)),l.length>0&&(this.cache.residentials=l,this.cache.residentialMap=new Map(l.map((function(e){return[e.id,e]}))),console.log("小区数据加载成功，Map大小:",this.cache.residentialMap.size))}else console.log("缓存中没有小区数据");console.log("缓存数据加载完成")}else console.log("缓存已过期 (".concat(Math.floor(n/1e3/60)," 分钟)，需要重新加载数据"))}catch(c){console.error("加载缓存数据失败:",c),this.clearCache()}}},{key:"clearCache",value:function(){Object.values(g).forEach((function(e){localStorage.removeItem(e)})),this.cache={regions:[],communities:[],residentials:[],regionMap:new Map,communityMap:new Map,residentialMap:new Map}}},{key:"saveToCache",value:function(){try{localStorage.setItem(g.TIMESTAMP,Date.now().toString()),this.cache.regions.length>0&&localStorage.setItem(g.REGIONS,JSON.stringify(this.cache.regions)),this.cache.communities.length>0&&localStorage.setItem(g.COMMUNITIES,JSON.stringify(this.cache.communities)),this.cache.residentials.length>0&&localStorage.setItem(g.RESIDENTIALS,JSON.stringify(this.cache.residentials))}catch(e){console.error("保存缓存数据失败:",e)}}},{key:"loadAllData",value:function(){var e=Object(o["a"])(Object(i["a"])().mark((function e(){var t,n,a,r,o=this,s=arguments;return Object(i["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t=s.length>0&&void 0!==s[0]&&s[0],console.log("开始加载所有区域数据, forceRefresh=".concat(t)),n=this.cache.regions.length>0,a=this.cache.communities.length>0,r=this.cache.residentials.length>0,console.log("当前缓存状态:",{hasRegions:n,hasCommunities:a,hasResidentials:r}),!this.loadPromises.all||t){e.next=9;break}return console.log("数据正在加载中，返回现有Promise"),e.abrupt("return",this.loadPromises.all);case 9:if(!(n&&a&&r)||t){e.next=12;break}return console.log("缓存中已有所有数据，跳过加载"),e.abrupt("return",Promise.resolve({regions:this.cache.regions,communities:this.cache.communities,residentials:this.cache.residentials}));case 12:return t&&(console.log("强制刷新，清除现有缓存"),this.clearCache()),this.loading.all=!0,console.log("开始从服务器加载数据..."),this.loadPromises.all=Promise.all([this.loadRegions(t),this.loadCommunities(t),this.loadResidentials(t)]).then((function(e){return console.log("所有数据加载完成:",{regions:e[0].length,communities:e[1].length,residentials:e[2].length}),{regions:e[0],communities:e[1],residentials:e[2]}})).finally((function(){o.loading.all=!1})),e.abrupt("return",this.loadPromises.all);case 17:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}()},{key:"loadRegions",value:function(){var e=Object(o["a"])(Object(i["a"])().mark((function e(){var t,n=this,a=arguments;return Object(i["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t=a.length>0&&void 0!==a[0]&&a[0],console.log("开始加载区域数据, forceRefresh=".concat(t,", 当前缓存数量=").concat(this.cache.regions.length)),!(this.cache.regions.length>0)||t){e.next=5;break}return console.log("缓存中已有区域数据，跳过加载"),e.abrupt("return",Promise.resolve(this.cache.regions));case 5:if(!this.loadPromises.regions||t){e.next=8;break}return console.log("区域数据正在加载中，返回现有Promise"),e.abrupt("return",this.loadPromises.regions);case 8:return this.loading.regions=!0,console.log("开始从服务器加载区域数据..."),this.loadPromises.regions=Object(c["d"])().then((function(e){console.log("区域数据API响应:",e?"成功":"失败");var t=[];return e.data&&Array.isArray(e.data)?(t=e.data,console.log("从response.data中提取区域数据")):e.rows&&Array.isArray(e.rows)?(t=e.rows,console.log("从response.rows中提取区域数据")):Array.isArray(e)?(t=e,console.log("响应本身是区域数据数组")):console.warn("无法从响应中提取区域数据:",e),console.log("加载区域数据成功，数量:",t.length),t.length>0&&console.log("区域数据示例:",t.slice(0,3)),t=t.map((function(e){return{id:Number(e.id),name:e.name,type:Number(e.type||0),pid:Number(e.pid||0),code:Number(e.code||0)}})),n.cache.regions=t,n.cache.regionMap=new Map(t.map((function(e){return[e.id,e]}))),console.log("区域Map创建完成，大小:",n.cache.regionMap.size),n.saveToCache(),console.log("区域数据已保存到localStorage"),t})).catch((function(e){throw console.error("加载区域数据失败:",e),e})).finally((function(){n.loading.regions=!1,console.log("区域数据加载完成")})),e.abrupt("return",this.loadPromises.regions);case 12:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}()},{key:"loadCommunities",value:function(){var e=Object(o["a"])(Object(i["a"])().mark((function e(){var t,n=this,a=arguments;return Object(i["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t=a.length>0&&void 0!==a[0]&&a[0],!(this.cache.communities.length>0)||t){e.next=3;break}return e.abrupt("return",Promise.resolve(this.cache.communities));case 3:if(!this.loadPromises.communities||t){e.next=5;break}return e.abrupt("return",this.loadPromises.communities);case 5:return this.loading.communities=!0,this.loadPromises.communities=Object(u["d"])().then((function(e){var t=[];return e.data&&Array.isArray(e.data)?t=e.data:e.rows&&Array.isArray(e.rows)?t=e.rows:Array.isArray(e)&&(t=e),console.log("加载社区数据成功，数量:",t.length),console.log("社区数据示例:",t.slice(0,3)),t=t.map((function(e){return{id:Number(e.id),name:e.name,regionId:Number(e.regionId||0),code:e.code,status:Number(e.status||0)}})),n.cache.communities=t,n.cache.communityMap=new Map(t.map((function(e){return[e.id,e]}))),console.log("社区Map创建完成，大小:",n.cache.communityMap.size),n.saveToCache(),t})).catch((function(e){throw console.error("加载社区数据失败:",e),e})).finally((function(){n.loading.communities=!1})),e.abrupt("return",this.loadPromises.communities);case 8:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}()},{key:"loadResidentials",value:function(){var e=Object(o["a"])(Object(i["a"])().mark((function e(){var t,n=this,a=arguments;return Object(i["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t=a.length>0&&void 0!==a[0]&&a[0],!(this.cache.residentials.length>0)||t){e.next=3;break}return e.abrupt("return",Promise.resolve(this.cache.residentials));case 3:if(!this.loadPromises.residentials||t){e.next=5;break}return e.abrupt("return",this.loadPromises.residentials);case 5:return this.loading.residentials=!0,this.loadPromises.residentials=Object(d["d"])().then((function(e){var t=[];return e.data&&Array.isArray(e.data)?t=e.data:e.rows&&Array.isArray(e.rows)?t=e.rows:Array.isArray(e)&&(t=e),console.log("加载小区数据成功，数量:",t.length),console.log("小区数据示例:",t.slice(0,3)),t=t.map((function(e){return{id:Number(e.id),name:e.name,communityId:Number(e.communityId||0),regionId:Number(e.regionId||0),address:e.address,status:Number(e.status||0)}})),n.cache.residentials=t,n.cache.residentialMap=new Map(t.map((function(e){return[e.id,e]}))),console.log("小区Map创建完成，大小:",n.cache.residentialMap.size),n.saveToCache(),t})).catch((function(e){throw console.error("加载小区数据失败:",e),e})).finally((function(){n.loading.residentials=!1})),e.abrupt("return",this.loadPromises.residentials);case 8:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}()},{key:"getCommunityListByRegionId",value:function(e){return e?this.cache.communities.filter((function(t){return t.regionId===Number(e)})):[]}},{key:"getResidentialListByCommunityId",value:function(e){return e?this.cache.residentials.filter((function(t){return t.communityId===Number(e)})):[]}},{key:"getRegions",value:function(){return this.cache.regions}},{key:"getCommunities",value:function(){return this.cache.communities}},{key:"getResidentials",value:function(){return this.cache.residentials}},{key:"getLoadingStatus",value:function(){return Object(r["a"])({},this.loading)}},{key:"getRegionById",value:function(e){if(!e)return null;var t=Number(e),n=this.cache.regionMap.get(t);if(!n){console.warn("未找到ID为".concat(t,"的区域信息，当前区域Map大小:"),this.cache.regionMap.size);var a=this.cache.regions.find((function(e){return e.id===t}));if(a)return console.log("从数组中找到ID为".concat(t,"的区域信息:"),a),a}return n||null}},{key:"getRegionPath",value:function(e){if(!e)return[];var t=this.getRegionById(e);if(!t)return console.warn("获取区域路径失败: 未找到ID为".concat(e,"的区域")),[];var n=[t];if(t.pid&&0!==t.pid){var r=this.getRegionPath(t.pid);return[].concat(Object(a["a"])(r),[t])}return n}},{key:"getRegionFullName",value:function(e){if(!e)return"";var t=this.getRegionPath(e);if(!t.length)return console.warn("获取区域完整路径名称失败: 路径为空"),"";var n=t.map((function(e){return e.name})).join("/");return n}},{key:"getCommunityById",value:function(e){if(!e)return null;var t=Number(e),n=this.cache.communityMap.get(t);if(!n){console.warn("未找到ID为".concat(t,"的社区信息，当前社区Map大小:"),this.cache.communityMap.size);var a=this.cache.communities.find((function(e){return e.id===t}));if(a)return console.log("从数组中找到ID为".concat(t,"的社区信息:"),a),a}return n||null}},{key:"getResidentialById",value:function(e){if(!e)return null;var t=Number(e),n=this.cache.residentialMap.get(t);if(!n){var a=this.cache.residentials.find((function(e){return e.id===t}));if(a)return a}return n||null}}])}(),y=new f;t["a"]=y},bd7d:function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"app-container"},[n("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",attrs:{model:e.queryParams,size:"small",inline:!0,"label-width":"68px"}},[n("el-form-item",{attrs:{label:"行政区域",prop:"regionId"}},[n("region-chain-selector",{ref:"queryRegionSelector",attrs:{regionId:e.queryParams.regionId,placeholder:"请选择所属行政区域"},on:{"update:regionId":function(t){return e.queryParams.regionId=t},change:e.handleQuery}})],1),n("el-form-item",{attrs:{label:"社区名称",prop:"name"}},[n("el-input",{attrs:{placeholder:"请输入社区名称",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.name,callback:function(t){e.$set(e.queryParams,"name",t)},expression:"queryParams.name"}})],1),n("el-form-item",{attrs:{label:"社区编码",prop:"code"}},[n("el-input",{attrs:{placeholder:"请输入社区编码",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.code,callback:function(t){e.$set(e.queryParams,"code",t)},expression:"queryParams.code"}})],1),n("el-form-item",{attrs:{label:"负责人",prop:"manager"}},[n("el-input",{attrs:{placeholder:"请输入负责人姓名",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.manager,callback:function(t){e.$set(e.queryParams,"manager",t)},expression:"queryParams.manager"}})],1),n("el-form-item",{attrs:{label:"联系电话",prop:"phone"}},[n("el-input",{attrs:{placeholder:"请输入联系电话",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.phone,callback:function(t){e.$set(e.queryParams,"phone",t)},expression:"queryParams.phone"}})],1),n("el-form-item",{attrs:{label:"状态",prop:"deleted"}},[n("el-select",{attrs:{placeholder:"请选择状态",clearable:""},model:{value:e.queryParams.deleted,callback:function(t){e.$set(e.queryParams,"deleted",t)},expression:"queryParams.deleted"}},[n("el-option",{attrs:{label:"正常",value:"0"}}),n("el-option",{attrs:{label:"已删除",value:"1"}})],1)],1),n("el-form-item",[n("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),n("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1),n("el-row",{staticClass:"mb8",attrs:{gutter:10}},[n("el-col",{attrs:{span:1.5}},[n("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:community:add"],expression:"['system:community:add']"}],attrs:{type:"primary",plain:"",icon:"el-icon-plus",size:"mini"},on:{click:e.handleAdd}},[e._v("新增社区")])],1),n("el-col",{attrs:{span:1.5}},[n("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:community:edit"],expression:"['system:community:edit']"}],attrs:{type:"success",plain:"",icon:"el-icon-edit",size:"mini",disabled:e.single},on:{click:e.handleUpdate}},[e._v("编辑")])],1),n("el-col",{attrs:{span:1.5}},[n("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:community:remove"],expression:"['system:community:remove']"}],attrs:{type:"danger",plain:"",icon:"el-icon-delete",size:"mini",disabled:e.multiple},on:{click:e.handleDelete}},[e._v("删除")])],1),n("el-col",{attrs:{span:1.5}},[n("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:community:export"],expression:"['system:community:export']"}],attrs:{type:"warning",plain:"",icon:"el-icon-download",size:"mini"},on:{click:e.handleExport}},[e._v("导出")])],1),n("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1),n("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.communityList},on:{"selection-change":e.handleSelectionChange}},[n("el-table-column",{attrs:{type:"selection",width:"55",align:"center"}}),n("el-table-column",{attrs:{label:"id",align:"center",prop:"id"}}),n("el-table-column",{attrs:{label:"行政区域",align:"center",prop:"regionId",formatter:e.regionFormatter}}),n("el-table-column",{attrs:{label:"社区名称",align:"center",prop:"name"}}),n("el-table-column",{attrs:{label:"社区编码",align:"center",prop:"code"}}),n("el-table-column",{attrs:{label:"负责人",align:"center",prop:"manager"}}),n("el-table-column",{attrs:{label:"联系电话",align:"center",prop:"phone"}}),n("el-table-column",{attrs:{label:"状态",align:"center",prop:"status"},scopedSlots:e._u([{key:"default",fn:function(t){return[1===t.row.status?n("el-tag",{attrs:{type:"success"}},[e._v("正常")]):n("el-tag",{attrs:{type:"info"}},[e._v("停用")])]}}])}),n("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:community:edit"],expression:"['system:community:edit']"}],attrs:{size:"mini",type:"text",icon:"el-icon-edit"},on:{click:function(n){return e.handleUpdate(t.row)}}},[e._v("编辑")]),n("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:community:remove"],expression:"['system:community:remove']"}],attrs:{size:"mini",type:"text",icon:"el-icon-delete"},on:{click:function(n){return e.handleDelete(t.row)}}},[e._v("删除")])]}}])})],1),n("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}}),n("el-dialog",{attrs:{title:e.title,visible:e.open,width:"500px","append-to-body":""},on:{"update:visible":function(t){e.open=t}}},[n("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"80px"}},[n("el-form-item",{attrs:{label:"行政区域",prop:"regionId"}},[n("region-chain-selector",{ref:"formRegionSelector",attrs:{regionId:e.form.regionId,placeholder:"请选择所属行政区域"},on:{"update:regionId":function(t){return e.form.regionId=t}}})],1),n("el-form-item",{attrs:{label:"社区名称",prop:"name"}},[n("el-input",{attrs:{placeholder:"请输入社区名称"},model:{value:e.form.name,callback:function(t){e.$set(e.form,"name",t)},expression:"form.name"}})],1),n("el-form-item",{attrs:{label:"社区编码",prop:"code"}},[n("el-input",{attrs:{placeholder:"请输入社区编码"},model:{value:e.form.code,callback:function(t){e.$set(e.form,"code",t)},expression:"form.code"}})],1),n("el-form-item",{attrs:{label:"负责人",prop:"manager"}},[n("el-input",{attrs:{placeholder:"请输入负责人姓名"},model:{value:e.form.manager,callback:function(t){e.$set(e.form,"manager",t)},expression:"form.manager"}})],1),n("el-form-item",{attrs:{label:"联系电话",prop:"phone"}},[n("el-input",{attrs:{placeholder:"请输入联系电话"},model:{value:e.form.phone,callback:function(t){e.$set(e.form,"phone",t)},expression:"form.phone"}})],1),n("el-form-item",{attrs:{label:"状态",prop:"status"}},[n("el-radio-group",{model:{value:e.form.status,callback:function(t){e.$set(e.form,"status",t)},expression:"form.status"}},[n("el-radio",{attrs:{label:1}},[e._v("正常")]),n("el-radio",{attrs:{label:0}},[e._v("停用")])],1)],1)],1),n("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[n("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),n("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1)],1)},r=[],i=n("5530"),o=(n("d81d"),n("a9e3"),n("d3b7"),n("0643"),n("a573"),n("ea81")),s=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"region-chain-selector"},[n("el-form-item",{attrs:{label:e.label,prop:e.prop}},[n("el-cascader",{attrs:{options:e.regionOptions,props:e.cascaderProps,placeholder:e.placeholder,disabled:e.disabled,clearable:!0,filterable:!0,"show-all-levels":!0,loading:e.loading},on:{change:e.handleRegionChange},model:{value:e.selectedRegionPath,callback:function(t){e.selectedRegionPath=t},expression:"selectedRegionPath"}},[n("template",{slot:"empty"},[e.loading?n("div",{staticClass:"el-cascader-menu__empty-text"},[n("i",{staticClass:"el-icon-loading"}),e._v(" 加载中... ")]):n("div",{staticClass:"el-cascader-menu__empty-text"},[e._v(" 暂无数据 ")])])],2)],1),e.error?n("div",{staticClass:"region-selector-error"},[n("el-alert",{attrs:{title:e.error,type:"error","show-icon":"",closable:!1}})],1):e._e(),e.showRefreshButton?n("div",{staticClass:"region-selector-refresh"},[n("el-button",{attrs:{type:"text",size:"mini",icon:"el-icon-refresh",loading:e.loading},on:{click:e.refreshData}},[e._v(" 刷新数据 ")])],1):e._e()],1)},l=[],c=n("c7eb"),u=n("1da1"),d=(n("4de4"),n("7db0"),n("b0c0"),n("2382"),n("fffc"),n("a04a")),m={name:"RegionChainSelector",props:{regionId:{type:[Number,String],default:null},prop:{type:String,default:"regionId"},label:{type:String,default:"行政区域"},placeholder:{type:String,default:"请选择行政区域"},disabled:{type:Boolean,default:!1},isLoading:{type:Boolean,default:!1},showRefreshButton:{type:Boolean,default:!0},maxLevel:{type:Number,default:4}},data:function(){return{selectedRegionPath:[],loading:!1,error:null,internalLoading:!1,regionList:[],cascaderProps:{value:"id",label:"name",children:"children",checkStrictly:!1,emitPath:!0,expandTrigger:"click",lazy:!1,multiple:!1}}},computed:{regionOptions:function(){var e=this;if(!this.regionList||0===this.regionList.length)return[];var t=this.regionList.filter((function(e){return 1===e.type})).map((function(t){var n=e.regionList.filter((function(e){return 2===e.type&&e.pid===t.id})).map((function(t){var n=e.regionList.filter((function(e){return 3===e.type&&e.pid===t.id})).map((function(t){if(3===e.maxLevel)return{id:t.id,name:t.name,leaf:!0};var n=e.regionList.filter((function(e){return 4===e.type&&e.pid===t.id})).map((function(e){return{id:e.id,name:e.name,leaf:!0}}));return{id:t.id,name:t.name,children:n.length>0?n:null,leaf:0===n.length}}));return{id:t.id,name:t.name,children:n.length>0?n:null,leaf:0===n.length}}));return{id:t.id,name:t.name,children:n.length>0?n:null,leaf:0===n.length}}));return t}},watch:{regionId:function(e){this.isLoading||e===this.getSelectedRegionId()||this.initFromRegionId(e)},selectedRegionPath:function(e){this.internalLoading||this.updateRegionId()}},created:function(){this.loadData()},methods:{getSelectedRegionId:function(){return this.selectedRegionPath&&0!==this.selectedRegionPath.length?this.selectedRegionPath[this.selectedRegionPath.length-1]:null},updateRegionId:function(){var e=this.getSelectedRegionId();this.$emit("update:regionId",e),this.$emit("change",e)},handleRegionChange:function(){this.isLoading},initFromRegionId:function(e){var t=this;return Object(u["a"])(Object(c["a"])().mark((function n(){var a,r,i,o;return Object(c["a"])().wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(e){n.next=3;break}return t.selectedRegionPath=[],n.abrupt("return");case 3:if(n.prev=3,t.internalLoading=!0,0!==t.regionList.length){n.next=8;break}return n.next=8,t.loadData(!1);case 8:if(a=Number(e),r=d["a"].getRegionPath(a),r&&0!==r.length){n.next=16;break}if(i=t.regionList.find((function(e){return e.id===a})),!i){n.next=15;break}return t.selectedRegionPath=[i.id],n.abrupt("return");case 15:return n.abrupt("return");case 16:o=r.map((function(e){return e.id})),t.selectedRegionPath=o,n.next=22;break;case 20:n.prev=20,n.t0=n["catch"](3);case 22:return n.prev=22,setTimeout((function(){t.internalLoading=!1}),300),n.finish(22);case 25:case"end":return n.stop()}}),n,null,[[3,20,22,25]])})))()},loadData:function(){var e=arguments,t=this;return Object(u["a"])(Object(c["a"])().mark((function n(){var a;return Object(c["a"])().wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(a=e.length>0&&void 0!==e[0]&&e[0],!t.loading||a){n.next=3;break}return n.abrupt("return");case 3:return t.loading=!0,t.error=null,n.prev=5,n.next=8,d["a"].loadRegions(a);case 8:return t.regionList=d["a"].getRegions(),n.abrupt("return",!0);case 12:return n.prev=12,n.t0=n["catch"](5),t.error="加载区域数据失败，请稍后重试",n.abrupt("return",!1);case 16:return n.prev=16,t.loading=!1,n.finish(16);case 19:case"end":return n.stop()}}),n,null,[[5,12,16,19]])})))()},refreshData:function(){var e=this;return Object(u["a"])(Object(c["a"])().mark((function t(){return Object(c["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.abrupt("return",e.loadData(!0));case 1:case"end":return t.stop()}}),t)})))()},forceUpdateValue:function(e){var t=this;this.internalLoading=!0,this.selectedRegionPath=e,setTimeout((function(){t.internalLoading=!1}),300)}}},h=m,g=(n("18b1"),n("2877")),p=Object(g["a"])(h,s,l,!1,null,"f9a5b5e6",null),f=p.exports,y=n("7db5"),b={name:"Community",components:{RegionChainSelector:f},data:function(){return{loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,communityList:[],title:"",open:!1,queryParams:{pageNum:1,pageSize:10,regionId:null,name:null,code:null,manager:null,phone:null,status:null,deleted:null},form:{},rules:{regionId:[{required:!0,message:"所属区域不能为空",trigger:"change"}],name:[{required:!0,message:"社区名称不能为空",trigger:"blur"}],status:[{required:!0,message:"状态不能为空",trigger:"change"}],createBy:[{required:!0,message:"创建人不能为空",trigger:"blur"}]}}},created:function(){this.getList()},watch:{"queryParams.regionId":{handler:function(e){console.log("regionId 变化:",e),console.log("queryParams:",this.queryParams)}}},computed:{},methods:{getList:function(){var e=this;this.loading=!0,Object(o["e"])(this.queryParams).then((function(t){e.communityList=t.rows,e.total=t.total,e.loading=!1}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={id:null,regionId:null,name:null,code:null,manager:null,phone:null,status:1,createTime:null,updateTime:null,createBy:null,updateBy:null,deleted:0},this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,console.log("handleQuery 开始，当前 regionId:",this.queryParams.regionId),this.queryParams.regionId&&(this.queryParams.regionId=parseInt(this.queryParams.regionId),console.log("转换后的 regionId:",this.queryParams.regionId)),console.log("查询参数:",this.queryParams),this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.queryParams.regionId=null,this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.id})),this.single=1!==e.length,this.multiple=!e.length},handleAdd:function(){this.reset(),this.open=!0,this.title="新增社区"},handleUpdate:function(e){var t=this;this.reset();var n=e.id||this.ids;Object(o["c"])(n).then((function(e){t.form=e.data,t.form.regionId&&(t.form.regionId=Number(t.form.regionId)),t.open=!0,t.title="编辑社区",t.$nextTick((function(){t.$refs.formRegionSelector&&t.$refs.formRegionSelector.initFromRegionId(t.form.regionId)}))})).catch((function(e){console.error("加载社区数据失败:",e),t.$message.error("加载社区数据失败，请稍后重试")}))},submitForm:function(){var e=this;this.$refs["form"].validate((function(t){t&&(null!=e.form.id?Object(o["f"])(e.form).then((function(){e.$modal.msgSuccess("修改成功"),e.open=!1,e.getList()})):Object(o["a"])(e.form).then((function(){e.$modal.msgSuccess("新增成功"),e.open=!1,e.getList()})))}))},handleDelete:function(e){var t=this,n=e.id||this.ids;this.$modal.confirm("是否确认删除所选社区？").then((function(){return Object(o["b"])(n)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleExport:function(){this.download("system/community/export",Object(i["a"])({},this.queryParams),"社区信息_".concat((new Date).getTime(),".xlsx"))},regionFormatter:y["b"]}},v=b,w=Object(g["a"])(v,a,r,!1,null,null,null);t["default"]=w.exports}}]);