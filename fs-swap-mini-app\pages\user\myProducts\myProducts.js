// pages/user/myProducts/myProducts.js
const app = getApp();
const api = require("../../../config/api.js");
const systemInfoService = require('../../../services/systemInfo.js');
const util = require("../../../utils/util");

Page({
  data: {
    // 一级tab控制
    mainActiveTab: 0, // 0: 商品, 1: 邻里互助
    mainTabs: ['商品', '邻里互助'],

    // 二级tab控制
    subActiveTab: 0, // 0: 在售/发布中, 1: 已下架

    // 商品相关数据
    productList: [],
    productLoading: false,
    productPage: 1,
    productHasMore: true,

    // 邻里互助相关数据
    helpList: [],
    helpLoading: false,
    helpPage: 1,
    helpHasMore: true,

    // 分类数据（用于类别名称转义）
    categories: [],

    // 共用配置
    limit: 20,
    // 文件服务器URL
    fileUrl: '',
    // 状态映射（与后端 ProductStatus 枚举对应）
    statusMap: {
      '1': '在售/发布中', // ON_SALE
      '2': '已售/已完成', // SOLD
      '3': '下架' // OFF_SHELF
    },

    // 操作相关
    showActionDialog: false,
    currentItem: null, // 当前操作的商品或互助
    currentAction: '',
    confirmText: '',
    operating: false,
    currentType: '' // 'product' 或 'help'
  },

  async onLoad() {
    // 获取文件服务器URL
    const systemInfo = wx.getStorageSync('systemInfo') || {};
    this.setData({
      fileUrl: systemInfo.fileUrl || ''
    });

    // 加载分类数据
    await this.loadCategories();

    // 默认加载商品列表
    this.loadCurrentData();
  },

  onShow() {
    // 刷新当前数据
    this.refreshCurrentData();
  },

  // 一级tab切换
  onMainTabChange(e) {
    const mainActiveTab = parseInt(e.currentTarget.dataset.index);
    this.setData({
      mainActiveTab,
      subActiveTab: 0 // 重置二级tab
    });

    // 重置并加载对应的数据
    this.refreshCurrentData();
  },

  // 二级tab切换
  onSubTabChange(e) {
    const subActiveTab = parseInt(e.currentTarget.dataset.index);
    this.setData({
      subActiveTab
    });

    // 重置并加载对应的数据
    this.refreshCurrentData();
  },

  // 加载当前数据
  loadCurrentData() {
    if (this.data.mainActiveTab === 0) {
      // 商品
      this.loadProducts();
    } else {
      // 邻里互助
      this.loadHelps();
    }
  },

  // 刷新当前数据
  refreshCurrentData() {
    if (this.data.mainActiveTab === 0) {
      this.refreshProducts();
    } else {
      this.refreshHelps();
    }
  },

  // 加载商品列表
  async loadProducts() {
    if (this.data.productLoading || !this.data.productHasMore) return;

    this.setData({ productLoading: true });

    try {
      const status = this.data.subActiveTab === 0 ? 1 : 3; // 1: 在售, 3: 已下架
      const res = await api.getMyProducts({
        page: this.data.productPage,
        limit: this.data.limit,
        status: status
      });

      if (res.code === 200) {
        let productList = res.rows || [];

        // 使用系统信息服务处理商品图片
        productList = await Promise.all(productList.map(async item => {
          if (item.images && typeof item.images === 'string') {
            const imageArray = item.images.split(',');
            if (imageArray.length > 0 && imageArray[0].trim()) {
              item.productImageUrl = await systemInfoService.processImageUrl(imageArray[0].trim());
            }
          }
          return item;
        }));

        // 判断是否是首页加载还是加载更多
        if (this.data.productPage === 1) {
          this.setData({
            productList: productList
          });
        } else {
          this.setData({
            productList: this.data.productList.concat(productList)
          });
        }

        // 判断是否还有更多数据
        this.setData({
          productHasMore: productList.length >= this.data.limit,
          productPage: this.data.productPage + 1
        });
      } else {
        wx.showToast({
          title: res.msg || '加载失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('加载商品失败:', error);
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      });
    } finally {
      this.setData({ productLoading: false });
    }
  },

  // 加载分类数据
  async loadCategories() {
    try {
      // 使用统一的系统信息服务获取分类数据
      const categories = await systemInfoService.getCommunityHelpRequireCategories();
      if (categories && categories.length > 0) {
        const categoryList = categories.map(item => ({
          id: item.dictValue,
          name: item.dictLabel
        }));
        this.setData({
          categories: categoryList
        });
      }
    } catch (error) {
      console.error('加载分类失败:', error);
    }
  },

  // 加载邻里互助列表
  async loadHelps() {
    if (this.data.helpLoading || !this.data.helpHasMore) return;

    this.setData({ helpLoading: true });

    try {
      const status = this.data.subActiveTab === 0 ? '1' : '3'; // 1: 发布中, 3: 已下架
      const res = await api.getMyCommunityHelp({
        page: this.data.helpPage,
        limit: this.data.limit,
        status: status
      });

      if (res.code === 200) {
        let helpList = res.rows || [];

        // 处理图片URL和类别名称转义
        helpList = await Promise.all(helpList.map(async item => {
          if (item.firstImage) {
            item.firstImage = await systemInfoService.processImageUrl(item.firstImage);
          }

          // 获取分类名称
          const category = this.data.categories.find(cat => cat.id === item.category);
          item.categoryName = category ? category.name : (item.category || '其他');

          return item;
        }));

        // 判断是否是首页加载还是加载更多
        if (this.data.helpPage === 1) {
          this.setData({
            helpList: helpList
          });
        } else {
          this.setData({
            helpList: this.data.helpList.concat(helpList)
          });
        }

        // 判断是否还有更多数据
        this.setData({
          helpHasMore: helpList.length >= this.data.limit,
          helpPage: this.data.helpPage + 1
        });
      } else {
        wx.showToast({
          title: res.msg || '加载失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('加载邻里互助失败:', error);
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      });
    } finally {
      this.setData({ helpLoading: false });
    }
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.refreshCurrentData();
    wx.stopPullDownRefresh();
  },

  // 触底加载更多
  onReachBottom() {
    if (this.data.mainActiveTab === 0) {
      // 商品
      if (this.data.productHasMore && !this.data.productLoading) {
        this.loadProducts();
      }
    } else {
      // 邻里互助
      if (this.data.helpHasMore && !this.data.helpLoading) {
        this.loadHelps();
      }
    }
  },

  // 刷新商品列表
  refreshProducts() {
    this.setData({
      productList: [],
      productPage: 1,
      productHasMore: true
    });
    this.loadProducts();
  },

  // 刷新邻里互助列表
  refreshHelps() {
    this.setData({
      helpList: [],
      helpPage: 1,
      helpHasMore: true
    });
    this.loadHelps();
  },

  // 点击商品项
  onProductTap(e) {
    const productId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: '/pages/item/detail?id=' + productId
    });
  },

  // 点击邻里互助项
  onHelpTap(e) {
    const helpId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: '/pages/community-help-detail/index?id=' + helpId
    });
  },

  // 显示商品操作菜单
  showProductActionMenu(e) {
    const product = e.currentTarget.dataset.product;
    const status = product.status;

    let actions = [];

    if (status === '1') { // 在售
      actions = [
        { name: '编辑', type: 'edit' },
        { name: '下架', type: 'offShelf' }
      ];
    } else if (status === '3') { // 已下架
      actions = [
        { name: '重新上架', type: 'onShelf' },
        { name: '删除', type: 'delete' }
      ];
    }

    if (actions.length === 0) return;

    wx.showActionSheet({
      itemList: actions.map(a => a.name),
      success: (res) => {
        const actionType = actions[res.tapIndex].type;
        this.handleProductAction(product, actionType);
      }
    });
  },

  // 显示邻里互助操作菜单
  showHelpActionMenu(e) {
    const help = e.currentTarget.dataset.help;
    const status = help.status;

    let actions = [];

    if (status === '1') { // 发布中
      actions = [
        { name: '编辑', type: 'edit' },
        { name: '下架', type: 'offShelf' }
      ];
    } else if (status === '3') { // 已下架
      actions = [
        { name: '重新上架', type: 'onShelf' },
        { name: '删除', type: 'delete' }
      ];
    }

    if (actions.length === 0) return;

    wx.showActionSheet({
      itemList: actions.map(a => a.name),
      success: (res) => {
        const actionType = actions[res.tapIndex].type;
        this.handleHelpAction(help, actionType);
      }
    });
  },

  // 处理商品操作
  handleProductAction(product, actionType) {
    if (actionType === 'edit') {
      // 跳转到编辑页面
      wx.navigateTo({
        url: `/pages/idle-publish/index?id=${product.id}`
      });
      return;
    }

    let confirmText = '';
    switch (actionType) {
      case 'offShelf':
        confirmText = '确定要下架该商品吗？';
        break;
      case 'onShelf':
        confirmText = '确定要重新上架该商品吗？';
        break;
      case 'delete':
        confirmText = '确定要删除该商品吗？删除后无法恢复。';
        break;
    }

    this.setData({
      showActionDialog: true,
      currentItem: product,
      currentAction: actionType,
      currentType: 'product',
      confirmText
    });
  },

  // 处理邻里互助操作
  handleHelpAction(help, actionType) {
    if (actionType === 'edit') {
      // 跳转到编辑页面
      wx.navigateTo({
        url: `/pages/community-help-publish/index?id=${help.id}&mode=edit`
      });
      return;
    }

    let confirmText = '';
    switch (actionType) {
      case 'offShelf':
        confirmText = '确定要下架该互助吗？';
        break;
      case 'onShelf':
        confirmText = '确定要重新上架该互助吗？';
        break;
      case 'delete':
        confirmText = '确定要删除该互助吗？删除后无法恢复。';
        break;
    }

    this.setData({
      showActionDialog: true,
      currentItem: help,
      currentAction: actionType,
      currentType: 'help',
      confirmText
    });
  },

  // 取消操作
  cancelAction() {
    this.setData({
      showActionDialog: false,
      currentItem: null,
      currentAction: '',
      currentType: '',
      confirmText: ''
    });
  },

  // 确认操作
  async confirmAction() {
    if (this.data.operating) return;

    this.setData({ operating: true });

    const { currentItem, currentAction, currentType } = this.data;

    try {
      let res;

      if (currentType === 'product') {
        // 商品操作
        switch (currentAction) {
          case 'offShelf':
            res = await api.updateProductStatus(currentItem.id, '3'); // 下架
            break;
          case 'onShelf':
            res = await api.updateProductStatus(currentItem.id, '1'); // 上架
            break;
          case 'delete':
            res = await api.deleteProduct(currentItem.id);
            break;
        }
      } else if (currentType === 'help') {
        // 邻里互助操作
        switch (currentAction) {
          case 'offShelf':
            res = await api.updateCommunityHelpStatus(currentItem.id, '3'); // 下架
            break;
          case 'onShelf':
            res = await api.updateCommunityHelpStatus(currentItem.id, '1'); // 上架
            break;
          case 'delete':
            res = await api.deleteCommunityHelp(currentItem.id);
            break;
        }
      }

      if (res && res.code === 200) {
        wx.showToast({
          title: '操作成功',
          icon: 'success'
        });

        // 刷新列表
        this.refreshCurrentData();
      } else {
        throw new Error(res?.msg || '操作失败');
      }
    } catch (error) {
      console.error('操作失败:', error);
      wx.showToast({
        title: error.message || '操作失败，请重试',
        icon: 'none'
      });
    } finally {
      this.setData({
        operating: false,
        showActionDialog: false,
        currentItem: null,
        currentAction: '',
        currentType: '',
        confirmText: ''
      });
    }
  }
});
